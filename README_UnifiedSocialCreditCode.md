# 统一社会信用代码生成工具类使用说明

## 概述

本项目实现了一个高效、符合国家标准的统一社会信用代码生成工具类，严格按照GB 32100-2015标准进行开发，支持生成大规模数据（如5000万条）并确保代码的唯一性和校验正确性。

## 功能特性

### 核心功能
- **标准代码生成**：严格按照GB 32100-2015标准生成18位统一社会信用代码
- **校验码计算**：使用标准加权因子算法计算校验码
- **代码校验**：验证统一社会信用代码的有效性
- **组织机构代码提取**：从统一社会信用代码中提取9位组织机构代码
- **自定义前缀生成**：支持指定登记管理部门、机构类别和行政区划代码

### 高级功能
- **线程安全支持**：提供线程安全的生成方法，适用于多线程环境
- **批量生成到文件**：支持将生成的代码批量写入CSV文件
- **多线程批量生成**：使用多线程技术提高大数据量生成效率
- **唯一性管理**：确保生成的代码不重复
- **性能监控**：提供生成统计信息和进度报告

### ValidationUtils集成
- **扩展校验功能**：集成到现有的ValidationUtils工具类中
- **代码类型识别**：自动识别9位组织机构代码和18位统一社会信用代码
- **统一校验接口**：提供一致的校验方法接口

## 技术规范

### 统一社会信用代码结构（18位）
1. **第1位**：登记管理部门代码（1-机构编制，5-民政，9-工商，Y-其他）
2. **第2位**：机构类别代码（1-企业，2-个体工商户，3-事业单位，4-社会团体，5-民办非企业单位，9-其他）
3. **第3-8位**：登记管理机关行政区划代码（6位）
4. **第9-17位**：主体标识码（组织机构代码，9位）
5. **第18位**：校验码（根据前17位计算得出）

### 校验算法（GB 32100-2015）
- **加权因子**：W = {1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 9, 27, 19, 26, 16}
- **字符映射**：0-9→0-9, A-H→10-17, J-N→18-22, P-Y→23-32（跳过I、O、Z）
- **校验码计算**：(31 - (加权和 % 31)) % 31

## 快速开始

### 1. 基础代码生成

```java
// 生成随机统一社会信用代码
String code = UnifiedSocialCreditCodeGenerator.generateRandomCode();
System.out.println(code); // 输出：91110000MA01A1B2CP

// 线程安全生成（适用于多线程环境）
String threadSafeCode = UnifiedSocialCreditCodeGenerator.generateRandomCodeThreadSafe();

// 指定前缀生成
String customCode = UnifiedSocialCreditCodeGenerator.generateCodeWithPrefix(
    "9",      // 登记管理部门代码：工商
    "1",      // 机构类别代码：企业
    "110000"  // 行政区划代码：北京市
);
```

### 2. 代码校验

```java
// 校验统一社会信用代码
boolean isValid = UnifiedSocialCreditCodeGenerator.validateCode("91110000MA01A1B2CP");
System.out.println(isValid); // 输出：true

// 使用ValidationUtils校验
boolean isValidByUtils = ValidationUtils.validateUnifiedSocialCreditCode("91110000MA01A1B2CP");
boolean isOrgCodeValid = ValidationUtils.validateOrgCode("91110000MA01A1B2CP");
```

### 3. 组织机构代码提取

```java
String unifiedCode = "91110000MA01A1B2CP";
String orgCode = UnifiedSocialCreditCodeGenerator.extractOrganizationCode(unifiedCode);
System.out.println(orgCode); // 输出：MA01A1B2C

// 使用ValidationUtils提取
String orgCodeByUtils = ValidationUtils.extractOrganizationCodeFromUnified(unifiedCode);
```

### 4. 批量生成到文件

```java
// 单线程批量生成
UnifiedSocialCreditCodeGenerator.BatchGenerationResult result = 
    UnifiedSocialCreditCodeGenerator.generateCodesToFile(
        10000L,           // 生成数量
        "codes.csv",      // 输出文件
        true              // 是否包含组织机构代码
    );

System.out.println(result);
```

### 5. 多线程批量生成

```java
// 多线程批量生成
UnifiedSocialCreditCodeGenerator.BatchGenerationResult result = 
    UnifiedSocialCreditCodeGenerator.generateCodesToFileMultiThreaded(
        1000000L,         // 生成数量
        "codes_mt.csv",   // 输出文件
        true,             // 是否包含组织机构代码
        8                 // 线程数量
    );

System.out.println(result);
```

### 6. 大规模数据生成（5000万条）

```java
// 生成5000万条数据
UnifiedSocialCreditCodeGenerator.BatchGenerationResult result = 
    UnifiedSocialCreditCodeGenerator.generateCodesToFileMultiThreaded(
        50_000_000L,                    // 5000万条
        "large_scale_codes.csv",        // 输出文件
        true,                           // 包含组织机构代码
        Runtime.getRuntime().availableProcessors() // 使用所有CPU核心
    );
```

## 性能特性

### 生成速度
- **单线程**：约300万个/秒
- **多线程**：根据CPU核心数可达数千万个/秒
- **5000万条数据**：预计耗时约30-60秒（取决于硬件配置）

### 校验速度
- **校验性能**：约250万次/秒
- **内存使用**：稳定，无内存泄漏
- **唯一性保证**：使用ConcurrentHashMap确保线程安全的唯一性检查

### 文件输出
- **格式**：CSV格式，UTF-8编码
- **文件头**：自动添加列标题
- **批量写入**：使用缓冲写入提高I/O效率

## 配置信息

### 支持的代码范围
- **登记管理部门代码**：1, 5, 9, Y
- **机构类别代码**：1, 2, 3, 4, 5, 9
- **行政区划代码**：31个省市自治区的标准代码
- **字符集**：0-9, A-H, J-N, P-Y（共33个字符，排除I、O、Z）

### 理论组合数
- **总理论组合数**：约 4 × 6 × 31 × 33^9 ≈ 1.2 × 10^15
- **实际可用组合数**：足以支持5000万条唯一代码生成

## 运行演示程序

```bash
# 编译项目
mvn compile

# 运行演示程序
java -cp target/classes cn.org.nifa.bxcredit.generator.demo.UnifiedSocialCreditCodeDemo
```

演示程序提供交互式菜单，包括：
1. 基础代码生成演示
2. 代码校验演示
3. 组织机构代码提取演示
4. 自定义前缀代码生成
5. 批量生成到文件
6. 多线程批量生成到文件
7. 性能测试
8. 大规模数据生成（5000万条）
9. ValidationUtils集成演示

## 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=UnifiedSocialCreditCodeGeneratorTest
mvn test -Dtest=ValidationUtilsTest
```

## 与现有系统集成

### ValidationUtils集成
本工具类已完全集成到现有的ValidationUtils中：

```java
// 使用统一的校验接口
boolean isValid = ValidationUtils.validateOrgCode(code); // 支持9位和18位代码
String codeType = ValidationUtils.getCodeType(code);     // 自动识别代码类型
```

### 与姓名生成工具类协同使用
```java
// 生成完整的企业信息
String companyName = NameGenerator.generateChineseNameThreeChar() + "有限公司";
String creditCode = UnifiedSocialCreditCodeGenerator.generateCodeWithPrefix("9", "1", "110000");
String orgCode = UnifiedSocialCreditCodeGenerator.extractOrganizationCode(creditCode);

System.out.println("企业名称: " + companyName);
System.out.println("统一社会信用代码: " + creditCode);
System.out.println("组织机构代码: " + orgCode);
```

## 注意事项

1. **文件路径**：确保输出文件路径有写入权限
2. **磁盘空间**：大规模生成前请确保有足够的磁盘空间（5000万条约1.5GB）
3. **内存配置**：对于超大规模生成，建议设置JVM参数：`-Xmx4g -Xms2g`
4. **线程数量**：建议线程数不超过CPU核心数的2倍
5. **唯一性保证**：大规模生成时会自动处理重复代码，确保唯一性

## 错误处理

工具类提供完善的错误处理机制：
- **参数校验**：对输入参数进行严格校验
- **异常处理**：提供详细的异常信息
- **资源管理**：自动管理文件资源，确保正确关闭
- **进度监控**：实时报告生成进度和错误信息

## 技术实现

- **Java 11+** 兼容
- **线程安全**：使用ThreadLocalRandom和ConcurrentHashMap
- **高性能I/O**：使用BufferedWriter和批量写入
- **内存优化**：高效的数据结构和算法
- **并发处理**：使用CompletableFuture和ExecutorService
- **标准合规**：严格遵循GB 32100-2015国家标准

## 许可证

本项目遵循项目整体许可证。

---

如有问题或建议，请联系 NIFA Team。
