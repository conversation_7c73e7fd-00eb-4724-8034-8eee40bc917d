package cn.org.nifa.bxcredit.generator.validator;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertTrue;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import cn.org.nifa.bxcredit.generator.model.CreditGuaranteeInsuranceData;

/**
 * 信用保证保险规则校验器测试类
 */
public class CreditGuaranteeInsuranceRuleValidatorTest {

    private CreditGuaranteeInsuranceRuleValidator validator;
    
    @BeforeClass
    public void setUp() {
        validator = new CreditGuaranteeInsuranceRuleValidator();
    }
    
    @Test
    public void testValidatePositiveEnterpriseData() {
        // 创建有效的企业被保证人数据
        CreditGuaranteeInsuranceData data = createValidEnterpriseData();
        
        // 校验
        ValidationResult result = validator.validate(data);
        
        // 断言
        assertTrue(result.isValid(), "企业被保证人数据应该通过校验");
        assertEquals(result.getErrors().size(), 0, "不应该有错误信息");
    }
    
    @Test
    public void testValidatePositivePersonData() {
        // 创建有效的自然人被保证人数据
        CreditGuaranteeInsuranceData data = createValidPersonData();
        
        // 校验
        ValidationResult result = validator.validate(data);
        
        // 断言
        assertTrue(result.isValid(), "自然人被保证人数据应该通过校验");
        assertEquals(result.getErrors().size(), 0, "不应该有错误信息");
    }
    
    @Test
    public void testValidateInvalidGuaranteedPersonType() {
        // 创建无效的被保证人类型数据
        CreditGuaranteeInsuranceData data = createValidEnterpriseData();
        data.setGuaranteedPersonType("3"); // 无效的被保证人类型
        
        // 校验
        ValidationResult result = validator.validate(data);
        
        // 断言
        assertFalse(result.isValid(), "无效的被保证人类型数据不应该通过校验");
        assertTrue(result.getErrors().contains("被保证人类型必须为1(企业或其他组织)或2(自然人)"), 
                "应该包含被保证人类型错误信息");
    }
    
    @Test
    public void testValidateInvalidPersonCertType() {
        // 创建无效的自然人证件类型数据
        CreditGuaranteeInsuranceData data = createValidPersonData();
        data.setGuaranteedPersonCertType("a"); // 无效的自然人证件类型
        
        // 校验
        ValidationResult result = validator.validate(data);
        
        // 断言
        assertFalse(result.isValid(), "无效的自然人证件类型数据不应该通过校验");
        assertTrue(result.getErrors().contains("自然人被保证人证件类型必须为0~9、A、B、C或X"), 
                "应该包含自然人证件类型错误信息");
    }
    
    @Test
    public void testValidateInvalidEnterpriseCertType() {
        // 创建无效的企业证件类型数据
        CreditGuaranteeInsuranceData data = createValidEnterpriseData();
        data.setGuaranteedPersonCertType("0"); // 无效的企业证件类型
        
        // 校验
        ValidationResult result = validator.validate(data);
        
        // 断言
        assertFalse(result.isValid(), "无效的企业证件类型数据不应该通过校验");
        assertTrue(result.getErrors().contains("企业被保证人证件类型必须为a(组织机构代码)或b(社会信用代码)"), 
                "应该包含企业证件类型错误信息");
    }
    
    @Test
    public void testValidateMissingLegalPersonInfo() {
        // 创建缺少企业法人信息的数据
        CreditGuaranteeInsuranceData data = createValidEnterpriseData();
        data.setLegalPersonName(null);
        data.setLegalPersonCertType(null);
        data.setLegalPersonCertNumber(null);
        
        // 校验
        ValidationResult result = validator.validate(data);
        
        // 断言
        assertFalse(result.isValid(), "缺少企业法人信息的数据不应该通过校验");
        assertTrue(result.getErrors().contains("企业被保证人的企业法人姓名为必填项"), 
                "应该包含企业法人姓名错误信息");
        assertTrue(result.getErrors().contains("企业被保证人的企业法人证件类型为必填项"), 
                "应该包含企业法人证件类型错误信息");
        assertTrue(result.getErrors().contains("企业被保证人的企业法人证件号码为必填项"), 
                "应该包含企业法人证件号码错误信息");
    }
    
    @Test
    public void testValidateInvalidIdCardLastChar() {
        // 创建身份证号码最后一位为小写x的数据
        CreditGuaranteeInsuranceData data = createValidPersonData();
        data.setGuaranteedPersonCertType("0"); // 身份证
        data.setGuaranteedPersonCertNumber("11010119900101001x"); // 最后一位为小写x
        
        // 校验
        ValidationResult result = validator.validate(data);
        
        // 断言
        assertFalse(result.isValid(), "身份证号码最后一位为小写x的数据不应该通过校验");
        assertTrue(result.getErrors().contains("身份证号码最后一位小写x应该用大写X替代"), 
                "应该包含身份证号码最后一位错误信息");
    }
    
    @Test
    public void testValidateInvalidInsuranceDates() {
        // 创建保险起始日期晚于到期日期的数据
        CreditGuaranteeInsuranceData data = createValidEnterpriseData();
        data.setInsuranceStartDate("20241201");
        data.setInsuranceEndDate("20241130");
        
        // 校验
        ValidationResult result = validator.validate(data);
        
        // 断言
        assertFalse(result.isValid(), "保险起始日期晚于到期日期的数据不应该通过校验");
        assertTrue(result.getErrors().contains("保险起始日期不能晚于保险到期日期"), 
                "应该包含保险日期错误信息");
    }
    
    @Test
    public void testValidateFutureInsuranceStartDate() {
        // 创建保险起始日期为未来日期的数据
        CreditGuaranteeInsuranceData data = createValidEnterpriseData();
        LocalDate futureDate = LocalDate.now().plusDays(30);
        data.setInsuranceStartDate(futureDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        data.setInsuranceEndDate(futureDate.plusDays(30).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        
        // 校验
        ValidationResult result = validator.validate(data);
        
        // 断言
        assertFalse(result.isValid(), "保险起始日期为未来日期的数据不应该通过校验");
        assertTrue(result.getErrors().contains("保险起始日期不能晚于当前日期"), 
                "应该包含保险起始日期错误信息");
    }
    
    @Test
    public void testValidateDuplicateUnderwritingData() {
        // 创建两条相同业务发生机构和保单编号的承保信息数据
        CreditGuaranteeInsuranceData data1 = createValidEnterpriseData();
        data1.setBusinessInstitution("123456789");
        data1.setPolicyNumber("POL123456");
        data1.setDataType("UNDERWRITING");
        
        CreditGuaranteeInsuranceData data2 = createValidEnterpriseData();
        data2.setBusinessInstitution("123456789");
        data2.setPolicyNumber("POL123456");
        data2.setDataType("UNDERWRITING");
        
        // 批量校验
        List<CreditGuaranteeInsuranceData> dataList = new ArrayList<>();
        dataList.add(data1);
        dataList.add(data2);
        List<ValidationResult> results = validator.validateBatch(dataList);
        
        // 断言
        assertTrue(results.get(0).isValid(), "第一条数据应该通过校验");
        assertFalse(results.get(1).isValid(), "第二条数据不应该通过校验");
        assertTrue(results.get(1).getErrors().contains("承保信息文件中存在重复的业务发生机构+保单编号组合"), 
                "应该包含唯一性约束错误信息");
    }
    
    // 辅助方法：创建有效的企业被保证人数据
    private CreditGuaranteeInsuranceData createValidEnterpriseData() {
        CreditGuaranteeInsuranceData data = new CreditGuaranteeInsuranceData();
        
        // 人员标识信息段
        data.setGuaranteedPersonType("1"); // 企业或其他组织
        data.setGuaranteedPersonName("测试企业有限公司");
        data.setGuaranteedPersonCertType("b"); // 社会信用代码
        data.setGuaranteedPersonCertNumber("123456789012345678");
        data.setLegalPersonName("张总");
        data.setLegalPersonCertType("0"); // 身份证
        data.setLegalPersonCertNumber("110101199001010018");
        
        // 业务标识信息段
        data.setBusinessInstitution("123456789"); // 9位组织机构代码
        data.setPolicyNumber("POL123456789");
        
        // 承保信息段
        data.setDataType("UNDERWRITING");
        data.setInsuranceBusinessType("01"); // 信用保证保险
        data.setInsuranceMethod("1"); // 保证
        
        // 设置保险起始日期为过去日期
        LocalDate pastDate = LocalDate.now().minusDays(30);
        data.setInsuranceStartDate(pastDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        
        // 设置保险到期日期为起始日期之后
        LocalDate endDate = pastDate.plusDays(365);
        data.setInsuranceEndDate(endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        
        data.setInsuranceAmount("1000000");
        
        return data;
    }
    
    // 辅助方法：创建有效的自然人被保证人数据
    private CreditGuaranteeInsuranceData createValidPersonData() {
        CreditGuaranteeInsuranceData data = new CreditGuaranteeInsuranceData();
        
        // 人员标识信息段
        data.setGuaranteedPersonType("2"); // 自然人
        data.setGuaranteedPersonName("张三");
        data.setGuaranteedPersonCertType("0"); // 身份证
        data.setGuaranteedPersonCertNumber("110101199001010018");
        
        // 业务标识信息段
        data.setBusinessInstitution("123456789"); // 9位组织机构代码
        data.setPolicyNumber("POL123456789");
        
        // 承保信息段
        data.setDataType("UNDERWRITING");
        data.setInsuranceBusinessType("03"); // 个人消费信用保险
        data.setInsuranceMethod("2"); // 信用
        
        // 设置保险起始日期为过去日期
        LocalDate pastDate = LocalDate.now().minusDays(30);
        data.setInsuranceStartDate(pastDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        
        // 设置保险到期日期为起始日期之后
        LocalDate endDate = pastDate.plusDays(365);
        data.setInsuranceEndDate(endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        
        data.setInsuranceAmount("500000");
        
        return data;
    }
}