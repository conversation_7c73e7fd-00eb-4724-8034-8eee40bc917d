package cn.org.nifa.bxcredit.generator.util;

import java.util.Random;

/**
 * 业务号生成器
 * 
 * <AUTHOR> Team
 */
public class BusinessCodeGenerator {
    
    private static final Random RANDOM = new Random();
    
    /**
     * 生成业务号
     * 格式：BN + 30位随机数字
     * 
     * @return 业务号
     */
    public static String generateBusinessNumber() {
        StringBuilder businessNumber = new StringBuilder("BN");
        
        // 生成30位随机数字
        for (int i = 0; i < 30; i++) {
            businessNumber.append(RANDOM.nextInt(10));
        }
        
        return businessNumber.toString();
    }
    
    /**
     * 生成组织机构代码（9位）
     * 
     * @return 组织机构代码
     */
    public static String generateOrgCode() {
        StringBuilder orgCode = new StringBuilder();
        
        // 生成8位数字
        for (int i = 0; i < 8; i++) {
            orgCode.append(RANDOM.nextInt(10));
        }
        
        // 生成1位校验码（数字或字母）
        if (RANDOM.nextBoolean()) {
            orgCode.append(RANDOM.nextInt(10));
        } else {
            orgCode.append((char) ('A' + RANDOM.nextInt(26)));
        }
        
        return orgCode.toString();
    }
    
    /**
     * 生成社会信用代码（18位）
     * 
     * @return 社会信用代码
     */
    public static String generateSocialCreditCode() {
        StringBuilder creditCode = new StringBuilder();
        
        // 第1位：登记管理部门代码
        String[] deptCodes = {"1", "5", "9", "Y"};
        creditCode.append(deptCodes[RANDOM.nextInt(deptCodes.length)]);
        
        // 第2位：机构类别代码
        String[] typeCodes = {"1", "2", "3", "9"};
        creditCode.append(typeCodes[RANDOM.nextInt(typeCodes.length)]);
        
        // 第3-8位：登记管理机关行政区划码
        String[] areaCodes = {"110000", "310000", "440000", "500000"};
        creditCode.append(areaCodes[RANDOM.nextInt(areaCodes.length)]);
        
        // 第9-17位：主体标识码
        for (int i = 0; i < 9; i++) {
            if (RANDOM.nextBoolean()) {
                creditCode.append(RANDOM.nextInt(10));
            } else {
                creditCode.append((char) ('A' + RANDOM.nextInt(26)));
            }
        }
        
        // 第18位：校验码
        creditCode.append(calculateSocialCreditCheckCode(creditCode.toString()));
        
        return creditCode.toString();
    }
    
    /**
     * 计算社会信用代码校验码
     * 
     * @param code17 前17位代码
     * @return 校验码
     */
    private static char calculateSocialCreditCheckCode(String code17) {
        // 简化处理，随机生成校验码
        String checkCodes = "0123456789ABCDEFGHJKLMNPQRTUWXY";
        return checkCodes.charAt(RANDOM.nextInt(checkCodes.length()));
    }
    
    /**
     * 生成无效的业务号（用于反向测试）
     * 
     * @param violationType 违反类型
     * @return 无效业务号
     */
    public static String generateInvalidBusinessNumber(String violationType) {
        switch (violationType) {
            case "NO_PREFIX":
                return generateRandomDigits(32); // 没有BN前缀
            case "WRONG_PREFIX":
                return "AB" + generateRandomDigits(30); // 错误前缀
            case "SHORT_LENGTH":
                return "BN" + generateRandomDigits(29); // 长度不足
            case "LONG_LENGTH":
                return "BN" + generateRandomDigits(31); // 长度超出
            case "NON_DIGIT":
                return "BN" + generateRandomDigits(29) + "A"; // 包含非数字
            default:
                return generateBusinessNumber();
        }
    }
    
    /**
     * 生成指定长度的随机数字字符串
     * 
     * @param length 长度
     * @return 数字字符串
     */
    private static String generateRandomDigits(int length) {
        StringBuilder digits = new StringBuilder();
        for (int i = 0; i < length; i++) {
            digits.append(RANDOM.nextInt(10));
        }
        return digits.toString();
    }
}