package cn.org.nifa.bxcredit.generator;

import java.util.List;

import org.testng.Assert;
import org.testng.annotations.Test;

import cn.org.nifa.bxcredit.generator.generator.PersonalDataGenerator;
import cn.org.nifa.bxcredit.generator.model.PersonalLoanData;
import cn.org.nifa.bxcredit.generator.util.BusinessCodeGenerator;
import cn.org.nifa.bxcredit.generator.util.DateUtils;
import cn.org.nifa.bxcredit.generator.util.IdCardGenerator;
import cn.org.nifa.bxcredit.generator.util.ValidationUtils;
import cn.org.nifa.bxcredit.generator.validator.PersonalDataValidator;
import cn.org.nifa.bxcredit.generator.validator.ValidationResult;

/**
 * 测试数据生成器测试类
 * 
 * <AUTHOR> Team
 */
public class TestDataGeneratorTest {
    
    @Test
    public void testIdCardGenerator() {
        // 测试身份证号生成
        String idCard = IdCardGenerator.generateRandomIdCard();
        Assert.assertNotNull(idCard);
        Assert.assertEquals(idCard.length(), 18);
        Assert.assertTrue(IdCardGenerator.validateIdCard(idCard));
        
        // 测试无效身份证号生成
        String invalidIdCard = IdCardGenerator.generateInvalidIdCard("LENGTH");
        Assert.assertFalse(IdCardGenerator.validateIdCard(invalidIdCard));
    }
    
    @Test
    public void testBusinessCodeGenerator() {
        // 测试业务号生成
        String businessNumber = BusinessCodeGenerator.generateBusinessNumber();
        Assert.assertNotNull(businessNumber);
        Assert.assertEquals(businessNumber.length(), 32);
        Assert.assertTrue(businessNumber.startsWith("BN"));
        Assert.assertTrue(ValidationUtils.validateBusinessNumber(businessNumber));
        
        // 测试组织机构代码生成
        String orgCode = BusinessCodeGenerator.generateOrgCode();
        Assert.assertNotNull(orgCode);
        Assert.assertEquals(orgCode.length(), 9);
        
        // 测试社会信用代码生成
        String creditCode = BusinessCodeGenerator.generateSocialCreditCode();
        Assert.assertNotNull(creditCode);
        Assert.assertEquals(creditCode.length(), 18);
    }
    
    @Test
    public void testDateUtils() {
        // 测试日期工具
        String currentDate = DateUtils.getCurrentDate();
        Assert.assertNotNull(currentDate);
        Assert.assertEquals(currentDate.length(), 8);
        Assert.assertTrue(DateUtils.isValidDate(currentDate));
        
        // 测试日期比较
        String date1 = "20231201";
        String date2 = "20231202";
        Assert.assertTrue(DateUtils.compareDates(date1, date2) < 0);
        
        // 测试无效日期
        Assert.assertFalse(DateUtils.isValidDate("20230230"));
    }
    
    @Test
    public void testPersonalDataGenerator() {
        PersonalDataGenerator generator = new PersonalDataGenerator();
        
        // 测试正向数据生成
        PersonalLoanData positiveData = generator.generatePositiveData();
        Assert.assertNotNull(positiveData);
        Assert.assertNotNull(positiveData.getCertificateType());
        Assert.assertNotNull(positiveData.getBusinessNumber());
        
        // 测试批量正向数据生成
        List<PersonalLoanData> positiveDataList = generator.generatePositiveDataBatch(5);
        Assert.assertEquals(positiveDataList.size(), 5);
        
        // 测试反向数据生成
        PersonalLoanData negativeData = generator.generateNegativeData("CERT_TYPE_INVALID");
        Assert.assertNotNull(negativeData);
        
        // 测试所有反向数据生成
        List<PersonalLoanData> negativeDataList = generator.generateAllNegativeData();
        Assert.assertTrue(negativeDataList.size() > 0);
    }
    
    @Test
    public void testPersonalDataValidator() {
        PersonalDataValidator validator = new PersonalDataValidator();
        PersonalDataGenerator generator = new PersonalDataGenerator();
        
        // 测试正向数据校验
        PersonalLoanData validData = generator.generatePositiveData();
        ValidationResult result = validator.validate(validData);
        Assert.assertNotNull(result);
        
        // 测试无效数据校验
        PersonalLoanData invalidData = generator.generateNegativeData("CERT_TYPE_INVALID");
        ValidationResult invalidResult = validator.validate(invalidData);
        Assert.assertNotNull(invalidResult);
        Assert.assertFalse(invalidResult.isValid());
        Assert.assertTrue(invalidResult.getErrors().size() > 0);
    }
    
    @Test
    public void testValidationUtils() {
        // 测试长度校验
        Assert.assertTrue(ValidationUtils.validateLength("test", 1, 10));
        Assert.assertFalse(ValidationUtils.validateLength("test", 5, 10));
        
        // 测试数字校验
        Assert.assertTrue(ValidationUtils.isNumeric("12345"));
        Assert.assertFalse(ValidationUtils.isNumeric("123a5"));
        
        // 测试非负整数校验
        Assert.assertTrue(ValidationUtils.isNonNegativeInteger("12345"));
        Assert.assertFalse(ValidationUtils.isNonNegativeInteger("-123"));
        
        // 测试业务号校验
        String validBusinessNumber = BusinessCodeGenerator.generateBusinessNumber();
        Assert.assertTrue(ValidationUtils.validateBusinessNumber(validBusinessNumber));
        Assert.assertFalse(ValidationUtils.validateBusinessNumber("AB123456789012345678901234567890"));
        
        // 测试枚举值校验
        Assert.assertTrue(ValidationUtils.validateEnum("0", ValidationUtils.PERSONAL_CERT_TYPES));
        Assert.assertFalse(ValidationUtils.validateEnum("Z", ValidationUtils.PERSONAL_CERT_TYPES));
    }
}