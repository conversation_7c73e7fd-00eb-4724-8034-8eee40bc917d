package cn.org.nifa.bxcredit.generator.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 公司名称生成工具类
 * 
 * 支持生成多种格式的公司名称：
 * - 前缀+核心词+后缀（如：未来科技有限公司）
 * - 核心词+核心词+后缀（如：创新发展集团）
 * - 核心词+数字/字母+后缀（如：数字时代A888科技）
 * - 地名+核心词+后缀（如：上海创新信息咨询）
 * 
 * 同时支持大规模（5000万）唯一公司名称生成，采用分批处理和多线程技术
 * 
 * <AUTHOR> Team
 */
public class CompanyNameGenerator {
    
    // 数据存储集合
    private static final List<String> PREFIX_WORDS = new ArrayList<>();
    private static final List<String> CORE_WORDS = new ArrayList<>();
    private static final List<String> SUFFIX_WORDS = new ArrayList<>();
    private static final List<String> LOCATION_WORDS = new ArrayList<>();
    private static final List<String> INDUSTRY_WORDS = new ArrayList<>();
    
    // 随机数生成器
    private static final Random RANDOM = new Random();
    
    // 数据文件路径
    private static final String PREFIX_WORDS_FILE = "/data/company_prefix_words.txt";
    private static final String CORE_WORDS_FILE = "/data/company_core_words.txt";
    private static final String SUFFIX_WORDS_FILE = "/data/company_suffix_words.txt";
    private static final String LOCATION_WORDS_FILE = "/data/company_location_words.txt";
    private static final String INDUSTRY_WORDS_FILE = "/data/company_industry_words.txt";
    
    // 名称模板类型
    public enum NameTemplate {
        PREFIX_CORE_SUFFIX,       // 前缀+核心词+后缀
        CORE_CORE_SUFFIX,         // 核心词+核心词+后缀
        CORE_DIGIT_SUFFIX,        // 核心词+数字/字母+后缀
        LOCATION_CORE_SUFFIX,     // 地名+核心词+后缀
        RANDOM                    // 随机选择模板
    }
    
    // 静态初始化块，加载所有公司名称数据
    static {
        try {
            // 初始化默认词库数据
            initializeDefaultData();
            
            // 尝试从文件加载数据
            loadDataFromFile(PREFIX_WORDS_FILE, PREFIX_WORDS);
            loadDataFromFile(CORE_WORDS_FILE, CORE_WORDS);
            loadDataFromFile(SUFFIX_WORDS_FILE, SUFFIX_WORDS);
            loadDataFromFile(LOCATION_WORDS_FILE, LOCATION_WORDS);
            loadDataFromFile(INDUSTRY_WORDS_FILE, INDUSTRY_WORDS);
            
            System.out.println("公司名称数据加载完成:");
            System.out.println("前缀词: " + PREFIX_WORDS.size() + " 个");
            System.out.println("核心词: " + CORE_WORDS.size() + " 个");
            System.out.println("后缀词: " + SUFFIX_WORDS.size() + " 个");
            System.out.println("地名词: " + LOCATION_WORDS.size() + " 个");
            System.out.println("行业词: " + INDUSTRY_WORDS.size() + " 个");
            
        } catch (Exception e) {
            System.err.println("公司名称数据加载失败: " + e.getMessage());
            // 确保即使文件加载失败，也有默认数据可用
            initializeDefaultData();
        }
    }
    
    /**
     * 初始化默认词库数据
     */
    private static void initializeDefaultData() {
        // 前缀词
        if (PREFIX_WORDS.isEmpty()) {
            PREFIX_WORDS.add("未来");
            PREFIX_WORDS.add("新");
            PREFIX_WORDS.add("大");
            PREFIX_WORDS.add("小");
            PREFIX_WORDS.add("全球");
            PREFIX_WORDS.add("数字");
            PREFIX_WORDS.add("智慧");
            PREFIX_WORDS.add("优");
            PREFIX_WORDS.add("高");
            PREFIX_WORDS.add("先进");
        }
        
        // 核心词
        if (CORE_WORDS.isEmpty()) {
            CORE_WORDS.add("科技");
            CORE_WORDS.add("创新");
            CORE_WORDS.add("信息");
            CORE_WORDS.add("智能");
            CORE_WORDS.add("网络");
            CORE_WORDS.add("发展");
            CORE_WORDS.add("咨询");
            CORE_WORDS.add("文化");
            CORE_WORDS.add("传媒");
            CORE_WORDS.add("能源");
            CORE_WORDS.add("生物");
            CORE_WORDS.add("医疗");
            CORE_WORDS.add("教育");
            CORE_WORDS.add("金融");
            CORE_WORDS.add("物流");
            CORE_WORDS.add("制造");
            CORE_WORDS.add("贸易");
            CORE_WORDS.add("建筑");
            CORE_WORDS.add("环境");
            CORE_WORDS.add("农业");
            CORE_WORDS.add("服务");
        }
        
        // 后缀词
        if (SUFFIX_WORDS.isEmpty()) {
            SUFFIX_WORDS.add("有限公司");
            SUFFIX_WORDS.add("集团");
            SUFFIX_WORDS.add("股份");
            SUFFIX_WORDS.add("工作室");
            SUFFIX_WORDS.add("中心");
            SUFFIX_WORDS.add("工厂");
            SUFFIX_WORDS.add("商行");
            SUFFIX_WORDS.add("实业");
        }
        
        // 地名词
        if (LOCATION_WORDS.isEmpty()) {
            LOCATION_WORDS.add("北京");
            LOCATION_WORDS.add("上海");
            LOCATION_WORDS.add("广州");
            LOCATION_WORDS.add("深圳");
            LOCATION_WORDS.add("杭州");
            LOCATION_WORDS.add("南京");
            LOCATION_WORDS.add("武汉");
            LOCATION_WORDS.add("成都");
            LOCATION_WORDS.add("重庆");
            LOCATION_WORDS.add("西安");
        }
        
        // 行业词
        if (INDUSTRY_WORDS.isEmpty()) {
            INDUSTRY_WORDS.add("互联网");
            INDUSTRY_WORDS.add("人工智能");
            INDUSTRY_WORDS.add("大数据");
            INDUSTRY_WORDS.add("云计算");
            INDUSTRY_WORDS.add("区块链");
            INDUSTRY_WORDS.add("物联网");
            INDUSTRY_WORDS.add("新能源");
            INDUSTRY_WORDS.add("生物医药");
            INDUSTRY_WORDS.add("半导体");
            INDUSTRY_WORDS.add("新材料");
        }
    }
    
    /**
     * 私有构造方法，防止实例化
     */
    private CompanyNameGenerator() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    /**
     * 生成公司名称（前缀+核心词+后缀）
     * 
     * @return 公司名称
     */
    public static String generatePrefixCoreSuffixName() {
        String prefix = getRandomElement(PREFIX_WORDS);
        String core = getRandomElement(CORE_WORDS);
        String suffix = getRandomElement(SUFFIX_WORDS);
        return prefix + core + suffix;
    }
    
    /**
     * 生成公司名称（核心词+核心词+后缀）
     * 
     * @return 公司名称
     */
    public static String generateCoreCoreSuffixName() {
        String core1 = getRandomElement(CORE_WORDS);
        String core2 = getRandomElement(CORE_WORDS);
        // 避免两个核心词相同
        while (core2.equals(core1)) {
            core2 = getRandomElement(CORE_WORDS);
        }
        String suffix = getRandomElement(SUFFIX_WORDS);
        return core1 + core2 + suffix;
    }
    
    /**
     * 生成公司名称（核心词+数字/字母+后缀）
     * 
     * @return 公司名称
     */
    public static String generateCoreDigitSuffixName() {
        String core = getRandomElement(CORE_WORDS);
        String digit = generateRandomDigitLetterCode();
        String suffix = getRandomElement(SUFFIX_WORDS);
        return core + digit + suffix;
    }
    
    /**
     * 生成公司名称（地名+核心词+后缀）
     * 
     * @return 公司名称
     */
    public static String generateLocationCoreSuffixName() {
        String location = getRandomElement(LOCATION_WORDS);
        String core = getRandomElement(CORE_WORDS);
        String suffix = getRandomElement(SUFFIX_WORDS);
        return location + core + suffix;
    }
    
    /**
     * 随机生成公司名称（随机选择模板）
     * 
     * @return 公司名称
     */
    public static String generateRandomCompanyName() {
        int templateType = RANDOM.nextInt(4);
        switch (templateType) {
            case 0:
                return generatePrefixCoreSuffixName();
            case 1:
                return generateCoreCoreSuffixName();
            case 2:
                return generateCoreDigitSuffixName();
            case 3:
                return generateLocationCoreSuffixName();
            default:
                return generatePrefixCoreSuffixName();
        }
    }
    
    /**
     * 根据指定模板生成公司名称
     * 
     * @param template 名称模板
     * @return 公司名称
     */
    public static String generateCompanyName(NameTemplate template) {
        switch (template) {
            case PREFIX_CORE_SUFFIX:
                return generatePrefixCoreSuffixName();
            case CORE_CORE_SUFFIX:
                return generateCoreCoreSuffixName();
            case CORE_DIGIT_SUFFIX:
                return generateCoreDigitSuffixName();
            case LOCATION_CORE_SUFFIX:
                return generateLocationCoreSuffixName();
            case RANDOM:
                return generateRandomCompanyName();
            default:
                return generateRandomCompanyName();
        }
    }
    
    /**
     * 使用线程安全的随机数生成器生成公司名称
     * 适用于多线程环境
     * 
     * @param template 名称模板
     * @return 公司名称
     */
    public static String generateCompanyNameThreadSafe(NameTemplate template) {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        
        switch (template) {
            case PREFIX_CORE_SUFFIX:
                return getRandomElementThreadSafe(PREFIX_WORDS) + 
                       getRandomElementThreadSafe(CORE_WORDS) + 
                       getRandomElementThreadSafe(SUFFIX_WORDS);
                
            case CORE_CORE_SUFFIX:
                String core1 = getRandomElementThreadSafe(CORE_WORDS);
                String core2 = getRandomElementThreadSafe(CORE_WORDS);
                // 避免两个核心词相同
                while (core2.equals(core1)) {
                    core2 = getRandomElementThreadSafe(CORE_WORDS);
                }
                return core1 + core2 + getRandomElementThreadSafe(SUFFIX_WORDS);
                
            case CORE_DIGIT_SUFFIX:
                return getRandomElementThreadSafe(CORE_WORDS) + 
                       generateRandomDigitLetterCodeThreadSafe() + 
                       getRandomElementThreadSafe(SUFFIX_WORDS);
                
            case LOCATION_CORE_SUFFIX:
                return getRandomElementThreadSafe(LOCATION_WORDS) + 
                       getRandomElementThreadSafe(CORE_WORDS) + 
                       getRandomElementThreadSafe(SUFFIX_WORDS);
                
            case RANDOM:
                int templateType = random.nextInt(4);
                switch (templateType) {
                    case 0:
                        return getRandomElementThreadSafe(PREFIX_WORDS) + 
                               getRandomElementThreadSafe(CORE_WORDS) + 
                               getRandomElementThreadSafe(SUFFIX_WORDS);
                    case 1:
                        core1 = getRandomElementThreadSafe(CORE_WORDS);
                        core2 = getRandomElementThreadSafe(CORE_WORDS);
                        while (core2.equals(core1)) {
                            core2 = getRandomElementThreadSafe(CORE_WORDS);
                        }
                        return core1 + core2 + getRandomElementThreadSafe(SUFFIX_WORDS);
                    case 2:
                        return getRandomElementThreadSafe(CORE_WORDS) + 
                               generateRandomDigitLetterCodeThreadSafe() + 
                               getRandomElementThreadSafe(SUFFIX_WORDS);
                    case 3:
                        return getRandomElementThreadSafe(LOCATION_WORDS) + 
                               getRandomElementThreadSafe(CORE_WORDS) + 
                               getRandomElementThreadSafe(SUFFIX_WORDS);
                    default:
                        return getRandomElementThreadSafe(PREFIX_WORDS) + 
                               getRandomElementThreadSafe(CORE_WORDS) + 
                               getRandomElementThreadSafe(SUFFIX_WORDS);
                }
                
            default:
                return getRandomElementThreadSafe(PREFIX_WORDS) + 
                       getRandomElementThreadSafe(CORE_WORDS) + 
                       getRandomElementThreadSafe(SUFFIX_WORDS);
        }
    }
    
    /**
     * 生成随机数字字母组合代码
     * 
     * @return 随机代码
     */
    private static String generateRandomDigitLetterCode() {
        StringBuilder code = new StringBuilder();
        int length = 3 + RANDOM.nextInt(4); // 3-6位长度
        
        // 第一位是字母
        code.append((char) ('A' + RANDOM.nextInt(26)));
        
        // 剩余位是数字
        for (int i = 1; i < length; i++) {
            code.append(RANDOM.nextInt(10));
        }
        
        return code.toString();
    }
    
    /**
     * 线程安全版本的随机数字字母组合代码生成
     * 
     * @return 随机代码
     */
    private static String generateRandomDigitLetterCodeThreadSafe() {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        StringBuilder code = new StringBuilder();
        int length = 3 + random.nextInt(4); // 3-6位长度
        
        // 第一位是字母
        code.append((char) ('A' + random.nextInt(26)));
        
        // 剩余位是数字
        for (int i = 1; i < length; i++) {
            code.append(random.nextInt(10));
        }
        
        return code.toString();
    }
    
    /**
     * 从文件加载数据到指定列表
     * 
     * @param filePath 文件路径
     * @param dataList 目标列表
     * @throws IOException 文件读取异常
     */
    private static void loadDataFromFile(String filePath, List<String> dataList) throws IOException {
        try (InputStream inputStream = CompanyNameGenerator.class.getResourceAsStream(filePath)) {
            if (inputStream == null) {
                System.out.println("未找到文件: " + filePath + "，使用默认数据");
                return;
            }
            
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    if (!line.isEmpty()) {
                        dataList.add(line);
                    }
                }
            }
        }
    }
    
    /**
     * 从列表中随机获取一个元素
     * 
     * @param list 源列表
     * @return 随机元素
     * @throws IllegalArgumentException 如果列表为空
     */
    private static String getRandomElement(List<String> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("列表不能为空");
        }
        return list.get(RANDOM.nextInt(list.size()));
    }
    
    /**
     * 使用线程安全的随机数生成器从列表中随机获取一个元素
     * 
     * @param list 源列表
     * @return 随机元素
     * @throws IllegalArgumentException 如果列表为空
     */
    private static String getRandomElementThreadSafe(List<String> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("列表不能为空");
        }
        return list.get(ThreadLocalRandom.current().nextInt(list.size()));
    }
    
    /**
     * 校验公司名称格式
     * 
     * @param companyName 公司名称
     * @return 是否有效
     */
    public static boolean validateCompanyName(String companyName) {
        if (ValidationUtils.isEmpty(companyName)) {
            return false;
        }
        
        // 基本长度检查
        if (companyName.length() < 4 || companyName.length() > 50) {
            return false;
        }
        
        // 检查是否包含常见的公司后缀
        boolean hasSuffix = false;
        for (String suffix : SUFFIX_WORDS) {
            if (companyName.contains(suffix)) {
                hasSuffix = true;
                break;
            }
        }
        
        return hasSuffix;
    }
    
    /**
     * 批量生成结果类
     */
    public static class BatchGenerationResult {
        private final long totalGenerated;
        private final long uniqueGenerated;
        private final long duplicateCount;
        private final long totalTimeMs;
        private final double averageSpeed;
        
        public BatchGenerationResult(long totalGenerated, long uniqueGenerated,
                                   long duplicateCount, long totalTimeMs) {
            this.totalGenerated = totalGenerated;
            this.uniqueGenerated = uniqueGenerated;
            this.duplicateCount = duplicateCount;
            this.totalTimeMs = totalTimeMs;
            this.averageSpeed = totalTimeMs > 0 ? (double) uniqueGenerated * 1000 / totalTimeMs : 0;
        }
        
        public long getTotalGenerated() { return totalGenerated; }
        public long getUniqueGenerated() { return uniqueGenerated; }
        public long getDuplicateCount() { return duplicateCount; }
        public long getTotalTimeMs() { return totalTimeMs; }
        public double getAverageSpeed() { return averageSpeed; }
        
        @Override
        public String toString() {
            return String.format(
                "批量生成结果:\n" +
                "  总生成数量: %,d\n" +
                "  唯一名称数量: %,d\n" +
                "  重复名称数量: %,d\n" +
                "  总耗时: %,d ms (%.2f 秒)\n" +
                "  平均速度: %,.0f 个/秒\n" +
                "  唯一性比例: %.2f%%",
                totalGenerated, uniqueGenerated, duplicateCount, totalTimeMs,
                totalTimeMs / 1000.0, averageSpeed,
                totalGenerated > 0 ? (double) uniqueGenerated / totalGenerated * 100 : 0
            );
        }
    }
    
    /**
     * 批量生成公司名称到文件
     *
     * @param count 生成数量
     * @param outputFilePath 输出文件路径
     * @param template 名称模板
     * @return 生成结果
     * @throws IOException 文件操作异常
     */
    public static BatchGenerationResult generateNamesToFile(long count, String outputFilePath,
                                                          NameTemplate template) throws IOException {
        Path outputPath = Paths.get(outputFilePath);
        Set<String> generatedNames = new HashSet<>();
        long totalGenerated = 0;
        long duplicateCount = 0;
        
        long startTime = System.currentTimeMillis();
        
        try (BufferedWriter writer = Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8,
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {
            
            // 写入文件头
            writer.write("公司名称");
            writer.newLine();
            
            final int batchSize = 10000;
            List<String> batch = new ArrayList<>(batchSize);
            
            while (generatedNames.size() < count) {
                String name = generateCompanyName(template);
                totalGenerated++;
                
                if (generatedNames.add(name)) {
                    batch.add(name);
                    
                    // 批量写入
                    if (batch.size() >= batchSize || generatedNames.size() >= count) {
                        for (String line : batch) {
                            writer.write(line);
                            writer.newLine();
                        }
                        writer.flush();
                        batch.clear();
                        
                        // 进度提示
                        if (generatedNames.size() % 100000 == 0 || generatedNames.size() >= count) {
                            System.out.printf("已生成 %,d/%,d 条唯一公司名称 (%.2f%%), 总尝试: %,d\n",
                                generatedNames.size(), count,
                                (double) generatedNames.size() / count * 100, totalGenerated);
                        }
                    }
                } else {
                    duplicateCount++;
                }
            }
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        BatchGenerationResult result = new BatchGenerationResult(
            totalGenerated, generatedNames.size(), duplicateCount, totalTime);
        
        System.out.println("批量生成完成，保存至: " + outputFilePath);
        System.out.println(result);
        
        return result;
    }
    
    /**
     * 多线程批量生成公司名称到文件
     * 适用于大数据量生成（如5000万条）
     *
     * @param count 生成数量
     * @param outputFilePath 输出文件路径
     * @param template 名称模板
     * @param threadCount 线程数量
     * @param batchSize 批处理大小
     * @return 生成结果
     * @throws IOException 文件操作异常
     * @throws InterruptedException 线程中断异常
     */
    public static BatchGenerationResult generateNamesToFileMultiThreaded(
            long count, String outputFilePath, NameTemplate template, 
            int threadCount, int batchSize) throws IOException, InterruptedException {
        
        Path outputPath = Paths.get(outputFilePath);
        Set<String> generatedNames = ConcurrentHashMap.newKeySet();
        AtomicLong totalGenerated = new AtomicLong(0);
        AtomicLong duplicateCount = new AtomicLong(0);
        AtomicLong writtenCount = new AtomicLong(0);
        
        // 创建线程安全的队列
        BlockingQueue<String> nameQueue = new LinkedBlockingQueue<>(50000);
        
        long startTime = System.currentTimeMillis();
        
        // 创建线程池
        ExecutorService generatorPool = Executors.newFixedThreadPool(threadCount);
        ExecutorService writerPool = Executors.newSingleThreadExecutor();
        
        System.out.println("开始多线程生成公司名称:");
        System.out.println("  目标数量: " + String.format("%,d", count));
        System.out.println("  输出文件: " + outputFilePath);
        System.out.println("  线程数量: " + threadCount);
        System.out.println("  批处理大小: " + batchSize);
        System.out.println("  名称模板: " + template);
        System.out.println();
        
        // 启动写入线程
        CompletableFuture<Void> writerFuture = CompletableFuture.runAsync(() -> {
            try (BufferedWriter writer = Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8,
                    StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {
                
                // 写入文件头
                writer.write("公司名称");
                writer.newLine();
                
                List<String> batch = new ArrayList<>(batchSize);
                
                while (writtenCount.get() < count) {
                    String name = nameQueue.poll(1, TimeUnit.SECONDS);
                    if (name != null) {
                        batch.add(name);
                        
                        if (batch.size() >= batchSize || writtenCount.get() + batch.size() >= count) {
                            for (String line : batch) {
                                writer.write(line);
                                writer.newLine();
                            }
                            writer.flush();
                            
                            long written = writtenCount.addAndGet(batch.size());
                            batch.clear();
                            
                            // 进度提示
                            if (written % 1000000 == 0 || written >= count) {
                                System.out.printf("[写入] 进度: %,d/%,d (%.2f%%)\n",
                                    written, count, (double) written / count * 100);
                            }
                        }
                    }
                }
                
                // 写入剩余数据
                if (!batch.isEmpty()) {
                    for (String line : batch) {
                        writer.write(line);
                        writer.newLine();
                    }
                    writer.flush();
                    writtenCount.addAndGet(batch.size());
                }
                
            } catch (Exception e) {
                throw new RuntimeException("写入文件失败", e);
            }
        }, writerPool);
        
        // 启动生成线程
        List<CompletableFuture<Void>> generatorFutures = new ArrayList<>();
        
        for (int i = 0; i < threadCount; i++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                while (generatedNames.size() < count) {
                    try {
                        String name = generateCompanyNameThreadSafe(template);
                        totalGenerated.incrementAndGet();
                        
                        if (generatedNames.add(name)) {
                            nameQueue.put(name);
                            
                            long generated = generatedNames.size();
                            if (generated % 1000000 == 0) {
                                System.out.printf("[生成] 进度: %,d/%,d (%.2f%%), 总尝试: %,d\n",
                                    generated, count, (double) generated / count * 100,
                                    totalGenerated.get());
                            }
                        } else {
                            duplicateCount.incrementAndGet();
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }, generatorPool);
            
            generatorFutures.add(future);
        }
        
        // 等待所有生成线程完成
        CompletableFuture.allOf(generatorFutures.toArray(new CompletableFuture[0])).join();
        
        // 等待写入线程完成
        writerFuture.join();
        
        // 关闭线程池
        generatorPool.shutdown();
        writerPool.shutdown();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        BatchGenerationResult result = new BatchGenerationResult(
            totalGenerated.get(), generatedNames.size(), duplicateCount.get(), totalTime);
        
        System.out.println("\n=== 多线程生成完成 ===");
        System.out.println("文件保存至: " + outputFilePath);
        System.out.println(result);
        
        return result;
    }
    
    /**
     * 生成5000万条公司名称数据的便捷方法
     * 
     * @param template 名称模板
     * @param outputFilePath 输出文件路径
     * @return 生成统计信息
     * @throws IOException 文件操作异常
     * @throws InterruptedException 线程中断异常
     */
    public static BatchGenerationResult generate50Million(NameTemplate template, String outputFilePath) 
            throws IOException, InterruptedException {
        
        int threadCount = Runtime.getRuntime().availableProcessors();
        int batchSize = 10000;
        
        return generateNamesToFileMultiThreaded(50_000_000L, outputFilePath, template, threadCount, batchSize);
    }
    
    /**
     * 估算生成时间
     * 
     * @param count 生成数量
     * @param template 名称模板
     * @return 估算时间（秒）
     */
    public static double estimateGenerationTime(long count, NameTemplate template) {
        // 基于性能测试的经验值，每秒可生成约50万个公司名称
        double baseSpeed = 500_000.0; // 个/秒
        
        // 根据名称模板调整速度
        double templateMultiplier = 1.0;
        switch (template) {
            case PREFIX_CORE_SUFFIX:
                templateMultiplier = 1.0;
                break;
            case CORE_CORE_SUFFIX:
                templateMultiplier = 0.95;
                break;
            case CORE_DIGIT_SUFFIX:
                templateMultiplier = 1.1; // 数字增加唯一性，减少碰撞
                break;
            case LOCATION_CORE_SUFFIX:
                templateMultiplier = 0.9;
                break;
            case RANDOM:
                templateMultiplier = 0.98;
                break;
        }
        
        return count / (baseSpeed * templateMultiplier);
    }
    
    /**
     * 估算文件大小
     * 
     * @param count 生成数量
     * @param template 名称模板
     * @return 估算文件大小（字节）
     */
    public static long estimateFileSize(long count, NameTemplate template) {
        int avgBytesPerName;
        
        switch (template) {
            case PREFIX_CORE_SUFFIX:
                avgBytesPerName = 20; // 平均公司名称长度 + 换行符
                break;
            case CORE_CORE_SUFFIX:
                avgBytesPerName = 22; // 平均公司名称长度 + 换行符
                break;
            case CORE_DIGIT_SUFFIX:
                avgBytesPerName = 25; // 平均公司名称长度 + 换行符
                break;
            case LOCATION_CORE_SUFFIX:
                avgBytesPerName = 23; // 平均公司名称长度 + 换行符
                break;
            case RANDOM:
                avgBytesPerName = 22; // 平均公司名称长度 + 换行符
                break;
            default:
                avgBytesPerName = 22;
        }
        
        return count * avgBytesPerName;
    }
    
    /**
     * 获取前缀词数量
     * 
     * @return 前缀词数量
     */
    public static int getPrefixWordCount() {
        return PREFIX_WORDS.size();
    }
    
    /**
     * 获取核心词数量
     * 
     * @return 核心词数量
     */
    public static int getCoreWordCount() {
        return CORE_WORDS.size();
    }
    
    /**
     * 获取后缀词数量
     * 
     * @return 后缀词数量
     */
    public static int getSuffixWordCount() {
        return SUFFIX_WORDS.size();
    }
    
    /**
     * 获取地名词数量
     * 
     * @return 地名词数量
     */
    public static int getLocationWordCount() {
        return LOCATION_WORDS.size();
    }
    
    /**
     * 获取行业词数量
     * 
     * @return 行业词数量
     */
    public static int getIndustryWordCount() {
        return INDUSTRY_WORDS.size();
    }
    
    /**
     * 计算理论上可能的组合数量
     * 
     * @param template 名称模板
     * @return 理论组合数量
     */
    public static long calculateTheoricalCombinations(NameTemplate template) {
        switch (template) {
            case PREFIX_CORE_SUFFIX:
                return (long) PREFIX_WORDS.size() * CORE_WORDS.size() * SUFFIX_WORDS.size();
            case CORE_CORE_SUFFIX:
                return (long) CORE_WORDS.size() * (CORE_WORDS.size() - 1) * SUFFIX_WORDS.size();
            case CORE_DIGIT_SUFFIX:
                // 假设数字字母组合有10000种可能
                return (long) CORE_WORDS.size() * 10000 * SUFFIX_WORDS.size();
            case LOCATION_CORE_SUFFIX:
                return (long) LOCATION_WORDS.size() * CORE_WORDS.size() * SUFFIX_WORDS.size();
            case RANDOM:
                // 所有模板的总和
                return calculateTheoricalCombinations(NameTemplate.PREFIX_CORE_SUFFIX) +
                       calculateTheoricalCombinations(NameTemplate.CORE_CORE_SUFFIX) +
                       calculateTheoricalCombinations(NameTemplate.CORE_DIGIT_SUFFIX) +
                       calculateTheoricalCombinations(NameTemplate.LOCATION_CORE_SUFFIX);
            default:
                return 0;
        }
    }
}