package cn.org.nifa.bxcredit.generator.validator;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.org.nifa.bxcredit.generator.model.CreditGuaranteeInsuranceData;

/**
 * 信用保证保险数据规则校验器
 * 根据《信用保证保险类业务数据规则及操作步骤》文档实现
 * 
 * <AUTHOR> Team
 */
public class CreditGuaranteeInsuranceRuleValidator implements DataValidator<CreditGuaranteeInsuranceData> {

    private static final Logger logger = LoggerFactory.getLogger(CreditGuaranteeInsuranceRuleValidator.class);
    
    // 自然人证件类型集合
    private static final Set<String> PERSON_CERT_TYPES = new HashSet<>(
            Arrays.asList("0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "X"));
    
    // 企业证件类型集合
    private static final Set<String> ENTERPRISE_CERT_TYPES = new HashSet<>(
            Arrays.asList("a", "b"));
    
    // 企业法人证件类型集合
    private static final Set<String> LEGAL_PERSON_CERT_TYPES = new HashSet<>(
            Arrays.asList("0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "X"));
    
    // 保险业务种类集合
    private static final Set<String> INSURANCE_BUSINESS_TYPES = new HashSet<>(
            Arrays.asList("01", "02", "03", "99"));
    
    // 保险方式集合
    private static final Set<String> INSURANCE_METHODS = new HashSet<>(
            Arrays.asList("1", "2"));

    @Override
    public ValidationResult validate(CreditGuaranteeInsuranceData data) {
        List<String> errors = new ArrayList<>();
        
        // 1. 人员标识信息段校验
        validatePersonInfo(data, errors);
        
        // 2. 业务标识信息段校验
        validateBusinessInfo(data, errors);
        
        // 3. 根据数据类型校验相应的业务信息段
        switch (data.getDataType()) {
            case "UNDERWRITING":
                validateUnderwritingInfo(data, errors);
                break;
            case "COMPENSATION":
                validateCompensationInfo(data, errors);
                break;
            case "RECOVERY":
                validateRecoveryInfo(data, errors);
                break;
            default:
                errors.add("无效的数据类型: " + data.getDataType());
        }
        
        return new ValidationResult(errors.isEmpty(), errors);
    }

    @Override
    public List<ValidationResult> validateBatch(List<CreditGuaranteeInsuranceData> dataList) {
        List<ValidationResult> results = new ArrayList<>();
        
        // 校验每条数据
        for (CreditGuaranteeInsuranceData data : dataList) {
            results.add(validate(data));
        }
        
        // 校验文件级别的规则
        validateFileRules(dataList, results);
        
        return results;
    }
    
    /**
     * 校验人员标识信息段
     */
    private void validatePersonInfo(CreditGuaranteeInsuranceData data, List<String> errors) {
        // 被保证人类型校验
        if (data.getGuaranteedPersonType() == null || data.getGuaranteedPersonType().isEmpty()) {
            errors.add("被保证人类型不能为空");
        } else if (!("1".equals(data.getGuaranteedPersonType()) || "2".equals(data.getGuaranteedPersonType()))) {
            errors.add("被保证人类型必须为1(企业或其他组织)或2(自然人)");
        }
        
        // 被保证人名称校验
        if (data.getGuaranteedPersonName() == null || data.getGuaranteedPersonName().isEmpty()) {
            errors.add("被保证人名称不能为空");
        } else if (data.getGuaranteedPersonName().length() > 64) {
            errors.add("被保证人名称长度不能超过64个字符");
        }
        
        // 被保证人证件类型校验
        if (data.getGuaranteedPersonCertType() == null || data.getGuaranteedPersonCertType().isEmpty()) {
            errors.add("被保证人证件类型不能为空");
        } else {
            // 根据被保证人类型校验证件类型
            if ("1".equals(data.getGuaranteedPersonType())) {
                // 企业或其他组织
                if (!ENTERPRISE_CERT_TYPES.contains(data.getGuaranteedPersonCertType())) {
                    errors.add("企业被保证人证件类型必须为a(组织机构代码)或b(社会信用代码)");
                }
            } else if ("2".equals(data.getGuaranteedPersonType())) {
                // 自然人
                if (!PERSON_CERT_TYPES.contains(data.getGuaranteedPersonCertType())) {
                    errors.add("自然人被保证人证件类型必须为0~9、A、B、C或X");
                }
            }
        }
        
        // 被保证人证件号码校验
        if (data.getGuaranteedPersonCertNumber() == null || data.getGuaranteedPersonCertNumber().isEmpty()) {
            errors.add("被保证人证件号码不能为空");
        } else if (data.getGuaranteedPersonCertNumber().length() != 18) {
            errors.add("被保证人证件号码长度必须为18位");
        } else if ("0".equals(data.getGuaranteedPersonCertType())) {
            // 身份证号码校验
            String idCard = data.getGuaranteedPersonCertNumber();
            char lastChar = idCard.charAt(17);
            if (lastChar == 'x') {
                errors.add("身份证号码最后一位小写x应该用大写X替代");
            } else if (!Character.isDigit(lastChar) && lastChar != 'X') {
                errors.add("身份证号码最后一位只能为0-9的数字或大写字母X");
            }
        }
        
        // 企业法人信息校验（当被保证人类型为企业时）
        if ("1".equals(data.getGuaranteedPersonType())) {
            // 企业法人姓名校验
            if (data.getLegalPersonName() == null || data.getLegalPersonName().isEmpty()) {
                errors.add("企业被保证人的企业法人姓名为必填项");
            } else if (data.getLegalPersonName().length() > 64) {
                errors.add("企业法人姓名长度不能超过64个字符");
            }
            
            // 企业法人证件类型校验
            if (data.getLegalPersonCertType() == null || data.getLegalPersonCertType().isEmpty()) {
                errors.add("企业被保证人的企业法人证件类型为必填项");
            } else if (!LEGAL_PERSON_CERT_TYPES.contains(data.getLegalPersonCertType())) {
                errors.add("企业法人证件类型必须为0~9、A、B、C或X");
            }
            
            // 企业法人证件号码校验
            if (data.getLegalPersonCertNumber() == null || data.getLegalPersonCertNumber().isEmpty()) {
                errors.add("企业被保证人的企业法人证件号码为必填项");
            } else if (data.getLegalPersonCertNumber().length() != 18) {
                errors.add("企业法人证件号码长度必须为18位");
            } else if ("0".equals(data.getLegalPersonCertType())) {
                // 身份证号码校验
                String idCard = data.getLegalPersonCertNumber();
                char lastChar = idCard.charAt(17);
                if (lastChar == 'x') {
                    errors.add("企业法人身份证号码最后一位小写x应该用大写X替代");
                } else if (!Character.isDigit(lastChar) && lastChar != 'X') {
                    errors.add("企业法人身份证号码最后一位只能为0-9的数字或大写字母X");
                }
            }
        }
    }
    
    /**
     * 校验业务标识信息段
     */
    private void validateBusinessInfo(CreditGuaranteeInsuranceData data, List<String> errors) {
        // 业务发生机构校验
        if (data.getBusinessInstitution() == null || data.getBusinessInstitution().isEmpty()) {
            errors.add("业务发生机构不能为空");
        } else if (data.getBusinessInstitution().length() != 9 && data.getBusinessInstitution().length() != 18) {
            errors.add("业务发生机构长度必须为9位(组织机构代码)或18位(统一社会信用代码)");
        }
        
        // 保单编号校验
        if (data.getPolicyNumber() == null || data.getPolicyNumber().isEmpty()) {
            errors.add("保单编号不能为空");
        } else if (data.getPolicyNumber().length() > 64) {
            errors.add("保单编号长度不能超过64个字符");
        } else if (!data.getPolicyNumber().startsWith("POL")) {
            errors.add("保单编号格式必须为POL+数字");
        }
    }
    
    /**
     * 校验承保信息段
     */
    private void validateUnderwritingInfo(CreditGuaranteeInsuranceData data, List<String> errors) {
        // 保险业务种类校验
        if (data.getInsuranceBusinessType() == null || data.getInsuranceBusinessType().isEmpty()) {
            errors.add("保险业务种类不能为空");
        } else if (!INSURANCE_BUSINESS_TYPES.contains(data.getInsuranceBusinessType())) {
            errors.add("保险业务种类必须为01(信用保证保险)、02(国内贸易信用保险)、03(个人消费信用保险)或99(其他)");
        }
        
        // 保险方式校验
        if (data.getInsuranceMethod() == null || data.getInsuranceMethod().isEmpty()) {
            errors.add("保险方式不能为空");
        } else if (!INSURANCE_METHODS.contains(data.getInsuranceMethod())) {
            errors.add("保险方式必须为1(保证)或2(信用)");
        }
        
        // 保险起始日期校验
        if (data.getInsuranceStartDate() == null || data.getInsuranceStartDate().isEmpty()) {
            errors.add("保险起始日期不能为空");
        } else if (data.getInsuranceStartDate().length() != 8) {
            errors.add("保险起始日期长度必须为8位");
        } else {
            try {
                LocalDate startDate = LocalDate.parse(data.getInsuranceStartDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
                LocalDate currentDate = LocalDate.now();
                
                if (startDate.isAfter(currentDate)) {
                    errors.add("保险起始日期不能晚于当前日期");
                }
                
                // 保险到期日期校验
                if (data.getInsuranceEndDate() == null || data.getInsuranceEndDate().isEmpty()) {
                    errors.add("保险到期日期不能为空");
                } else if (data.getInsuranceEndDate().length() != 8) {
                    errors.add("保险到期日期长度必须为8位");
                } else {
                    try {
                        LocalDate endDate = LocalDate.parse(data.getInsuranceEndDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
                        
                        if (startDate.isAfter(endDate)) {
                            errors.add("保险起始日期不能晚于保险到期日期");
                        }
                    } catch (DateTimeParseException e) {
                        errors.add("保险到期日期格式无效，应为YYYYMMDD");
                    }
                }
            } catch (DateTimeParseException e) {
                errors.add("保险起始日期格式无效，应为YYYYMMDD");
            }
        }
        
        // 保险金额校验
        if (data.getInsuranceAmount() == null || data.getInsuranceAmount().isEmpty()) {
            errors.add("保险金额不能为空");
        } else {
            try {
                int amount = Integer.parseInt(data.getInsuranceAmount());
                if (amount <= 0) {
                    errors.add("保险金额必须为正整数");
                } else if (data.getInsuranceAmount().length() > 10) {
                    errors.add("保险金额长度不能超过10位");
                }
            } catch (NumberFormatException e) {
                errors.add("保险金额必须为整数");
            }
        }
    }
    
    /**
     * 校验代偿信息段
     */
    private void validateCompensationInfo(CreditGuaranteeInsuranceData data, List<String> errors) {
        // 代偿日期校验
        if (data.getCompensationDate() == null || data.getCompensationDate().isEmpty()) {
            errors.add("代偿日期不能为空");
        } else if (data.getCompensationDate().length() != 8) {
            errors.add("代偿日期长度必须为8位");
        } else {
            try {
                LocalDate.parse(data.getCompensationDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
            } catch (DateTimeParseException e) {
                errors.add("代偿日期格式无效，应为YYYYMMDD");
            }
        }
        
        // 代偿金额校验
        if (data.getCompensationAmount() == null || data.getCompensationAmount().isEmpty()) {
            errors.add("代偿金额不能为空");
        } else {
            try {
                int amount = Integer.parseInt(data.getCompensationAmount());
                if (amount <= 0) {
                    errors.add("代偿金额必须为正整数");
                } else if (data.getCompensationAmount().length() > 10) {
                    errors.add("代偿金额长度不能超过10位");
                }
            } catch (NumberFormatException e) {
                errors.add("代偿金额必须为整数");
            }
        }
    }
    
    /**
     * 校验追偿信息段
     */
    private void validateRecoveryInfo(CreditGuaranteeInsuranceData data, List<String> errors) {
        // 追偿日期校验
        if (data.getRecoveryDate() == null || data.getRecoveryDate().isEmpty()) {
            errors.add("追偿日期不能为空");
        } else if (data.getRecoveryDate().length() != 8) {
            errors.add("追偿日期长度必须为8位");
        } else {
            try {
                LocalDate.parse(data.getRecoveryDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
            } catch (DateTimeParseException e) {
                errors.add("追偿日期格式无效，应为YYYYMMDD");
            }
        }
        
        // 追偿金额校验
        if (data.getRecoveryAmount() == null || data.getRecoveryAmount().isEmpty()) {
            errors.add("追偿金额不能为空");
        } else {
            try {
                int amount = Integer.parseInt(data.getRecoveryAmount());
                if (amount <= 0) {
                    errors.add("追偿金额必须为正整数");
                } else if (data.getRecoveryAmount().length() > 10) {
                    errors.add("追偿金额长度不能超过10位");
                }
            } catch (NumberFormatException e) {
                errors.add("追偿金额必须为整数");
            }
        }
    }
    
    /**
     * 校验文件级别的规则
     */
    private void validateFileRules(List<CreditGuaranteeInsuranceData> dataList, List<ValidationResult> results) {
        // 检查承保信息文件中的唯一性约束
        Set<String> underwritingKeys = new HashSet<>();
        // 检查代偿信息文件中的唯一性约束
        Set<String> compensationKeys = new HashSet<>();
        // 检查追偿信息文件中的唯一性约束
        Set<String> recoveryKeys = new HashSet<>();
        
        for (int i = 0; i < dataList.size(); i++) {
            CreditGuaranteeInsuranceData data = dataList.get(i);
            ValidationResult result = results.get(i);
            
            if (!result.isValid()) {
                continue; // 跳过已经有错误的数据
            }
            
            List<String> errors = new ArrayList<>(result.getErrors());
            
            switch (data.getDataType()) {
                case "UNDERWRITING":
                    // 承保信息唯一性校验：业务发生机构+保单编号
                    String underwritingKey = data.getBusinessInstitution() + "|" + data.getPolicyNumber();
                    if (underwritingKeys.contains(underwritingKey)) {
                        errors.add("承保信息文件中存在重复的业务发生机构+保单编号组合");
                    } else {
                        underwritingKeys.add(underwritingKey);
                    }
                    break;
                    
                case "COMPENSATION":
                    // 代偿信息唯一性校验：业务发生机构+保单编号+代偿日期
                    String compensationKey = data.getBusinessInstitution() + "|" + data.getPolicyNumber() + "|" + data.getCompensationDate();
                    if (compensationKeys.contains(compensationKey)) {
                        errors.add("代偿信息文件中存在重复的业务发生机构+保单编号+代偿日期组合");
                    } else {
                        compensationKeys.add(compensationKey);
                    }
                    break;
                    
                case "RECOVERY":
                    // 追偿信息唯一性校验：业务发生机构+保单编号+追偿日期
                    String recoveryKey = data.getBusinessInstitution() + "|" + data.getPolicyNumber() + "|" + data.getRecoveryDate();
                    if (recoveryKeys.contains(recoveryKey)) {
                        errors.add("追偿信息文件中存在重复的业务发生机构+保单编号+追偿日期组合");
                    } else {
                        recoveryKeys.add(recoveryKey);
                    }
                    break;
            }
            
            // 更新结果
            if (!errors.isEmpty()) {
                results.set(i, new ValidationResult(false, errors));
            }
        }
    }
}