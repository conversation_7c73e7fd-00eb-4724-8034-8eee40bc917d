package cn.org.nifa.bxcredit.generator.generator;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.org.nifa.bxcredit.generator.model.PersonalLoanData;
import cn.org.nifa.bxcredit.generator.util.BusinessCodeGenerator;
import cn.org.nifa.bxcredit.generator.util.DateUtils;
import cn.org.nifa.bxcredit.generator.util.IdCardGenerator;
import cn.org.nifa.bxcredit.generator.util.ValidationUtils;

/**
 * 个人数据生成器
 * 
 * <AUTHOR> Team
 */
public class PersonalDataGenerator implements DataGenerator<PersonalLoanData> {
    
    private static final Logger logger = LoggerFactory.getLogger(PersonalDataGenerator.class);
    private static final Random RANDOM = new Random();
    
    @Override
    public PersonalLoanData generatePositiveData() {
        return PersonalLoanData.builder()
                .name(generateName())
                .certificateType(generateCertificateType())
                .certificateNumber(generateCertificateNumber("0"))
                .businessInstitution(generateBusinessInstitution())
                .businessNumber(BusinessCodeGenerator.generateBusinessNumber())
                .businessType(generateBusinessType())
                .businessCategory(generateBusinessCategory())
                .openDate(generateOpenDate())
                .dueDate(generateDueDate("2"))
                .creditLimit(generateCreditLimit())
                .businessDate(generateBusinessDate())
                .repaymentStatus(generateRepaymentStatus())
                .balance(generateBalance())
                .overdueAmount(generateOverdueAmount("N"))
                .build();
    }
    
    @Override
    public List<PersonalLoanData> generatePositiveDataBatch(int count) {
        List<PersonalLoanData> dataList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            dataList.add(generatePositiveData());
        }
        logger.info("生成{}条正向测试数据", count);
        return dataList;
    }
    
    @Override
    public PersonalLoanData generateNegativeData(String ruleViolation) {
        PersonalLoanData.Builder builder = PersonalLoanData.builder();
        
        switch (ruleViolation) {
            case "NAME_EMPTY":
                builder.name(""); // 空姓名
                break;
            case "NAME_TOO_LONG":
                builder.name("A".repeat(31)); // 超长姓名
                break;
            case "CERT_TYPE_INVALID":
                builder.certificateType("Z"); // 无效证件类型
                break;
            case "CERT_NUMBER_INVALID":
                builder.certificateType("0")
                       .certificateNumber(IdCardGenerator.generateInvalidIdCard("LENGTH"));
                break;
            case "BUSINESS_NUMBER_INVALID":
                builder.businessNumber(BusinessCodeGenerator.generateInvalidBusinessNumber("NO_PREFIX"));
                break;
            case "BUSINESS_TYPE_INVALID":
                builder.businessType("9"); // 无效业务类型
                break;
            case "BUSINESS_CATEGORY_INVALID":
                builder.businessCategory("88"); // 无效业务种类
                break;
            case "DATE_INVALID":
                builder.openDate("20230230"); // 无效日期
                break;
            case "DATE_ORDER_INVALID":
                builder.openDate("20231201")
                       .dueDate("20231101"); // 开户日期晚于到期日期
                break;
            case "BALANCE_NEGATIVE":
                builder.balance("-1000"); // 负数余额
                break;
            case "OVERDUE_AMOUNT_INVALID":
                builder.repaymentStatus("C")
                       .overdueAmount("1000"); // 结清状态但逾期金额不为0
                break;
            case "BALANCE_ZERO_INVALID":
                builder.repaymentStatus("C")
                       .balance("1000"); // 结清状态但余额不为0
                break;
            default:
                return generatePositiveData();
        }
        
        // 填充其他必要字段
        fillDefaultFields(builder);
        
        return builder.build();
    }
    
    @Override
    public List<PersonalLoanData> generateAllNegativeData() {
        List<PersonalLoanData> negativeDataList = new ArrayList<>();
        
        String[] ruleViolations = {
            "NAME_EMPTY",
            "NAME_TOO_LONG",
            "CERT_TYPE_INVALID",
            "CERT_NUMBER_INVALID", 
            "BUSINESS_NUMBER_INVALID",
            "BUSINESS_TYPE_INVALID",
            "BUSINESS_CATEGORY_INVALID",
            "DATE_INVALID",
            "DATE_ORDER_INVALID",
            "BALANCE_NEGATIVE",
            "OVERDUE_AMOUNT_INVALID",
            "BALANCE_ZERO_INVALID"
        };
        
        for (String violation : ruleViolations) {
            negativeDataList.add(generateNegativeData(violation));
        }
        
        logger.info("生成{}条反向测试数据", negativeDataList.size());
        return negativeDataList;
    }
    
    /**
     * 生成证件类型
     */
    private String generateCertificateType() {
        return ValidationUtils.PERSONAL_CERT_TYPES.get(
            RANDOM.nextInt(ValidationUtils.PERSONAL_CERT_TYPES.size())
        );
    }
    
    /**
     * 生成证件号码
     */
    private String generateCertificateNumber(String certType) {
        if ("0".equals(certType)) {
            return IdCardGenerator.generateRandomIdCard();
        }
        // 简化处理，其他证件类型生成固定格式
        return "E12345678";
    }
    
    /**
     * 生成业务发生机构
     */
    private String generateBusinessInstitution() {
        return RANDOM.nextBoolean() ? 
            BusinessCodeGenerator.generateOrgCode() : 
            BusinessCodeGenerator.generateSocialCreditCode();
    }
    
    /**
     * 生成业务类型
     */
    private String generateBusinessType() {
        return ValidationUtils.BUSINESS_TYPES.get(
            RANDOM.nextInt(ValidationUtils.BUSINESS_TYPES.size())
        );
    }
    
    /**
     * 生成业务种类
     */
    private String generateBusinessCategory() {
        return ValidationUtils.PERSONAL_BUSINESS_CATEGORIES.get(
            RANDOM.nextInt(ValidationUtils.PERSONAL_BUSINESS_CATEGORIES.size())
        );
    }
    
    /**
     * 生成开户日期
     */
    private String generateOpenDate() {
        return DateUtils.getDateBefore(RANDOM.nextInt(365) + 1);
    }
    
    /**
     * 生成到期日期
     */
    private String generateDueDate(String businessType) {
        if ("4".equals(businessType)) {
            return "20991231"; // 循环业务固定到期日期
        }
        return DateUtils.getDateAfter(RANDOM.nextInt(365) + 30);
    }
    
    /**
     * 生成授信额度
     */
    private String generateCreditLimit() {
        return String.valueOf((RANDOM.nextInt(1000) + 1) * 1000);
    }
    
    /**
     * 生成业务发生日期
     */
    private String generateBusinessDate() {
        return DateUtils.getCurrentDate();
    }
    
    /**
     * 生成还款状态
     */
    private String generateRepaymentStatus() {
        return ValidationUtils.REPAYMENT_STATUS.get(
            RANDOM.nextInt(ValidationUtils.REPAYMENT_STATUS.size())
        );
    }
    
    /**
     * 生成余额
     */
    private String generateBalance() {
        return String.valueOf(RANDOM.nextInt(100000));
    }
    
    /**
     * 生成逾期金额
     */
    private String generateOverdueAmount(String repaymentStatus) {
        if ("C".equals(repaymentStatus) || "*".equals(repaymentStatus) || "N".equals(repaymentStatus)) {
            return "0";
        }
        return String.valueOf(RANDOM.nextInt(10000));
    }
    
    /**
     * 生成姓名
     */
    private String generateName() {
        String[] surnames = {"王", "李", "张", "刘", "陈", "杨", "黄", "赵", "吴", "周"};
        String[] names = {"伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军"};
        return surnames[RANDOM.nextInt(surnames.length)] + names[RANDOM.nextInt(names.length)];
    }
    
    /**
     * 填充默认字段
     */
    private void fillDefaultFields(PersonalLoanData.Builder builder) {
        builder.name(builder.build().getName() != null ?
                builder.build().getName() : generateName())
               .certificateType(builder.build().getCertificateType() != null ? 
                builder.build().getCertificateType() : "0")
               .certificateNumber(builder.build().getCertificateNumber() != null ?
                       builder.build().getCertificateNumber() : IdCardGenerator.generateRandomIdCard())
               .businessInstitution(generateBusinessInstitution())
               .businessNumber(builder.build().getBusinessNumber() != null ?
                       builder.build().getBusinessNumber() : BusinessCodeGenerator.generateBusinessNumber())
               .businessType(builder.build().getBusinessType() != null ?
                       builder.build().getBusinessType() : "2")
               .businessCategory(builder.build().getBusinessCategory() != null ?
                       builder.build().getBusinessCategory() : "11")
               .openDate(builder.build().getOpenDate() != null ?
                       builder.build().getOpenDate() : generateOpenDate())
               .dueDate(builder.build().getDueDate() != null ?
                       builder.build().getDueDate() : generateDueDate("2"))
               .creditLimit(generateCreditLimit())
               .businessDate(generateBusinessDate())
               .repaymentStatus(builder.build().getRepaymentStatus() != null ?
                       builder.build().getRepaymentStatus() : "N")
               .balance(builder.build().getBalance() != null ?
                       builder.build().getBalance() : generateBalance())
               .overdueAmount(builder.build().getOverdueAmount() != null ?
                       builder.build().getOverdueAmount() : "0");
    }
}