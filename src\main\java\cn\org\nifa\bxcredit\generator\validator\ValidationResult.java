package cn.org.nifa.bxcredit.generator.validator;

import java.util.List;

/**
 * 校验结果类
 * 
 * <AUTHOR> Team
 */
public class ValidationResult {
    
    private boolean valid;
    private List<String> errors;
    
    public ValidationResult() {}
    
    public ValidationResult(boolean valid, List<String> errors) {
        this.valid = valid;
        this.errors = errors;
    }
    
    public boolean isValid() {
        return valid;
    }
    
    public void setValid(boolean valid) {
        this.valid = valid;
    }
    
    public List<String> getErrors() {
        return errors;
    }
    
    public void setErrors(List<String> errors) {
        this.errors = errors;
    }
    
    @Override
    public String toString() {
        return "ValidationResult{" +
                "valid=" + valid +
                ", errors=" + errors +
                '}';
    }
}