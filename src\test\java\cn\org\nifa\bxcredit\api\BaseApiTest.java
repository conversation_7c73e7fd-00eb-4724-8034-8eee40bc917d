package cn.org.nifa.bxcredit.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeClass;

import io.restassured.RestAssured;
import io.restassured.config.HttpClientConfig;
import io.restassured.config.RestAssuredConfig;
import io.restassured.filter.log.RequestLoggingFilter;
import io.restassured.filter.log.ResponseLoggingFilter;
import io.restassured.specification.RequestSpecification;

/**
 * API测试基类
 * 提供通用的测试配置和工具方法
 */
public abstract class BaseApiTest {
    
    protected static final Logger logger = LoggerFactory.getLogger(BaseApiTest.class);
    protected RequestSpecification requestSpec;
    
    @BeforeClass
    public void setupBase() {
        // 配置REST-assured日志
        RestAssured.filters(new RequestLoggingFilter(), new ResponseLoggingFilter());
        
        // 配置默认超时
        RestAssured.config = RestAssuredConfig.config()
                .httpClient(HttpClientConfig.httpClientConfig()
                        .setParam("http.connection.timeout", 30000)
                        .setParam("http.socket.timeout", 30000));
        
        // 配置SSL信任所有证书（仅在测试环境使用）
        RestAssured.useRelaxedHTTPSValidation();
    }
    
    /**
     * 打印测试信息
     * 
     * @param testName 测试名称
     * @param description 测试描述
     */
    protected void printTestInfo(String testName, String description) {
        logger.info("\n===================================");
        logger.info("Test: {}" , testName);
        logger.info("Description: {}", description);
        logger.info("===================================");
    }
}