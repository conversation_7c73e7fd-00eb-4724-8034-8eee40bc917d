package cn.org.nifa.bxcredit.generator.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 姓名生成工具类
 * 
 * 支持生成多种格式的中文和英文姓名：
 * - 中文两字姓名（单姓+单字名）
 * - 中文三字姓名（单姓+双字名）
 * - 中文复姓四字姓名（复姓+双字名）
 * - 英文姓名（First Name + Last Name）
 * 
 * <AUTHOR> Team
 */
public class NameGenerator {
    
    // 数据存储集合
    private static final List<String> CHINESE_SINGLE_SURNAMES = new ArrayList<>();
    private static final List<String> CHINESE_COMPOUND_SURNAMES = new ArrayList<>();
    private static final List<String> CHINESE_SINGLE_NAMES = new ArrayList<>();
    private static final List<String> CHINESE_DOUBLE_NAMES = new ArrayList<>();
    private static final List<String> ENGLISH_FIRST_NAMES = new ArrayList<>();
    private static final List<String> ENGLISH_LAST_NAMES = new ArrayList<>();
    
    // 随机数生成器
    private static final Random RANDOM = new Random();
    
    // 数据文件路径
    private static final String CHINESE_SINGLE_SURNAMES_FILE = "/data/chinese_single_surnames.txt";
    private static final String CHINESE_COMPOUND_SURNAMES_FILE = "/data/chinese_compound_surnames.txt";
    private static final String CHINESE_SINGLE_NAMES_FILE = "/data/chinese_single_names.txt";
    private static final String CHINESE_DOUBLE_NAMES_FILE = "/data/chinese_double_names.txt";
    private static final String ENGLISH_FIRST_NAMES_FILE = "/data/english_first_names.txt";
    private static final String ENGLISH_LAST_NAMES_FILE = "/data/english_last_names.txt";
    
    // 静态初始化块，加载所有姓名数据
    static {
        try {
            loadDataFromFile(CHINESE_SINGLE_SURNAMES_FILE, CHINESE_SINGLE_SURNAMES);
            loadDataFromFile(CHINESE_COMPOUND_SURNAMES_FILE, CHINESE_COMPOUND_SURNAMES);
            loadDataFromFile(CHINESE_SINGLE_NAMES_FILE, CHINESE_SINGLE_NAMES);
            loadDataFromFile(CHINESE_DOUBLE_NAMES_FILE, CHINESE_DOUBLE_NAMES);
            loadDataFromFile(ENGLISH_FIRST_NAMES_FILE, ENGLISH_FIRST_NAMES);
            loadDataFromFile(ENGLISH_LAST_NAMES_FILE, ENGLISH_LAST_NAMES);
            
            System.out.println("姓名数据加载完成:");
            System.out.println("中文单姓: " + CHINESE_SINGLE_SURNAMES.size() + " 个");
            System.out.println("中文复姓: " + CHINESE_COMPOUND_SURNAMES.size() + " 个");
            System.out.println("中文单字名: " + CHINESE_SINGLE_NAMES.size() + " 个");
            System.out.println("中文双字名: " + CHINESE_DOUBLE_NAMES.size() + " 个");
            System.out.println("英文名: " + ENGLISH_FIRST_NAMES.size() + " 个");
            System.out.println("英文姓: " + ENGLISH_LAST_NAMES.size() + " 个");
            
        } catch (Exception e) {
            throw new RuntimeException("姓名数据加载失败", e);
        }
    }
    
    /**
     * 私有构造方法，防止实例化
     */
    private NameGenerator() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    /**
     * 生成中文两字姓名（单姓+单字名）
     * 
     * @return 两字中文姓名
     */
    public static String generateChineseNameTwoChar() {
        String surname = getRandomElement(CHINESE_SINGLE_SURNAMES);
        String name = getRandomElement(CHINESE_SINGLE_NAMES);
        return surname + name;
    }
    
    /**
     * 生成中文三字姓名（单姓+双字名）
     * 
     * @return 三字中文姓名
     */
    public static String generateChineseNameThreeChar() {
        String surname = getRandomElement(CHINESE_SINGLE_SURNAMES);
        String name = getRandomElement(CHINESE_DOUBLE_NAMES);
        return surname + name;
    }
    
    /**
     * 生成中文复姓四字姓名（复姓+双字名）
     * 
     * @return 四字中文姓名
     */
    public static String generateChineseNameCompound() {
        String surname = getRandomElement(CHINESE_COMPOUND_SURNAMES);
        String name = getRandomElement(CHINESE_DOUBLE_NAMES);
        return surname + name;
    }
    
    /**
     * 生成英文姓名（First Name + Last Name）
     * 
     * @return 英文姓名
     */
    public static String generateEnglishName() {
        String firstName = getRandomElement(ENGLISH_FIRST_NAMES);
        String lastName = getRandomElement(ENGLISH_LAST_NAMES);
        return firstName + " " + lastName;
    }
    
    /**
     * 随机生成中文姓名（随机选择两字、三字或复姓四字）
     * 
     * @return 中文姓名
     */
    public static String generateRandomChineseName() {
        int type = RANDOM.nextInt(3);
        switch (type) {
            case 0:
                return generateChineseNameTwoChar();
            case 1:
                return generateChineseNameThreeChar();
            case 2:
                return generateChineseNameCompound();
            default:
                return generateChineseNameTwoChar();
        }
    }
    
    /**
     * 使用线程安全的随机数生成器生成中文两字姓名
     * 适用于多线程环境
     * 
     * @return 两字中文姓名
     */
    public static String generateChineseNameTwoCharThreadSafe() {
        String surname = getRandomElementThreadSafe(CHINESE_SINGLE_SURNAMES);
        String name = getRandomElementThreadSafe(CHINESE_SINGLE_NAMES);
        return surname + name;
    }
    
    /**
     * 使用线程安全的随机数生成器生成中文三字姓名
     * 适用于多线程环境
     * 
     * @return 三字中文姓名
     */
    public static String generateChineseNameThreeCharThreadSafe() {
        String surname = getRandomElementThreadSafe(CHINESE_SINGLE_SURNAMES);
        String name = getRandomElementThreadSafe(CHINESE_DOUBLE_NAMES);
        return surname + name;
    }
    
    /**
     * 使用线程安全的随机数生成器生成中文复姓四字姓名
     * 适用于多线程环境
     * 
     * @return 四字中文姓名
     */
    public static String generateChineseNameCompoundThreadSafe() {
        String surname = getRandomElementThreadSafe(CHINESE_COMPOUND_SURNAMES);
        String name = getRandomElementThreadSafe(CHINESE_DOUBLE_NAMES);
        return surname + name;
    }
    
    /**
     * 使用线程安全的随机数生成器生成英文姓名
     * 适用于多线程环境
     * 
     * @return 英文姓名
     */
    public static String generateEnglishNameThreadSafe() {
        String firstName = getRandomElementThreadSafe(ENGLISH_FIRST_NAMES);
        String lastName = getRandomElementThreadSafe(ENGLISH_LAST_NAMES);
        return firstName + " " + lastName;
    }
    
    /**
     * 使用线程安全的随机数生成器随机生成中文姓名
     * 适用于多线程环境
     * 
     * @return 中文姓名
     */
    public static String generateRandomChineseNameThreadSafe() {
        int type = ThreadLocalRandom.current().nextInt(3);
        switch (type) {
            case 0:
                return generateChineseNameTwoCharThreadSafe();
            case 1:
                return generateChineseNameThreeCharThreadSafe();
            case 2:
                return generateChineseNameCompoundThreadSafe();
            default:
                return generateChineseNameTwoCharThreadSafe();
        }
    }
    
    /**
     * 从文件加载数据到指定列表
     * 
     * @param filePath 文件路径
     * @param dataList 目标列表
     * @throws IOException 文件读取异常
     */
    private static void loadDataFromFile(String filePath, List<String> dataList) throws IOException {
        try (InputStream inputStream = NameGenerator.class.getResourceAsStream(filePath);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            
            if (inputStream == null) {
                throw new IOException("无法找到文件: " + filePath);
            }
            
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    dataList.add(line);
                }
            }
            
            if (dataList.isEmpty()) {
                throw new IOException("文件为空或无有效数据: " + filePath);
            }
        }
    }
    
    /**
     * 从列表中随机获取一个元素
     * 
     * @param list 源列表
     * @return 随机元素
     * @throws IllegalArgumentException 如果列表为空
     */
    private static String getRandomElement(List<String> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("列表不能为空");
        }
        return list.get(RANDOM.nextInt(list.size()));
    }
    
    /**
     * 使用线程安全的随机数生成器从列表中随机获取一个元素
     * 
     * @param list 源列表
     * @return 随机元素
     * @throws IllegalArgumentException 如果列表为空
     */
    private static String getRandomElementThreadSafe(List<String> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("列表不能为空");
        }
        return list.get(ThreadLocalRandom.current().nextInt(list.size()));
    }
    
    /**
     * 获取中文单姓数量
     * 
     * @return 单姓数量
     */
    public static int getChineseSingleSurnameCount() {
        return CHINESE_SINGLE_SURNAMES.size();
    }
    
    /**
     * 获取中文复姓数量
     * 
     * @return 复姓数量
     */
    public static int getChineseCompoundSurnameCount() {
        return CHINESE_COMPOUND_SURNAMES.size();
    }
    
    /**
     * 获取中文单字名数量
     * 
     * @return 单字名数量
     */
    public static int getChineseSingleNameCount() {
        return CHINESE_SINGLE_NAMES.size();
    }
    
    /**
     * 获取中文双字名数量
     * 
     * @return 双字名数量
     */
    public static int getChineseDoubleNameCount() {
        return CHINESE_DOUBLE_NAMES.size();
    }
    
    /**
     * 获取英文名数量
     * 
     * @return 英文名数量
     */
    public static int getEnglishFirstNameCount() {
        return ENGLISH_FIRST_NAMES.size();
    }
    
    /**
     * 获取英文姓数量
     *
     * @return 英文姓数量
     */
    public static int getEnglishLastNameCount() {
        return ENGLISH_LAST_NAMES.size();
    }

    /**
     * 批量生成姓名数据类型枚举
     */
    public enum NameType {
        CHINESE_TWO_CHAR,      // 中文两字姓名
        CHINESE_THREE_CHAR,    // 中文三字姓名
        CHINESE_COMPOUND,      // 中文复姓四字姓名
        ENGLISH,               // 英文姓名
        CHINESE_RANDOM         // 随机中文姓名
    }

    /**
     * 批量生成姓名到文件
     *
     * @param count 生成数量
     * @param nameType 姓名类型
     * @param outputFilePath 输出文件路径
     * @throws IOException 文件操作异常
     */
    public static void generateNamesToFile(long count, NameType nameType, String outputFilePath) throws IOException {
        Path outputPath = Paths.get(outputFilePath);

        try (BufferedWriter writer = Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8,
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {

            final int batchSize = 10000; // 批量写入大小
            List<String> batch = new ArrayList<>(batchSize);

            for (long i = 0; i < count; i++) {
                String name = generateNameByType(nameType);
                batch.add(name);

                // 批量写入
                if (batch.size() >= batchSize || i == count - 1) {
                    for (String n : batch) {
                        writer.write(n);
                        writer.newLine();
                    }
                    writer.flush();
                    batch.clear();

                    // 进度提示
                    if ((i + 1) % 100000 == 0 || i == count - 1) {
                        System.out.printf("已生成 %d/%d 条姓名数据 (%.2f%%)\n",
                            i + 1, count, (double)(i + 1) / count * 100);
                    }
                }
            }
        }

        System.out.println("姓名数据生成完成，保存至: " + outputFilePath);
    }

    /**
     * 多线程批量生成姓名到文件
     * 适用于大数据量生成（如5000万条）
     *
     * @param count 生成数量
     * @param nameType 姓名类型
     * @param outputFilePath 输出文件路径
     * @param threadCount 线程数量
     * @throws IOException 文件操作异常
     * @throws InterruptedException 线程中断异常
     */
    public static void generateNamesToFileMultiThreaded(long count, NameType nameType,
            String outputFilePath, int threadCount) throws IOException, InterruptedException {

        Path outputPath = Paths.get(outputFilePath);

        // 创建线程安全的队列用于数据传递
        BlockingQueue<String> nameQueue = new LinkedBlockingQueue<>(50000);
        AtomicLong generatedCount = new AtomicLong(0);
        AtomicLong writtenCount = new AtomicLong(0);

        // 创建线程池
        ExecutorService generatorPool = Executors.newFixedThreadPool(threadCount);
        ExecutorService writerPool = Executors.newSingleThreadExecutor();

        // 启动写入线程
        CompletableFuture<Void> writerFuture = CompletableFuture.runAsync(() -> {
            try (BufferedWriter writer = Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8,
                    StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {

                final int batchSize = 10000;
                List<String> batch = new ArrayList<>(batchSize);

                while (writtenCount.get() < count) {
                    String name = nameQueue.poll(1, TimeUnit.SECONDS);
                    if (name != null) {
                        batch.add(name);

                        if (batch.size() >= batchSize || writtenCount.get() + batch.size() >= count) {
                            for (String n : batch) {
                                writer.write(n);
                                writer.newLine();
                            }
                            writer.flush();

                            long written = writtenCount.addAndGet(batch.size());
                            batch.clear();

                            // 进度提示
                            if (written % 100000 == 0 || written >= count) {
                                System.out.printf("已写入 %d/%d 条姓名数据 (%.2f%%)\n",
                                    written, count, (double)written / count * 100);
                            }
                        }
                    }
                }

                // 写入剩余数据
                if (!batch.isEmpty()) {
                    for (String n : batch) {
                        writer.write(n);
                        writer.newLine();
                    }
                    writer.flush();
                    writtenCount.addAndGet(batch.size());
                }

            } catch (Exception e) {
                throw new RuntimeException("写入文件失败", e);
            }
        }, writerPool);

        // 启动生成线程
        long countPerThread = count / threadCount;
        long remainder = count % threadCount;

        List<CompletableFuture<Void>> generatorFutures = new ArrayList<>();

        for (int i = 0; i < threadCount; i++) {
            long threadCount_local = countPerThread + (i < remainder ? 1 : 0);

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (long j = 0; j < threadCount_local; j++) {
                    try {
                        String name = generateNameByTypeThreadSafe(nameType);
                        nameQueue.put(name);

                        long generated = generatedCount.incrementAndGet();
                        if (generated % 100000 == 0) {
                            System.out.printf("已生成 %d/%d 条姓名数据\n", generated, count);
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("生成线程被中断", e);
                    }
                }
            }, generatorPool);

            generatorFutures.add(future);
        }

        // 等待所有生成线程完成
        CompletableFuture.allOf(generatorFutures.toArray(new CompletableFuture[0])).join();

        // 等待写入线程完成
        writerFuture.join();

        // 关闭线程池
        generatorPool.shutdown();
        writerPool.shutdown();

        System.out.println("多线程姓名数据生成完成，保存至: " + outputFilePath);
    }

    /**
     * 根据类型生成姓名
     *
     * @param nameType 姓名类型
     * @return 生成的姓名
     */
    private static String generateNameByType(NameType nameType) {
        switch (nameType) {
            case CHINESE_TWO_CHAR:
                return generateChineseNameTwoChar();
            case CHINESE_THREE_CHAR:
                return generateChineseNameThreeChar();
            case CHINESE_COMPOUND:
                return generateChineseNameCompound();
            case ENGLISH:
                return generateEnglishName();
            case CHINESE_RANDOM:
                return generateRandomChineseName();
            default:
                return generateChineseNameTwoChar();
        }
    }

    /**
     * 根据类型生成姓名（线程安全版本）
     *
     * @param nameType 姓名类型
     * @return 生成的姓名
     */
    private static String generateNameByTypeThreadSafe(NameType nameType) {
        switch (nameType) {
            case CHINESE_TWO_CHAR:
                return generateChineseNameTwoCharThreadSafe();
            case CHINESE_THREE_CHAR:
                return generateChineseNameThreeCharThreadSafe();
            case CHINESE_COMPOUND:
                return generateChineseNameCompoundThreadSafe();
            case ENGLISH:
                return generateEnglishNameThreadSafe();
            case CHINESE_RANDOM:
                return generateRandomChineseNameThreadSafe();
            default:
                return generateChineseNameTwoCharThreadSafe();
        }
    }
}
