package cn.org.nifa.bxcredit.utils;

import java.io.IOException;
import java.util.Map;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * JSON工具类
 * 使用Jackson库实现高性能JSON处理
 * 
 * <AUTHOR> Team
 */
public class JsonUtils {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 将Map转换为JSON字符串
     * 
     * @param map 输入Map
     * @return JSON字符串
     */
    public static String toJson(Map<String, String> map) {
        try {
            return objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert map to JSON", e);
        }
    }
    
    /**
     * 将对象转换为JSON字符串
     * 
     * @param object 任意对象
     * @return JSON字符串
     */
    public static String toJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert object to JSON", e);
        }
    }
    
    /**
     * 将JSON字符串转换为Map
     * 
     * @param json JSON字符串
     * @return Map对象
     */
    public static Map<String, Object> toMap(String json) {
        try {
            return objectMapper.readValue(json, 
                    objectMapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class));
        } catch (IOException e) {
            throw new RuntimeException("Failed to convert JSON to map", e);
        }
    }
    
    /**
     * 将JSON字符串转换为指定类型的对象
     * 
     * @param <T> 目标类型
     * @param json JSON字符串
     * @param clazz 目标类
     * @return 转换后的对象
     */
    public static <T> T toObject(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            throw new RuntimeException("Failed to convert JSON to object", e);
        }
    }
    
    /**
     * 创建一个空的JSON对象
     * 
     * @return ObjectNode实例
     */
    public static ObjectNode createObjectNode() {
        return objectMapper.createObjectNode();
    }
    
    /**
     * 解析JSON字符串为JsonNode
     * 
     * @param json JSON字符串
     * @return JsonNode对象
     */
    public static JsonNode parseJson(String json) {
        try {
            return objectMapper.readTree(json);
        } catch (IOException e) {
            throw new RuntimeException("Failed to parse JSON", e);
        }
    }
    
    /**
     * 获取ObjectMapper实例
     * 用于高级定制化操作
     * 
     * @return ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }
}