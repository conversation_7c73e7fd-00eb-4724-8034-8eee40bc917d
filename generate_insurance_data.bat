@echo off
REM 信用保证保险测试数据生成脚本

REM 设置Java环境
set JAVA_HOME=%JAVA_HOME%
set PATH=%JAVA_HOME%\bin;%PATH%

REM 设置NIFA环境变量
set NIFA_ENV=dev
REM 设置机构代码（根据实际情况修改）
set NIFA_ORG_CODE=123456789

REM 解析命令行参数
set ARGS=

:parse
if "%~1"=="" goto :execute
set ARGS=%ARGS% %1
shift
goto :parse

:execute
REM 执行数据生成命令
echo 开始生成信用保证保险测试数据...
java -cp target\bxcredit-1.0.0.jar cn.org.nifa.bxcredit.generator.CreditGuaranteeInsuranceDataGeneratorCLI %ARGS%

if %ERRORLEVEL% NEQ 0 (
    echo 数据生成失败，错误代码: %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)

echo 数据生成完成