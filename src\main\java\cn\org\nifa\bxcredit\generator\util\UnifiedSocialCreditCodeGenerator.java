package cn.org.nifa.bxcredit.generator.util;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 统一社会信用代码生成工具类
 * 
 * 根据GB 32100-2015标准生成符合校验规范的18位统一社会信用代码
 * 支持：
 * - 单个代码生成
 * - 批量代码生成
 * - 代码校验
 * - 组织机构代码提取
 * 
 * <AUTHOR> Team
 */
public class UnifiedSocialCreditCodeGenerator {
    
    // 加权因子表（GB 32100-2015标准）
    private static final int[] WEIGHT_FACTORS = {
        1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 9, 27, 19, 26, 16
    };
    
    // 字符集（不包含I、O、Z）
    private static final String VALID_CHARS = "0123456789ABCDEFGHJKLMNPQRSTUVWXY";
    
    // 字符到数值的映射
    private static final Map<Character, Integer> CHAR_TO_VALUE = new HashMap<>();
    
    // 数值到字符的映射
    private static final Map<Integer, Character> VALUE_TO_CHAR = new HashMap<>();
    
    // 随机数生成器
    private static final Random RANDOM = new Random();
    
    // 登记管理部门代码
    private static final List<String> REGISTRATION_DEPT_CODES = Arrays.asList(
        "1", // 机构编制
        "5", // 民政
        "9", // 工商
        "Y"  // 其他
    );
    
    // 机构类别代码
    private static final List<String> ORGANIZATION_TYPE_CODES = Arrays.asList(
        "1", // 企业
        "2", // 个体工商户
        "3", // 事业单位
        "4", // 社会团体
        "5", // 民办非企业单位
        "9"  // 其他
    );
    
    // 常用行政区划代码（示例）
    private static final List<String> ADMIN_DIVISION_CODES = Arrays.asList(
        "110000", // 北京市
        "120000", // 天津市
        "130000", // 河北省
        "140000", // 山西省
        "150000", // 内蒙古自治区
        "210000", // 辽宁省
        "220000", // 吉林省
        "230000", // 黑龙江省
        "310000", // 上海市
        "320000", // 江苏省
        "330000", // 浙江省
        "340000", // 安徽省
        "350000", // 福建省
        "360000", // 江西省
        "370000", // 山东省
        "410000", // 河南省
        "420000", // 湖北省
        "430000", // 湖南省
        "440000", // 广东省
        "450000", // 广西壮族自治区
        "460000", // 海南省
        "500000", // 重庆市
        "510000", // 四川省
        "520000", // 贵州省
        "530000", // 云南省
        "540000", // 西藏自治区
        "610000", // 陕西省
        "620000", // 甘肃省
        "630000", // 青海省
        "640000", // 宁夏回族自治区
        "650000"  // 新疆维吾尔自治区
    );
    
    // 静态初始化块
    static {
        // 初始化字符映射表
        for (int i = 0; i < VALID_CHARS.length(); i++) {
            char c = VALID_CHARS.charAt(i);
            CHAR_TO_VALUE.put(c, i);
            VALUE_TO_CHAR.put(i, c);
        }
        
        System.out.println("统一社会信用代码生成器初始化完成");
        System.out.println("支持字符集: " + VALID_CHARS);
        System.out.println("字符集长度: " + VALID_CHARS.length());
    }
    
    /**
     * 私有构造方法，防止实例化
     */
    private UnifiedSocialCreditCodeGenerator() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    /**
     * 生成随机的统一社会信用代码
     * 
     * @return 18位统一社会信用代码
     */
    public static String generateRandomCode() {
        StringBuilder code = new StringBuilder();
        
        // 第1位：登记管理部门代码
        code.append(getRandomElement(REGISTRATION_DEPT_CODES));
        
        // 第2位：机构类别代码
        code.append(getRandomElement(ORGANIZATION_TYPE_CODES));
        
        // 第3-8位：行政区划代码
        code.append(getRandomElement(ADMIN_DIVISION_CODES));
        
        // 第9-17位：主体标识码（9位随机字符）
        for (int i = 0; i < 9; i++) {
            code.append(VALID_CHARS.charAt(RANDOM.nextInt(VALID_CHARS.length())));
        }
        
        // 第18位：校验码
        char checkCode = calculateCheckCode(code.toString());
        code.append(checkCode);
        
        return code.toString();
    }
    
    /**
     * 生成指定前缀的统一社会信用代码
     * 
     * @param deptCode 登记管理部门代码
     * @param typeCode 机构类别代码
     * @param adminCode 行政区划代码（6位）
     * @return 18位统一社会信用代码
     */
    public static String generateCodeWithPrefix(String deptCode, String typeCode, String adminCode) {
        if (deptCode == null || deptCode.length() != 1) {
            throw new IllegalArgumentException("登记管理部门代码必须为1位字符");
        }
        if (typeCode == null || typeCode.length() != 1) {
            throw new IllegalArgumentException("机构类别代码必须为1位字符");
        }
        if (adminCode == null || adminCode.length() != 6) {
            throw new IllegalArgumentException("行政区划代码必须为6位字符");
        }
        
        StringBuilder code = new StringBuilder();
        code.append(deptCode);
        code.append(typeCode);
        code.append(adminCode);
        
        // 第9-17位：主体标识码（9位随机字符）
        for (int i = 0; i < 9; i++) {
            code.append(VALID_CHARS.charAt(RANDOM.nextInt(VALID_CHARS.length())));
        }
        
        // 第18位：校验码
        char checkCode = calculateCheckCode(code.toString());
        code.append(checkCode);
        
        return code.toString();
    }
    
    /**
     * 使用线程安全的随机数生成器生成统一社会信用代码
     * 适用于多线程环境
     * 
     * @return 18位统一社会信用代码
     */
    public static String generateRandomCodeThreadSafe() {
        StringBuilder code = new StringBuilder();
        ThreadLocalRandom random = ThreadLocalRandom.current();
        
        // 第1位：登记管理部门代码
        code.append(REGISTRATION_DEPT_CODES.get(random.nextInt(REGISTRATION_DEPT_CODES.size())));
        
        // 第2位：机构类别代码
        code.append(ORGANIZATION_TYPE_CODES.get(random.nextInt(ORGANIZATION_TYPE_CODES.size())));
        
        // 第3-8位：行政区划代码
        code.append(ADMIN_DIVISION_CODES.get(random.nextInt(ADMIN_DIVISION_CODES.size())));
        
        // 第9-17位：主体标识码（9位随机字符）
        for (int i = 0; i < 9; i++) {
            code.append(VALID_CHARS.charAt(random.nextInt(VALID_CHARS.length())));
        }
        
        // 第18位：校验码
        char checkCode = calculateCheckCode(code.toString());
        code.append(checkCode);
        
        return code.toString();
    }
    
    /**
     * 计算校验码
     * 
     * @param code17 前17位代码
     * @return 校验码字符
     */
    public static char calculateCheckCode(String code17) {
        if (code17 == null || code17.length() != 17) {
            throw new IllegalArgumentException("代码必须为17位字符");
        }
        
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            char c = code17.charAt(i);
            Integer value = CHAR_TO_VALUE.get(c);
            if (value == null) {
                throw new IllegalArgumentException("无效字符: " + c);
            }
            sum += value * WEIGHT_FACTORS[i];
        }
        
        int remainder = sum % 31;
        int checkValue = (31 - remainder) % 31;
        
        Character checkChar = VALUE_TO_CHAR.get(checkValue);
        if (checkChar == null) {
            throw new RuntimeException("校验码计算错误: " + checkValue);
        }
        
        return checkChar;
    }
    
    /**
     * 验证统一社会信用代码的有效性
     * 
     * @param code 18位统一社会信用代码
     * @return 是否有效
     */
    public static boolean validateCode(String code) {
        if (code == null || code.length() != 18) {
            return false;
        }
        
        try {
            String code17 = code.substring(0, 17);
            char expectedCheckCode = calculateCheckCode(code17);
            return code.charAt(17) == expectedCheckCode;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 从统一社会信用代码中提取组织机构代码
     * 
     * @param code 18位统一社会信用代码
     * @return 9位组织机构代码（第9-17位）
     */
    public static String extractOrganizationCode(String code) {
        if (code == null || code.length() != 18) {
            throw new IllegalArgumentException("统一社会信用代码必须为18位字符");
        }
        return code.substring(8, 17);
    }
    
    /**
     * 从列表中随机获取一个元素
     * 
     * @param list 源列表
     * @return 随机元素
     */
    private static String getRandomElement(List<String> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("列表不能为空");
        }
        return list.get(RANDOM.nextInt(list.size()));
    }
    
    /**
     * 获取支持的字符集
     * 
     * @return 字符集字符串
     */
    public static String getValidCharacters() {
        return VALID_CHARS;
    }
    
    /**
     * 获取加权因子数组
     * 
     * @return 加权因子数组的副本
     */
    public static int[] getWeightFactors() {
        return Arrays.copyOf(WEIGHT_FACTORS, WEIGHT_FACTORS.length);
    }
    
    /**
     * 获取登记管理部门代码列表
     * 
     * @return 部门代码列表
     */
    public static List<String> getRegistrationDeptCodes() {
        return REGISTRATION_DEPT_CODES;
    }
    
    /**
     * 获取机构类别代码列表
     * 
     * @return 类别代码列表
     */
    public static List<String> getOrganizationTypeCodes() {
        return ORGANIZATION_TYPE_CODES;
    }
    
    /**
     * 获取行政区划代码列表
     *
     * @return 区划代码列表
     */
    public static List<String> getAdminDivisionCodes() {
        return ADMIN_DIVISION_CODES;
    }

    /**
     * 批量生成结果类
     */
    public static class BatchGenerationResult {
        private final long totalGenerated;
        private final long uniqueGenerated;
        private final long duplicateCount;
        private final long totalTimeMs;
        private final double averageSpeed;

        public BatchGenerationResult(long totalGenerated, long uniqueGenerated,
                                   long duplicateCount, long totalTimeMs) {
            this.totalGenerated = totalGenerated;
            this.uniqueGenerated = uniqueGenerated;
            this.duplicateCount = duplicateCount;
            this.totalTimeMs = totalTimeMs;
            this.averageSpeed = totalTimeMs > 0 ? (double) uniqueGenerated * 1000 / totalTimeMs : 0;
        }

        public long getTotalGenerated() { return totalGenerated; }
        public long getUniqueGenerated() { return uniqueGenerated; }
        public long getDuplicateCount() { return duplicateCount; }
        public long getTotalTimeMs() { return totalTimeMs; }
        public double getAverageSpeed() { return averageSpeed; }

        @Override
        public String toString() {
            return String.format(
                "批量生成结果:\n" +
                "  总生成数量: %,d\n" +
                "  唯一代码数量: %,d\n" +
                "  重复代码数量: %,d\n" +
                "  总耗时: %,d ms (%.2f 秒)\n" +
                "  平均速度: %,.0f 个/秒\n" +
                "  唯一性比例: %.2f%%",
                totalGenerated, uniqueGenerated, duplicateCount, totalTimeMs,
                totalTimeMs / 1000.0, averageSpeed,
                totalGenerated > 0 ? (double) uniqueGenerated / totalGenerated * 100 : 0
            );
        }
    }

    /**
     * 批量生成统一社会信用代码到文件
     *
     * @param count 生成数量
     * @param outputFilePath 输出文件路径
     * @param includeOrgCode 是否包含组织机构代码
     * @return 生成结果
     * @throws IOException 文件操作异常
     */
    public static BatchGenerationResult generateCodesToFile(long count, String outputFilePath,
                                                          boolean includeOrgCode) throws IOException {
        Path outputPath = Paths.get(outputFilePath);
        Set<String> generatedCodes = new HashSet<>();
        long totalGenerated = 0;
        long duplicateCount = 0;

        long startTime = System.currentTimeMillis();

        try (BufferedWriter writer = Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8,
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {

            // 写入文件头
            if (includeOrgCode) {
                writer.write("统一社会信用代码,组织机构代码");
            } else {
                writer.write("统一社会信用代码");
            }
            writer.newLine();

            final int batchSize = 10000;
            List<String> batch = new ArrayList<>(batchSize);

            while (generatedCodes.size() < count) {
                String code = generateRandomCode();
                totalGenerated++;

                if (generatedCodes.add(code)) {
                    if (includeOrgCode) {
                        String orgCode = extractOrganizationCode(code);
                        batch.add(code + "," + orgCode);
                    } else {
                        batch.add(code);
                    }

                    // 批量写入
                    if (batch.size() >= batchSize || generatedCodes.size() >= count) {
                        for (String line : batch) {
                            writer.write(line);
                            writer.newLine();
                        }
                        writer.flush();
                        batch.clear();

                        // 进度提示
                        if (generatedCodes.size() % 100000 == 0 || generatedCodes.size() >= count) {
                            System.out.printf("已生成 %,d/%,d 条唯一代码 (%.2f%%), 总尝试: %,d\n",
                                generatedCodes.size(), count,
                                (double) generatedCodes.size() / count * 100, totalGenerated);
                        }
                    }
                } else {
                    duplicateCount++;
                }
            }
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        BatchGenerationResult result = new BatchGenerationResult(
            totalGenerated, generatedCodes.size(), duplicateCount, totalTime);

        System.out.println("批量生成完成，保存至: " + outputFilePath);
        System.out.println(result);

        return result;
    }

    /**
     * 多线程批量生成统一社会信用代码到文件
     * 适用于大数据量生成（如5000万条）
     *
     * @param count 生成数量
     * @param outputFilePath 输出文件路径
     * @param includeOrgCode 是否包含组织机构代码
     * @param threadCount 线程数量
     * @return 生成结果
     * @throws IOException 文件操作异常
     * @throws InterruptedException 线程中断异常
     */
    public static BatchGenerationResult generateCodesToFileMultiThreaded(long count, String outputFilePath,
                                                                       boolean includeOrgCode, int threadCount)
            throws IOException, InterruptedException {

        Path outputPath = Paths.get(outputFilePath);
        Set<String> generatedCodes = ConcurrentHashMap.newKeySet();
        AtomicLong totalGenerated = new AtomicLong(0);
        AtomicLong duplicateCount = new AtomicLong(0);
        AtomicLong writtenCount = new AtomicLong(0);

        // 创建线程安全的队列
        BlockingQueue<String> codeQueue = new LinkedBlockingQueue<>(50000);

        long startTime = System.currentTimeMillis();

        // 创建线程池
        ExecutorService generatorPool = Executors.newFixedThreadPool(threadCount);
        ExecutorService writerPool = Executors.newSingleThreadExecutor();

        System.out.println("开始多线程生成统一社会信用代码:");
        System.out.println("  目标数量: " + String.format("%,d", count));
        System.out.println("  输出文件: " + outputFilePath);
        System.out.println("  线程数量: " + threadCount);
        System.out.println("  包含组织机构代码: " + includeOrgCode);
        System.out.println();

        // 启动写入线程
        CompletableFuture<Void> writerFuture = CompletableFuture.runAsync(() -> {
            try (BufferedWriter writer = Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8,
                    StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {

                // 写入文件头
                if (includeOrgCode) {
                    writer.write("统一社会信用代码,组织机构代码");
                } else {
                    writer.write("统一社会信用代码");
                }
                writer.newLine();

                final int batchSize = 10000;
                List<String> batch = new ArrayList<>(batchSize);

                while (writtenCount.get() < count) {
                    String line = codeQueue.poll(1, TimeUnit.SECONDS);
                    if (line != null) {
                        batch.add(line);

                        if (batch.size() >= batchSize || writtenCount.get() + batch.size() >= count) {
                            for (String l : batch) {
                                writer.write(l);
                                writer.newLine();
                            }
                            writer.flush();

                            long written = writtenCount.addAndGet(batch.size());
                            batch.clear();

                            // 进度提示
                            if (written % 100000 == 0 || written >= count) {
                                System.out.printf("[写入] 进度: %,d/%,d (%.2f%%)\n",
                                    written, count, (double) written / count * 100);
                            }
                        }
                    }
                }

                // 写入剩余数据
                if (!batch.isEmpty()) {
                    for (String l : batch) {
                        writer.write(l);
                        writer.newLine();
                    }
                    writer.flush();
                    writtenCount.addAndGet(batch.size());
                }

            } catch (Exception e) {
                throw new RuntimeException("写入文件失败", e);
            }
        }, writerPool);

        // 启动生成线程
        List<CompletableFuture<Void>> generatorFutures = new ArrayList<>();

        for (int i = 0; i < threadCount; i++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                while (generatedCodes.size() < count) {
                    try {
                        String code = generateRandomCodeThreadSafe();
                        totalGenerated.incrementAndGet();

                        if (generatedCodes.add(code)) {
                            String line;
                            if (includeOrgCode) {
                                String orgCode = extractOrganizationCode(code);
                                line = code + "," + orgCode;
                            } else {
                                line = code;
                            }

                            codeQueue.put(line);

                            long generated = generatedCodes.size();
                            if (generated % 100000 == 0) {
                                System.out.printf("[生成] 进度: %,d/%,d (%.2f%%), 总尝试: %,d\n",
                                    generated, count, (double) generated / count * 100,
                                    totalGenerated.get());
                            }
                        } else {
                            duplicateCount.incrementAndGet();
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }, generatorPool);

            generatorFutures.add(future);
        }

        // 等待所有生成线程完成
        CompletableFuture.allOf(generatorFutures.toArray(new CompletableFuture[0])).join();

        // 等待写入线程完成
        writerFuture.join();

        // 关闭线程池
        generatorPool.shutdown();
        writerPool.shutdown();

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        BatchGenerationResult result = new BatchGenerationResult(
            totalGenerated.get(), generatedCodes.size(), duplicateCount.get(), totalTime);

        System.out.println("\n=== 多线程生成完成 ===");
        System.out.println("文件保存至: " + outputFilePath);
        System.out.println(result);

        return result;
    }
}
