package cn.org.nifa.bxcredit.generator.util;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 校验工具类
 * 
 * <AUTHOR> Team
 */
public class ValidationUtils {
    
    // 证件类型枚举
    public static final List<String> PERSONAL_CERT_TYPES = Arrays.asList(
        "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "X"
    );
    
    public static final List<String> ENTERPRISE_CERT_TYPES = Arrays.asList("a", "b");
    
    // 业务类型枚举
    public static final List<String> BUSINESS_TYPES = Arrays.asList("2", "3", "4", "5");
    
    // 个人业务种类枚举
    public static final List<String> PERSONAL_BUSINESS_CATEGORIES = Arrays.asList(
        "11", "21", "31", "41", "51", "71", "99"
    );
    
    // 企业业务种类枚举
    public static final List<String> ENTERPRISE_BUSINESS_CATEGORIES = Arrays.asList(
        "11", "21", "31", "41", "51", "61", "71", "99"
    );
    
    // 还款状态枚举
    public static final List<String> REPAYMENT_STATUS = Arrays.asList(
        "*", "N", "1", "2", "3", "4", "5", "6", "7", "C", "D", "I", "Z", "G"
    );
    
    // 企业还款状态枚举（包含D-第三方代偿）
    public static final List<String> ENTERPRISE_REPAYMENT_STATUS = Arrays.asList(
        "*", "N", "1", "2", "3", "4", "5", "6", "7", "C", "D", "Z", "G"
    );
    
    /**
     * 校验字符串长度
     * 
     * @param value 待校验值
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 是否有效
     */
    public static boolean validateLength(String value, int minLength, int maxLength) {
        if (value == null) {
            return minLength == 0;
        }
        int length = value.length();
        return length >= minLength && length <= maxLength;
    }
    
    /**
     * 校验是否为空或空格
     * 
     * @param value 待校验值
     * @return 是否为空
     */
    public static boolean isEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }
    
    /**
     * 校验是否为数字
     * 
     * @param value 待校验值
     * @return 是否为数字
     */
    public static boolean isNumeric(String value) {
        if (isEmpty(value)) {
            return false;
        }
        return Pattern.matches("\\d+", value);
    }
    
    /**
     * 校验是否为非负整数
     * 
     * @param value 待校验值
     * @return 是否为非负整数
     */
    public static boolean isNonNegativeInteger(String value) {
        if (!isNumeric(value)) {
            return false;
        }
        try {
            long num = Long.parseLong(value);
            return num >= 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 校验业务号格式
     * 
     * @param businessNumber 业务号
     * @return 是否有效
     */
    public static boolean validateBusinessNumber(String businessNumber) {
        if (businessNumber == null || businessNumber.length() != 32) {
            return false;
        }
        if (!businessNumber.startsWith("BN")) {
            return false;
        }
        return isNumeric(businessNumber.substring(2));
    }
    
    /**
     * 校验组织机构代码或社会信用代码
     *
     * @param code 代码
     * @return 是否有效
     */
    public static boolean validateOrgCode(String code) {
        if (code == null) {
            return false;
        }
        if (code.length() == 9) {
            // 9位组织机构代码校验（简化版本）
            return validateOrganizationCode(code);
        } else if (code.length() == 18) {
            // 18位统一社会信用代码校验
            return validateUnifiedSocialCreditCode(code);
        }
        return false;
    }

    /**
     * 校验统一社会信用代码
     *
     * @param code 18位统一社会信用代码
     * @return 是否有效
     */
    public static boolean validateUnifiedSocialCreditCode(String code) {
        if (isEmpty(code) || code.length() != 18) {
            return false;
        }

        try {
            return UnifiedSocialCreditCodeGenerator.validateCode(code);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 校验组织机构代码（9位）
     *
     * @param code 9位组织机构代码
     * @return 是否有效
     */
    public static boolean validateOrganizationCode(String code) {
        if (isEmpty(code) || code.length() != 9) {
            return false;
        }

        // 简化的组织机构代码校验
        // 前8位应为数字或大写字母，第9位为校验码
        String code8 = code.substring(0, 8);
        for (char c : code8.toCharArray()) {
            if (!Character.isDigit(c) && !Character.isUpperCase(c)) {
                return false;
            }
        }

        // 校验码可以是数字或大写字母
        char checkCode = code.charAt(8);
        return Character.isDigit(checkCode) || Character.isUpperCase(checkCode);
    }

    /**
     * 从统一社会信用代码中提取组织机构代码
     *
     * @param unifiedCode 18位统一社会信用代码
     * @return 9位组织机构代码，如果输入无效则返回null
     */
    public static String extractOrganizationCodeFromUnified(String unifiedCode) {
        if (!validateUnifiedSocialCreditCode(unifiedCode)) {
            return null;
        }

        try {
            return UnifiedSocialCreditCodeGenerator.extractOrganizationCode(unifiedCode);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 校验代码类型并返回类型信息
     *
     * @param code 代码
     * @return 代码类型描述
     */
    public static String getCodeType(String code) {
        if (isEmpty(code)) {
            return "无效代码";
        }

        switch (code.length()) {
            case 9:
                return validateOrganizationCode(code) ? "组织机构代码" : "无效的组织机构代码";
            case 18:
                return validateUnifiedSocialCreditCode(code) ? "统一社会信用代码" : "无效的统一社会信用代码";
            default:
                return "未知代码类型";
        }
    }

    /**
     * 校验公司名称格式
     *
     * @param companyName 公司名称
     * @return 是否有效
     */
    public static boolean validateCompanyName(String companyName) {
        if (isEmpty(companyName)) {
            return false;
        }

        try {
            return CompanyNameGenerator.validateCompanyName(companyName);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 校验公司名称长度
     *
     * @param companyName 公司名称
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 是否有效
     */
    public static boolean validateCompanyNameLength(String companyName, int minLength, int maxLength) {
        if (isEmpty(companyName)) {
            return false;
        }

        return companyName.length() >= minLength && companyName.length() <= maxLength;
    }

    /**
     * 校验公司名称是否包含必要的后缀
     *
     * @param companyName 公司名称
     * @return 是否包含有效后缀
     */
    public static boolean validateCompanyNameSuffix(String companyName) {
        if (isEmpty(companyName)) {
            return false;
        }

        // 常见的公司后缀
        String[] commonSuffixes = {
            "有限公司", "股份有限公司", "集团", "公司", "企业", "工厂", "厂",
            "中心", "研究所", "院", "社", "部", "店", "行", "馆", "所"
        };

        for (String suffix : commonSuffixes) {
            if (companyName.endsWith(suffix)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 校验公司名称是否包含非法字符
     *
     * @param companyName 公司名称
     * @return 是否包含非法字符
     */
    public static boolean containsIllegalCharacters(String companyName) {
        if (isEmpty(companyName)) {
            return false;
        }

        // 非法字符列表
        String[] illegalChars = {
            "!", "@", "#", "$", "%", "^", "&", "*", "(", ")",
            "[", "]", "{", "}", "|", "\\", ":", ";", "\"", "'",
            "<", ">", ",", ".", "?", "/", "~", "`"
        };

        for (String illegalChar : illegalChars) {
            if (companyName.contains(illegalChar)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 综合校验公司名称
     *
     * @param companyName 公司名称
     * @return 校验结果描述
     */
    public static String validateCompanyNameComprehensive(String companyName) {
        if (isEmpty(companyName)) {
            return "公司名称不能为空";
        }

        if (!validateCompanyNameLength(companyName, 4, 50)) {
            return "公司名称长度应在4-50个字符之间";
        }

        if (containsIllegalCharacters(companyName)) {
            return "公司名称包含非法字符";
        }

        if (!validateCompanyNameSuffix(companyName)) {
            return "公司名称缺少有效的公司类型后缀";
        }

        if (!validateCompanyName(companyName)) {
            return "公司名称格式不符合规范";
        }

        return "有效的公司名称";
    }
    
    /**
     * 校验证件号码格式
     * 
     * @param certType 证件类型
     * @param certNumber 证件号码
     * @return 是否有效
     */
    public static boolean validateCertNumber(String certType, String certNumber) {
        if (isEmpty(certNumber)) {
            return false;
        }
        
        switch (certType) {
            case "0": // 身份证
                return IdCardGenerator.validateIdCard(certNumber);
            case "2": // 护照
                return Pattern.matches("E\\d{8}", certNumber);
            case "3": // 军官证
                return Pattern.matches("军\\d{7}", certNumber);
            case "4": // 士官证
                return Pattern.matches("士\\d{7}", certNumber);
            case "5": // 港澳居民来往内地通行证
                return Pattern.matches("[HM]\\d{8}", certNumber);
            case "6": // 台湾同胞来往内地通行证
                return Pattern.matches("L\\d{8}", certNumber);
            case "8": // 外国人居留证
                return Pattern.matches("CAN\\d{12}", certNumber);
            case "9": // 警官证
                return Pattern.matches("\\d{6}", certNumber);
            case "A": // 香港身份证
                return Pattern.matches("[A-Z]\\w{6,7}", certNumber);
            case "B": // 澳门身份证
                return Pattern.matches("\\w{8}", certNumber);
            case "C": // 台湾身份证
                return Pattern.matches("\\w{10}", certNumber);
            default:
                return certNumber.length() <= 18;
        }
    }
    
    /**
     * 校验日期格式
     * 
     * @param date 日期字符串
     * @return 是否有效
     */
    public static boolean validateDateFormat(String date) {
        return DateUtils.isValidDate(date);
    }
    
    /**
     * 校验枚举值
     * 
     * @param value 待校验值
     * @param validValues 有效值列表
     * @return 是否有效
     */
    public static boolean validateEnum(String value, List<String> validValues) {
        return validValues.contains(value);
    }
}