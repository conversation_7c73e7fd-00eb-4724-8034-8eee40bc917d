package cn.org.nifa.bxcredit.generator.generator;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.org.nifa.bxcredit.config.ConfigManager;
import cn.org.nifa.bxcredit.generator.model.EnterpriseLoanData;
import cn.org.nifa.bxcredit.generator.util.BusinessCodeGenerator;
import cn.org.nifa.bxcredit.generator.util.CompanyNameGenerator;
import cn.org.nifa.bxcredit.generator.util.DateUtils;
import cn.org.nifa.bxcredit.generator.util.IdCardGenerator;
import cn.org.nifa.bxcredit.generator.util.NameGenerator;
import cn.org.nifa.bxcredit.generator.util.UnifiedSocialCreditCodeGenerator;
import cn.org.nifa.bxcredit.generator.util.ValidationUtils;

/**
 * 企业数据生成器
 * 
 * <AUTHOR> Team
 */
public class EnterpriseDataGenerator implements DataGenerator<EnterpriseLoanData> {
    
    private static final Logger logger = LoggerFactory.getLogger(EnterpriseDataGenerator.class);
    private static final Random RANDOM = new Random();
    
    @Override
    public EnterpriseLoanData generatePositiveData() {
        return EnterpriseLoanData.builder()
                .enterpriseName(generateEnterpriseName())
                .enterpriseCertType(generateEnterpriseCertType())
                .enterpriseCode(generateEnterpriseCode("a"))
                .legalPersonName(generateLegalPersonName())
                .legalPersonCertType("0")
                .legalPersonCertNumber(IdCardGenerator.generateRandomIdCard())
                .businessInstitution(generateBusinessInstitution())
                .businessNumber(BusinessCodeGenerator.generateBusinessNumber())
                .businessType(generateBusinessType())
                .businessCategory(generateBusinessCategory())
                .openDate(generateOpenDate())
                .dueDate(generateDueDate("2"))
                .creditLimit(generateCreditLimit())
                .businessDate(generateBusinessDate())
                .repaymentStatus(generateRepaymentStatus())
                .balance(generateBalance())
                .overdueAmount(generateOverdueAmount("N"))
                .compensationInstitution("")
                .compensationAmount("0")
                .build();
    }
    
    @Override
    public List<EnterpriseLoanData> generatePositiveDataBatch(int count) {
        List<EnterpriseLoanData> dataList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            dataList.add(generatePositiveData());
        }
        logger.info("生成{}条企业正向测试数据", count);
        return dataList;
    }
    
    @Override
    public EnterpriseLoanData generateNegativeData(String ruleViolation) {
        EnterpriseLoanData.Builder builder = EnterpriseLoanData.builder();
        
        switch (ruleViolation) {
            case "ENTERPRISE_NAME_EMPTY":
                builder.enterpriseName("");
                break;
            case "ENTERPRISE_NAME_TOO_LONG":
                builder.enterpriseName("A".repeat(61));
                break;
            case "ENTERPRISE_CERT_TYPE_INVALID":
                builder.enterpriseCertType("c");
                break;
            case "LEGAL_PERSON_CERT_INVALID":
                builder.legalPersonCertType("0")
                       .legalPersonCertNumber(IdCardGenerator.generateInvalidIdCard("LENGTH"));
                break;
            case "BUSINESS_NUMBER_INVALID":
                builder.businessNumber(BusinessCodeGenerator.generateInvalidBusinessNumber("NO_PREFIX"));
                break;
            case "COMPENSATION_STATUS_INVALID":
                builder.repaymentStatus("D")
                       .compensationAmount("0");
                break;
            default:
                return generatePositiveData();
        }
        
        fillDefaultFields(builder);
        return builder.build();
    }
    
    @Override
    public List<EnterpriseLoanData> generateAllNegativeData() {
        List<EnterpriseLoanData> negativeDataList = new ArrayList<>();
        
        String[] ruleViolations = {
            "ENTERPRISE_NAME_EMPTY",
            "ENTERPRISE_NAME_TOO_LONG", 
            "ENTERPRISE_CERT_TYPE_INVALID",
            "LEGAL_PERSON_CERT_INVALID",
            "BUSINESS_NUMBER_INVALID",
            "COMPENSATION_STATUS_INVALID"
        };
        
        for (String violation : ruleViolations) {
            negativeDataList.add(generateNegativeData(violation));
        }
        
        logger.info("生成{}条企业反向测试数据", negativeDataList.size());
        return negativeDataList;
    }
    
    private String generateEnterpriseName() {
        try {
            return CompanyNameGenerator.generateRandomCompanyName();
        } catch (Exception e) {
            logger.warn("使用CompanyNameGenerator生成公司名称失败: {}", e.getMessage());
            // 降级处理，使用原有方法
            String[] prefixes = {"北京", "上海", "深圳", "广州", "杭州"};
            String[] names = {"科技", "贸易", "实业", "投资", "咨询"};
            String[] suffixes = {"有限公司", "股份有限公司", "集团有限公司"};
            
            return prefixes[RANDOM.nextInt(prefixes.length)] + 
                   names[RANDOM.nextInt(names.length)] + 
                   suffixes[RANDOM.nextInt(suffixes.length)];
        }
    }
    
    private String generateEnterpriseCertType() {
        return ValidationUtils.ENTERPRISE_CERT_TYPES.get(
            RANDOM.nextInt(ValidationUtils.ENTERPRISE_CERT_TYPES.size()));
    }
    
    private String generateEnterpriseCode(String certType) {
        return "a".equals(certType) ? 
            BusinessCodeGenerator.generateOrgCode() : 
            UnifiedSocialCreditCodeGenerator.generateRandomCode();
    }
    
    private String generateLegalPersonName() {
        try {
            return NameGenerator.generateChineseNameTwoChar();
        } catch (Exception e) {
            logger.warn("使用NameGenerator生成法人姓名失败: {}", e.getMessage());
            // 降级处理，使用原有方法
            String[] surnames = {"王", "李", "张", "刘", "陈"};
            String[] names = {"伟", "芳", "娜", "秀英", "敏"};
            return surnames[RANDOM.nextInt(surnames.length)] + names[RANDOM.nextInt(names.length)];
        }
    }
    
    private String generateBusinessInstitution() {
        try {
            // 使用配置文件中的机构代码
            return ConfigManager.getOrgCode();
        } catch (Exception e) {
            logger.warn("从配置文件获取机构代码失败: {}", e.getMessage());
            // 降级处理，使用原有方法
            return RANDOM.nextBoolean() ? 
                BusinessCodeGenerator.generateOrgCode() : 
                UnifiedSocialCreditCodeGenerator.generateRandomCode();
        }
    }
    
    private String generateBusinessType() {
        return ValidationUtils.BUSINESS_TYPES.get(
            RANDOM.nextInt(ValidationUtils.BUSINESS_TYPES.size()));
    }
    
    private String generateBusinessCategory() {
        return ValidationUtils.ENTERPRISE_BUSINESS_CATEGORIES.get(
            RANDOM.nextInt(ValidationUtils.ENTERPRISE_BUSINESS_CATEGORIES.size()));
    }
    
    private String generateOpenDate() {
        return DateUtils.getDateBefore(RANDOM.nextInt(365) + 1);
    }
    
    private String generateDueDate(String businessType) {
        if ("4".equals(businessType)) {
            return "20991231";
        }
        return DateUtils.getDateAfter(RANDOM.nextInt(365) + 30);
    }
    
    private String generateCreditLimit() {
        return String.valueOf((RANDOM.nextInt(10000) + 1) * 1000);
    }
    
    private String generateBusinessDate() {
        return DateUtils.getCurrentDate();
    }
    
    private String generateRepaymentStatus() {
        return ValidationUtils.ENTERPRISE_REPAYMENT_STATUS.get(
            RANDOM.nextInt(ValidationUtils.ENTERPRISE_REPAYMENT_STATUS.size()));
    }
    
    private String generateBalance() {
        return String.valueOf(RANDOM.nextInt(1000000));
    }
    
    private String generateOverdueAmount(String repaymentStatus) {
        if ("C".equals(repaymentStatus) || "*".equals(repaymentStatus) || "N".equals(repaymentStatus)) {
            return "0";
        }
        return String.valueOf(RANDOM.nextInt(100000));
    }
    
    private void fillDefaultFields(EnterpriseLoanData.Builder builder) {
        builder.enterpriseName(builder.build().getEnterpriseName() != null ?
                builder.build().getEnterpriseName() : generateEnterpriseName())
               .enterpriseCertType(builder.build().getEnterpriseCertType() != null ?
                       builder.build().getEnterpriseCertType() : "a")
               .enterpriseCode(generateEnterpriseCode("a"))
               .legalPersonName(generateLegalPersonName())
               .legalPersonCertType("0")
               .legalPersonCertNumber(IdCardGenerator.generateRandomIdCard())
               .businessInstitution(generateBusinessInstitution())
               .businessNumber(builder.build().getBusinessNumber() != null ?
                       builder.build().getBusinessNumber() : BusinessCodeGenerator.generateBusinessNumber())
               .businessType("2")
               .businessCategory("11")
               .openDate(generateOpenDate())
               .dueDate(generateDueDate("2"))
               .creditLimit(generateCreditLimit())
               .businessDate(generateBusinessDate())
               .repaymentStatus(builder.build().getRepaymentStatus() != null ?
                       builder.build().getRepaymentStatus() : "N")
               .balance(generateBalance())
               .overdueAmount(builder.build().getOverdueAmount() != null ?
                       builder.build().getOverdueAmount() : "0")
               .compensationInstitution(builder.build().getCompensationInstitution() != null ?
                       builder.build().getCompensationInstitution() : "")
               .compensationAmount(builder.build().getCompensationAmount() != null ?
                       builder.build().getCompensationAmount() : "0");
    }
}