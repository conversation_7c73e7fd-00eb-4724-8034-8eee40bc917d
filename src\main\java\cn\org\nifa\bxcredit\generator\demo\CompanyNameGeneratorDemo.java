package cn.org.nifa.bxcredit.generator.demo;

import cn.org.nifa.bxcredit.generator.util.CompanyNameGenerator;
import cn.org.nifa.bxcredit.generator.util.TestDataManager;
import cn.org.nifa.bxcredit.generator.util.UnifiedSocialCreditCodeGenerator;

import java.io.IOException;
import java.util.Scanner;

/**
 * 公司名称生成器演示程序
 * 
 * 展示公司名称生成器的各种功能，包括：
 * - 单个公司名称生成
 * - 批量公司名称生成
 * - 与社会信用代码生成器的集成
 * 
 * <AUTHOR> Team
 */
public class CompanyNameGeneratorDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 公司名称生成器演示程序 ===");
        
        Scanner scanner = new Scanner(System.in);
        boolean exit = false;
        
        while (!exit) {
            printMenu();
            int choice = getChoice(scanner);
            
            try {
                switch (choice) {
                    case 1:
                        demonstrateSingleNameGeneration();
                        break;
                    case 2:
                        demonstrateTemplateNameGeneration();
                        break;
                    case 3:
                        demonstrateBatchGeneration(scanner);
                        break;
                    case 4:
                        demonstrateIntegrationWithCreditCode();
                        break;
                    case 5:
                        demonstrateEstimation();
                        break;
                    case 0:
                        exit = true;
                        System.out.println("感谢使用公司名称生成器演示程序！");
                        break;
                    default:
                        System.out.println("无效选择，请重新输入。");
                }
            } catch (Exception e) {
                System.err.println("操作失败: " + e.getMessage());
                e.printStackTrace();
            }
            
            if (!exit) {
                System.out.println("\n按回车键继续...");
                scanner.nextLine();
            }
        }
        
        scanner.close();
    }
    
    private static void printMenu() {
        System.out.println("\n请选择要演示的功能：");
        System.out.println("1. 单个公司名称生成");
        System.out.println("2. 不同模板的公司名称生成");
        System.out.println("3. 批量公司名称生成");
        System.out.println("4. 与社会信用代码生成器集成");
        System.out.println("5. 大规模生成估算");
        System.out.println("0. 退出");
        System.out.print("请输入选择 [0-5]: ");
    }
    
    private static int getChoice(Scanner scanner) {
        try {
            return Integer.parseInt(scanner.nextLine());
        } catch (NumberFormatException e) {
            return -1;
        }
    }
    
    /**
     * 演示单个公司名称生成
     */
    private static void demonstrateSingleNameGeneration() {
        System.out.println("\n=== 单个公司名称生成 ===");
        
        String name = CompanyNameGenerator.generateRandomCompanyName();
        System.out.println("生成的随机公司名称: " + name);
        System.out.println("名称长度: " + name.length() + " 字符");
        System.out.println("名称有效性: " + (CompanyNameGenerator.validateCompanyName(name) ? "有效" : "无效"));
    }
    
    /**
     * 演示不同模板的公司名称生成
     */
    private static void demonstrateTemplateNameGeneration() {
        System.out.println("\n=== 不同模板的公司名称生成 ===");
        
        System.out.println("1. 前缀+核心词+后缀: " + 
            CompanyNameGenerator.generateCompanyName(CompanyNameGenerator.NameTemplate.PREFIX_CORE_SUFFIX));
        
        System.out.println("2. 核心词+核心词+后缀: " + 
            CompanyNameGenerator.generateCompanyName(CompanyNameGenerator.NameTemplate.CORE_CORE_SUFFIX));
        
        System.out.println("3. 核心词+数字/字母+后缀: " + 
            CompanyNameGenerator.generateCompanyName(CompanyNameGenerator.NameTemplate.CORE_DIGIT_SUFFIX));
        
        System.out.println("4. 地名+核心词+后缀: " + 
            CompanyNameGenerator.generateCompanyName(CompanyNameGenerator.NameTemplate.LOCATION_CORE_SUFFIX));
        
        System.out.println("5. 随机模板: " + 
            CompanyNameGenerator.generateCompanyName(CompanyNameGenerator.NameTemplate.RANDOM));
    }
    
    /**
     * 演示批量公司名称生成
     */
    private static void demonstrateBatchGeneration(Scanner scanner) throws IOException, InterruptedException {
        System.out.println("\n=== 批量公司名称生成 ===");
        
        System.out.print("请输入要生成的公司名称数量 [1-10000]: ");
        int count;
        try {
            count = Integer.parseInt(scanner.nextLine());
            if (count < 1 || count > 10000) {
                System.out.println("数量超出范围，使用默认值100。");
                count = 100;
            }
        } catch (NumberFormatException e) {
            System.out.println("输入无效，使用默认值100。");
            count = 100;
        }
        
        System.out.println("请选择名称模板：");
        System.out.println("1. 前缀+核心词+后缀");
        System.out.println("2. 核心词+核心词+后缀");
        System.out.println("3. 核心词+数字/字母+后缀");
        System.out.println("4. 地名+核心词+后缀");
        System.out.println("5. 随机模板");
        System.out.print("请输入选择 [1-5]: ");
        
        int templateChoice;
        try {
            templateChoice = Integer.parseInt(scanner.nextLine());
            if (templateChoice < 1 || templateChoice > 5) {
                System.out.println("选择无效，使用随机模板。");
                templateChoice = 5;
            }
        } catch (NumberFormatException e) {
            System.out.println("输入无效，使用随机模板。");
            templateChoice = 5;
        }
        
        CompanyNameGenerator.NameTemplate template;
        switch (templateChoice) {
            case 1:
                template = CompanyNameGenerator.NameTemplate.PREFIX_CORE_SUFFIX;
                break;
            case 2:
                template = CompanyNameGenerator.NameTemplate.CORE_CORE_SUFFIX;
                break;
            case 3:
                template = CompanyNameGenerator.NameTemplate.CORE_DIGIT_SUFFIX;
                break;
            case 4:
                template = CompanyNameGenerator.NameTemplate.LOCATION_CORE_SUFFIX;
                break;
            case 5:
            default:
                template = CompanyNameGenerator.NameTemplate.RANDOM;
        }
        
        System.out.println("请选择生成方式：");
        System.out.println("1. 单线程生成");
        System.out.println("2. 多线程生成");
        System.out.print("请输入选择 [1-2]: ");
        
        int methodChoice;
        try {
            methodChoice = Integer.parseInt(scanner.nextLine());
            if (methodChoice < 1 || methodChoice > 2) {
                System.out.println("选择无效，使用单线程生成。");
                methodChoice = 1;
            }
        } catch (NumberFormatException e) {
            System.out.println("输入无效，使用单线程生成。");
            methodChoice = 1;
        }
        
        String outputFilePath = TestDataManager.generateTimestampedFileName("company_names", "csv");
        outputFilePath = TestDataManager.getEnterpriseDataPath(outputFilePath);
        
        System.out.println("\n开始生成 " + count + " 个公司名称...");
        
        CompanyNameGenerator.BatchGenerationResult result;
        if (methodChoice == 1) {
            result = CompanyNameGenerator.generateNamesToFile(count, outputFilePath, template);
        } else {
            int threadCount = Math.min(Runtime.getRuntime().availableProcessors(), 4);
            int batchSize = 100;
            result = CompanyNameGenerator.generateNamesToFileMultiThreaded(
                count, outputFilePath, template, threadCount, batchSize);
        }
        
        System.out.println("\n生成完成！");
        System.out.println("输出文件: " + outputFilePath);
        System.out.println(result);
    }
    
    /**
     * 演示与社会信用代码生成器的集成
     */
    private static void demonstrateIntegrationWithCreditCode() {
        System.out.println("\n=== 与社会信用代码生成器集成 ===");
        
        System.out.println("生成10个公司信息（名称+统一社会信用代码）：");
        System.out.println("----------------------------------------------");
        System.out.printf("%-30s | %s\n", "公司名称", "统一社会信用代码");
        System.out.println("----------------------------------------------");
        
        for (int i = 0; i < 10; i++) {
            String companyName = CompanyNameGenerator.generateRandomCompanyName();
            String creditCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();
            System.out.printf("%-30s | %s\n", companyName, creditCode);
        }
    }
    
    /**
     * 演示大规模生成估算
     */
    private static void demonstrateEstimation() {
        System.out.println("\n=== 大规模生成估算 ===");
        
        long[] counts = {1_000_000L, 10_000_000L, 50_000_000L};
        CompanyNameGenerator.NameTemplate[] templates = {
            CompanyNameGenerator.NameTemplate.PREFIX_CORE_SUFFIX,
            CompanyNameGenerator.NameTemplate.CORE_CORE_SUFFIX,
            CompanyNameGenerator.NameTemplate.CORE_DIGIT_SUFFIX,
            CompanyNameGenerator.NameTemplate.LOCATION_CORE_SUFFIX,
            CompanyNameGenerator.NameTemplate.RANDOM
        };
        
        System.out.println("理论组合数量：");
        for (CompanyNameGenerator.NameTemplate template : templates) {
            long combinations = CompanyNameGenerator.calculateTheoricalCombinations(template);
            System.out.printf("%-25s: %,d\n", template, combinations);
        }
        
        System.out.println("\n不同规模生成估算：");
        System.out.println("--------------------------------------------------------------");
        System.out.printf("%-10s | %-25s | %-15s | %s\n", "数量", "模板", "估算时间", "估算文件大小");
        System.out.println("--------------------------------------------------------------");
        
        for (long count : counts) {
            for (CompanyNameGenerator.NameTemplate template : templates) {
                double time = CompanyNameGenerator.estimateGenerationTime(count, template);
                long size = CompanyNameGenerator.estimateFileSize(count, template);
                
                String timeStr;
                if (time < 60) {
                    timeStr = String.format("%.1f 秒", time);
                } else if (time < 3600) {
                    timeStr = String.format("%.1f 分钟", time / 60);
                } else {
                    timeStr = String.format("%.1f 小时", time / 3600);
                }
                
                String sizeStr;
                if (size < 1024 * 1024) {
                    sizeStr = String.format("%.1f KB", size / 1024.0);
                } else if (size < 1024 * 1024 * 1024) {
                    sizeStr = String.format("%.1f MB", size / 1024.0 / 1024.0);
                } else {
                    sizeStr = String.format("%.1f GB", size / 1024.0 / 1024.0 / 1024.0);
                }
                
                System.out.printf("%-10s | %-25s | %-15s | %s\n", 
                    String.format("%,d", count), template, timeStr, sizeStr);
            }
            System.out.println("--------------------------------------------------------------");
        }
    }
}