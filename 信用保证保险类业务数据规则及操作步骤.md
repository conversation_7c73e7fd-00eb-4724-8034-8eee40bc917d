# 人员标识信息段数据项
## 被保证人类型
- 长度 1
- 枚举值：1-企业或其他组织；2-自然人。
## 被保证人名称
- 长度 大于0，小于等于64
## 被保证人证件类型
- 长度 1
- 枚举值：0-身份证；1-户口簿；2-护照；3-军官证；4-士兵证；5-港澳居民来往内地通行证；6-台湾同胞来往内地通行证；7-临时身份证；8-外国人居留证；9-警官证；A-香港身份证；B-澳门身份证；C-台湾身份证；X-其他证件。a-组织机构代码；b-社会信用代码；
- 规则：当被保证人为自然人时，证件类型只能为0~9，A、B、C或 X ；当被保证人是企业时，证件类型只能为a或b。
## 被保证人证件号码
- 长度 等于18
- 规则：18位身份证号码的最后一位只能为0-9的数字或大写字母“X”。若数据报送机构在提取证件号码时，发现18位身份证号码的最后一位为小写字母“x”，应该用大写字母“X”替代
## 企业法人姓名
- 长度 大于0小于等于64
- 规则：当被保证人的类型为“1-企业或其他组织”时，此数据项为必填项。企业在工商部门的注册法人身份证登记的姓名。
## 企业法人证件类型
- 长度 等于1
- 枚举值：0-身份证；1-户口簿；2-护照；3-军官证；4-士兵证；5-港澳居民来往内地通行证；6-台湾同胞来往内地通行证；7-临时身份证；8-外国人居留证；9-警官证；A-香港身份证；B-澳门身份证；C-台湾身份证；X-其他证件。
- 规则：当被保证人的类型为“1-企业或其他组织”时，此数据项为必填项。
## 企业法人证件号码
 - 长度 等于18
 - 规则：18位身份证号码的最后一位只能为0-9的数字或大写字母“X”。若数据报送机构在提取证件号码时，发现18位身份证号码的最后一位为小写字母“x”，应该用大写字母“X”替代
# 业务标识信息段数据项
## 业务发生机构
- 长度 9或者18
- 组织机构代码长度等于9，统一社会信用代码长度为18
## 保单编号
- 长度 大于0小于等于64
- 保单格式：POL+数字
# 信用保证保险承保信息段数据项
## 保险业务种类 ****
- 长度 等于2
- 枚举值：01－信用保证保险；02－国内贸易信用保险；03－个人消费信用保险；99－其他
## 保险方式
- 长度 等于1
-  枚举值：1－保证；2－信用
## 保险起始日期
 - 长度 等于8
 - 规则：指保险合同约定的保险公司承担保险责任期间的起始日期。格式为YYYYMMDD。
## 保险到期日期
- 长度 等于8
- 规则：指保险合同约定的保险公司承担保险责任期间的终止日期。格式为YYYYMMDD。
## 保险金额
- 长度 大于0小于等于10
- 指保险合同中签订时约定的由保险公司承担保险责任的主债权数额。

# 信用保证保险代偿信息段数据项
## 代偿日期
- 长度 等于8
- 指针对本笔保险合同发生代偿的日期。格式为YYYYMMDD。
## 代偿金额
- 长度 大于0小于等于10
- 指针对本笔保险合同每笔发生代偿的金额，包含本金、利息和罚息。

# 信用保证保险追偿信息段数据项
## 追偿日期
- 长度 等于8
- 规则：指针对本笔保险合同回收代偿款项的日期。格式为YYYYMMDD。
## 追偿金额
- 长度大于0小于等于10
- 指针对本笔保险合同每笔回收代偿款项的金额，包含本金、利息和罚息。


# 承保信息
- 包含人员标识信息段、业务标识信息段、信用保证保险承保信息段。
- 生成的测试数据写入221GUARINFO.txt
- 承保信息文件固定命名：221GUARINFO.txt
- 将221GUARINFO.txt压缩为zip包
- zip命名组成结构：(1)  第1～9位：表示数据报送机构的**组织机构代码（9位）**或者**社会信用代码（18位）的第9-17位**；第22～23位固定为22；第24～27位：表示文件流水序号，按由小至大的顺序编号，编号范围为0000至9999。文件包名长度27位。
- 将zip包加密为enc文件
- 加压加密前后文件包名前缀一致，压缩加密前的文件包名后缀为“zip”，加压加密后文件包名后缀为 .enc。文件名唯一标识一个文件，不得与之前的所有文件的文件名重复。
# 代偿信息
- 包含人员标识信息段、业务标识信息段、信用保证保险代偿信息段。
- 生成的测试数据写入222COMPENSATORYINFO.txt
- 代偿信息文件固定命名：222COMPENSATORYINFO.txt
- 将222COMPENSATORYINFO.txt压缩为zip包
- zip命名组成结构：(1)  第1～9位：表示数据报送机构的**组织机构代码（9位）**或者**社会信用代码（18位）的第9-17位**；第22～23位固定为22；第24～27位：表示文件流水序号，按由小至大的顺序编号，编号范围为0000至9999。文件包名长度27位。
- 将zip包加密为enc文件
- 加压加密前后文件包名前缀一致，压缩加密前的文件包名后缀为“zip”，加压加密后文件包名后缀为 .enc。文件名唯一标识一个文件，不得与之前的所有文件的文件名重复。
# 追偿信息
- 包含人员标识信息段、业务标识信息段、信用保证保险追偿信息段。
- 生成的测试数据写入223DUNINFO.txt
- 代偿信息文件固定命名：223DUNINFO.txt
- 将223DUNINFO.txt压缩为zip包
- zip命名组成结构：(1)  第1～9位：表示数据报送机构的**组织机构代码（9位）**或者**社会信用代码（18位）的第9-17位**；第22～23位固定为22；第24～27位：表示文件流水序号，按由小至大的顺序编号，编号范围为0000至9999。文件包名长度27位。
- 将zip包加密为enc文件
- 加压加密前后文件包名前缀一致，压缩加密前的文件包名后缀为“zip”，加压加密后文件包名后缀为 .enc。文件名唯一标识一个文件，不得与之前的所有文件的文件名重复。

# 文件包名校验规则
- 文件名长度必须为27位。
- 文件名第1～9位必须为上报机构的组织机构代码（9位）或者社会信用代码（18位）的第9-17位（必须与报送用户所属机构的组织机构代码一致）。
- 文件名第10～17位为有效年月，且年份大于1990，年月小于或等于当前年月，格式为“YYYYMMDD”。
- 文件名第24～27位只能填写“0～9”的数字。
- 文件名必须和本机构以前所有上报文件的文件名不重复，即文件名唯一。

# 格式校验规则
- 文件中数据项个数必须符合规范要求。
- 格式为“YYYYMMDD”的必填项，必须是有效日期。（根据具体数据项确定标识符）
- 所有数值型（N）数据必须为整数，与金额有关的数据项值为正整数，且精确到元。
- 文件中数据项长度必须符合规范要求
- 人员标识信息段中，“证件类型”为“0-身份证”时， “证件号码”必须为18位
- 同一承保信息文件中，任意两条信用保证保险交易信息的“业务发生机构”+“保单编号”不能完全相同；
- 同一代偿信息文件中，任意两条信用保证保险交易信息的“业务发生机构”+“保单编号”+“代偿日期”不能完全相同；
- 同一追偿信息文件中，任意两条信用保证保险交易信息的“业务发生机构”+“保单编号”+“追偿日期”不能完全相同；
- 信用保证保险交易信息文件中，当“被保证人类型”为“1-企业或其他组织”时，“被保证人证件类型”必须为“a”或“b”; 当“被保证人类型”为“2-自然人”时，“被保证人证件类型”必须为“0~9，A、B、C或 X”；
- 信用保证保险交易信息文件中，当“被保证人类型”为“1-企业或其他组织”时，“企业法人姓名”为必填项；
- 信用保证保险交易信息文件中，当“被保证人类型”为“1-企业或其他组织”时，“企业法人证件类型”为必填项；
- 信用保证保险交易信息文件中，当“被保证人类型”为“1-企业或其他组织”时，“企业法人证件号码”为必填项；
- 代偿信息文件中，该被保证人（根据“被保证人名称”+“被保证人证件类型”+“被保证人证件号码”判断）未报送过承保信息，或与之前入库的承保信息不一致。追偿信息文件中，该被保证人（根据“被保证人名称”+“被保证人证件类型”+“被保证人证件号码”判断）未报送过承保信息，或与之前入库的承保信息不一致。
- 代偿信息文件中，该笔保单（根据“业务发生机构”+“保单编号”判断）未报送过承保信息。追偿信息文件中，该笔保单（根据“业务发生机构”+“保单编号”判断）未报送过承保信息。
- 代偿信息文件中， “被保证人类型”与对应承保信息（ “业务发生机构”+“保单编号”一致）中的“被保证人类型”不一致；追偿信息文件中， “被保证人类型”与对应承保信息（ “业务发生机构”+“保单编号”一致）中的“被保证人类型”不一致
- 代偿信息文件中， “企业法人姓名”与对应承保信息（ “业务发生机构”+“保单编号”一致）中的“企业法人姓名”不一致；追偿信息文件中， “企业法人姓名”与对应承保信息（ “业务发生机构”+“保单编号”一致）中的“企业法人姓名”不一致
- 代偿信息文件中， “企业法人证件类型”与对应承保信息（ “业务发生机构”+“保单编号”一致）中的“企业法人证件类型”不一致；追偿信息文件中，中“企业法人证件类型”与对应承保信息（ “业务发生机构”+“保单编号”一致）中的“企业法人证件类型”不一致
- 代偿信息文件中， “企业法人证件号码”与对应承保信息（ “业务发生机构”+“保单编号”一致）中的“企业法人证件号码”不一致；追偿信息文件中， “企业法人证件号码”与对应承保信息（ “业务发生机构”+“保单编号”一致）中的“企业法人证件号码”不一致
- 承保信息文件中，“保险起始日期”必须小于等于“文件生成时间”中的日期，“保险起始日期”必须小于等于“保险到期日期”


