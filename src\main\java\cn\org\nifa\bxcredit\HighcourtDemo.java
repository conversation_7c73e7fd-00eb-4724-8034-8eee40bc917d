package cn.org.nifa.bxcredit;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.org.nifa.bxcredit.exception.NifaApiException;

import cn.org.nifa.bxcredit.config.ConfigManager;
import cn.org.nifa.bxcredit.utils.HttpClientUtils;
import cn.org.nifa.bxcredit.utils.JsonUtils;
import cn.org.nifa.bxcredit.utils.SignatureUtils;

/**
 * 司法数据查询接口示例
 * 
 * <AUTHOR> Team
 */
public class HighcourtDemo {
    
    private static final Logger logger = LoggerFactory.getLogger(HighcourtDemo.class);
    
    /**
     * 构造请求头
     * 
     * @param bodyMap 请求体Map
     * @return 请求头Map
     */
    private static Map<String, String> getHeaderMap(Map<String, String> bodyMap) {
        // 生成10位随机数
        String randomCode = SignatureUtils.generateRandomNumber(10);
        
        // 构造签名用于身份验证
        String sign = SignatureUtils.generateInfoQuerySignature(
                ConfigManager.getOrgCode(),
                randomCode,
                bodyMap.get("sname"),
                bodyMap.get("stype"),
                bodyMap.get("sreason"),
                bodyMap.get("sno"),
                ConfigManager.getApiKey());
        
        // 构造header
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("sbankcode", ConfigManager.getOrgCode());
        headerMap.put("scode", randomCode);
        headerMap.put("sign", sign);
        return headerMap;
    }
    
    public static void main(String[] args) {
        // 构造body
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("sname", "许文");
        bodyMap.put("stype", "0");
        bodyMap.put("sreason", "b");
        bodyMap.put("sno", "640221198509101292");
        logger.debug("[body]: \n{}", JsonUtils.toJson(bodyMap));
        
        // 构造header
        Map<String, String> headerMap = getHeaderMap(bodyMap);
        logger.debug("[header]: \n{}", JsonUtils.toJson(headerMap));
        
        try {
            // 发送请求
            String response = HttpClientUtils.post(
                    ConfigManager.getHighcourtUri(),
                    headerMap,
                    bodyMap);
            
            // 处理响应
            logger.info("[response]: \n{}", response);
            
        } catch (NifaApiException e) {
            logger.error("[ERROR] 请求失败: {}", e.getMessage(), e);
        }
    }
}
