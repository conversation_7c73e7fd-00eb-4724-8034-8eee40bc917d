# 姓名生成工具类使用说明

## 概述

本项目实现了一个高效、可扩展的Java姓名生成工具类，支持生成多种格式的中文和英文姓名，并针对大数据量生成（如5000万条）进行了优化。

## 功能特性

### 基础功能
- **中文两字姓名生成**：单姓 + 单字名（如：王伟）
- **中文三字姓名生成**：单姓 + 双字名（如：李建国）
- **中文复姓四字姓名生成**：复姓 + 双字名（如：欧阳建华）
- **英文姓名生成**：First Name + Last Name（如：John Smith）
- **随机中文姓名生成**：随机选择上述中文姓名类型

### 高级功能
- **线程安全支持**：提供线程安全的生成方法，适用于多线程环境
- **批量生成到文件**：支持将生成的姓名批量写入文件
- **多线程批量生成**：使用多线程技术提高大数据量生成效率
- **大规模数据处理**：专门优化的5000万条数据生成功能
- **性能监控**：提供生成统计信息和进度报告

## 项目结构

```
src/main/java/cn/org/nifa/bxcredit/generator/
├── util/
│   ├── NameGenerator.java              # 核心姓名生成工具类
│   └── LargeScaleNameGenerator.java    # 大规模数据生成工具类
├── demo/
│   └── NameGeneratorDemo.java          # 演示程序
└── resources/data/
    ├── chinese_single_surnames.txt     # 中文单姓库
    ├── chinese_compound_surnames.txt   # 中文复姓库
    ├── chinese_single_names.txt        # 中文单字名库
    ├── chinese_double_names.txt        # 中文双字名库
    ├── english_first_names.txt         # 英文名库
    └── english_last_names.txt          # 英文姓库
```

## 快速开始

### 1. 基础姓名生成

```java
// 生成中文两字姓名
String name1 = NameGenerator.generateChineseNameTwoChar();
System.out.println(name1); // 输出：王伟

// 生成中文三字姓名
String name2 = NameGenerator.generateChineseNameThreeChar();
System.out.println(name2); // 输出：李建国

// 生成中文复姓四字姓名
String name3 = NameGenerator.generateChineseNameCompound();
System.out.println(name3); // 输出：欧阳建华

// 生成英文姓名
String name4 = NameGenerator.generateEnglishName();
System.out.println(name4); // 输出：John Smith

// 生成随机中文姓名
String name5 = NameGenerator.generateRandomChineseName();
System.out.println(name5); // 输出：张明（随机类型）
```

### 2. 线程安全生成

```java
// 在多线程环境中使用线程安全方法
String name = NameGenerator.generateChineseNameTwoCharThreadSafe();
```

### 3. 批量生成到文件

```java
// 生成1万条中文两字姓名到文件
NameGenerator.generateNamesToFile(
    10000L, 
    NameGenerator.NameType.CHINESE_TWO_CHAR, 
    "names.txt"
);
```

### 4. 多线程批量生成

```java
// 使用4个线程生成100万条姓名
NameGenerator.generateNamesToFileMultiThreaded(
    1000000L,
    NameGenerator.NameType.CHINESE_THREE_CHAR,
    "large_names.txt",
    4
);
```

### 5. 大规模数据生成

```java
// 生成5000万条姓名数据
LargeScaleNameGenerator.GenerationStatistics stats = 
    LargeScaleNameGenerator.generate50Million(
        NameGenerator.NameType.CHINESE_TWO_CHAR,
        "50million_names.txt"
    );

System.out.println(stats);
```

### 6. 自定义配置的大规模生成

```java
// 创建自定义配置
LargeScaleNameGenerator.GenerationConfig config = 
    new LargeScaleNameGenerator.GenerationConfig(
        10000000L, // 1000万条
        NameGenerator.NameType.CHINESE_THREE_CHAR,
        "custom_names.txt"
    );

// 自定义参数
config.setThreadCount(8);           // 8个线程
config.setBatchSize(20000);         // 批量大小2万
config.setProgressReportInterval(500000L); // 每50万条报告进度

// 执行生成
LargeScaleNameGenerator.GenerationStatistics stats = 
    LargeScaleNameGenerator.generateLargeScale(config);
```

## 性能特性

### 生成速度
- **单线程**：约100万个/秒
- **多线程**：根据CPU核心数可达数百万个/秒
- **5000万条数据**：预计耗时约50-100秒（取决于硬件配置）

### 内存使用
- 姓名数据在程序启动时一次性加载到内存
- 生成过程中内存使用稳定，无内存泄漏
- 支持大数据量生成而不会导致内存溢出

### 文件输出
- 使用缓冲写入提高I/O效率
- 支持UTF-8编码确保中文字符正确显示
- 批量写入减少磁盘I/O次数

## 数据统计

当前数据库包含：
- 中文单姓：100个
- 中文复姓：81个
- 中文单字名：567个
- 中文双字名：440个
- 英文名：349个
- 英文姓：301个

理论组合数：
- 中文两字姓名：56,700种
- 中文三字姓名：44,000种
- 中文复姓四字姓名：35,640种
- 英文姓名：105,049种

## 运行演示程序

```bash
# 编译项目
mvn compile

# 运行演示程序
mvn exec:java -Dexec.mainClass="cn.org.nifa.bxcredit.generator.demo.NameGeneratorDemo"
```

演示程序提供交互式菜单，包括：
1. 基础姓名生成演示
2. 线程安全姓名生成演示
3. 数据统计信息
4. 批量生成到文件
5. 多线程批量生成到文件
6. 性能测试
7. 大规模数据生成（5000万条）

## 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=NameGeneratorTest
mvn test -Dtest=LargeScaleNameGeneratorTest
```

## 扩展数据库

如需扩展姓名数据库，可以编辑 `src/main/resources/data/` 目录下的文本文件：

1. **chinese_single_surnames.txt** - 添加更多中文单姓
2. **chinese_compound_surnames.txt** - 添加更多中文复姓
3. **chinese_single_names.txt** - 添加更多中文单字名
4. **chinese_double_names.txt** - 添加更多中文双字名
5. **english_first_names.txt** - 添加更多英文名
6. **english_last_names.txt** - 添加更多英文姓

每个文件每行一个条目，程序会在启动时自动加载。

## 注意事项

1. **文件路径**：确保输出文件路径有写入权限
2. **磁盘空间**：大规模生成前请确保有足够的磁盘空间
3. **内存配置**：对于超大规模生成，可能需要调整JVM内存参数
4. **线程数量**：建议线程数不超过CPU核心数的2倍
5. **中文编码**：确保系统支持UTF-8编码以正确显示中文

## 技术实现

- **Java 11+** 兼容
- **线程安全**：使用 ThreadLocalRandom 确保多线程安全
- **高性能I/O**：使用 BufferedWriter 和批量写入
- **内存优化**：数据预加载和高效的随机访问
- **并发处理**：使用 CompletableFuture 和 ExecutorService
- **进度监控**：实时报告生成和写入进度

## 许可证

本项目遵循项目整体许可证。

---

如有问题或建议，请联系 NIFA Team。
