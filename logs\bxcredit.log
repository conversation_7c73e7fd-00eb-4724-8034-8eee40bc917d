2025-07-18 09:34:15.200 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - === NIFA测试数据生成工具启动（优化版） ===
2025-07-18 09:34:15.214 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - 开始生成个人债权融资测试数据...
2025-07-18 09:34:15.228 [main] INFO  c.o.n.b.g.g.PersonalDataGenerator - 生成10条正向测试数据
2025-07-18 09:34:15.230 [main] INFO  c.o.n.b.g.g.PersonalDataGenerator - 生成12条反向测试数据
2025-07-18 09:34:15.231 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - 生成数据总量: 22 条
2025-07-18 09:34:15.243 [main] INFO  c.o.n.b.g.v.PersonalDataValidator - 批量校验完成，总数: 22, 有效: 3, 无效: 19
2025-07-18 09:34:15.244 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - 数据校验结果 - 总数: 22, 有效: 3, 无效: 19
2025-07-18 09:34:15.246 [main] INFO  c.o.n.b.g.util.TestDataManager - 测试数据目录结构初始化完成
2025-07-18 09:34:15.252 [main] INFO  c.o.n.b.g.processor.TxtFileProcessor - 成功写入22条数据到文件: test-data/generated/personal/personal_20250718_093415/121EXPORTTRADEINFO.txt
2025-07-18 09:34:15.256 [main] INFO  c.o.n.b.g.processor.ZipFileProcessor - 文件压缩成功: test-data/generated/personal/personal_20250718_093415/121EXPORTTRADEINFO.txt -> test-data/generated/personal/personal_20250718_093415/097967874202507180934120001.zip
2025-07-18 09:34:15.260 [main] INFO  c.o.n.b.g.processor.EncFileProcessor - 开始加密文件: test-data/generated/personal/personal_20250718_093415/097967874202507180934120001.zip -> test-data/generated/personal/personal_20250718_093415/097967874202507180934120001.enc
2025-07-18 09:34:15.391 [main] INFO  c.o.n.b.c.JarBasedSM2Implementation - 检查JAR库兼容性...
2025-07-18 09:34:15.488 [main] INFO  c.o.n.b.c.JarBasedSM2Implementation - JAR库兼容性检查通过
2025-07-18 09:34:15.505 [main] WARN  c.o.n.b.c.JarBasedSM2Implementation - JAR库BouncyCastle兼容性问题，切换到降级实现: IllegalAccessError - class com.cfcc.jaf.crypto.sm.sm2.SM2 tried to access method 'void org.bouncycastle.math.ec.ECPoint$Fp.<init>(org.bouncycastle.math.ec.ECCurve, org.bouncycastle.math.ec.ECFieldElement, org.bouncycastle.math.ec.ECFieldElement)' (com.cfcc.jaf.crypto.sm.sm2.SM2 and org.bouncycastle.math.ec.ECPoint$Fp are in unnamed module of loader 'app')
2025-07-18 09:34:15.509 [main] INFO  c.o.n.b.c.StandardSM2Implementation - StandardSM2Implementation initialized with SM2 curve parameters
2025-07-18 09:34:15.563 [main] INFO  c.o.n.b.utils.JarBasedCryptoUtils - 文件加密成功: test-data/generated/personal/personal_20250718_093415/097967874202507180934120001.zip -> test-data/generated/personal/personal_20250718_093415/097967874202507180934120001.enc, 大小: 1339 -> 1436
2025-07-18 09:34:15.564 [main] INFO  c.o.n.b.g.processor.EncFileProcessor - 文件加密成功: test-data/generated/personal/personal_20250718_093415/097967874202507180934120001.enc, 大小: 1436 bytes
2025-07-18 09:34:15.564 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - 个人债权融资测试数据加密成功
2025-07-18 09:34:15.566 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - 个人债权融资测试数据生成完成:
2025-07-18 09:34:15.566 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - - TXT文件: test-data/generated/personal/personal_20250718_093415/121EXPORTTRADEINFO.txt
2025-07-18 09:34:15.566 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - - ZIP文件: test-data/generated/personal/personal_20250718_093415/097967874202507180934120001.zip
2025-07-18 09:34:15.567 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - - ENC文件: test-data/generated/personal/personal_20250718_093415/097967874202507180934120001.enc
2025-07-18 09:34:15.567 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - 开始生成企业债权融资测试数据...
2025-07-18 09:34:15.575 [main] INFO  c.o.n.b.g.g.EnterpriseDataGenerator - 生成10条企业正向测试数据
2025-07-18 09:34:15.576 [main] INFO  c.o.n.b.g.g.EnterpriseDataGenerator - 生成6条企业反向测试数据
2025-07-18 09:34:15.576 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - 生成企业数据总量: 16 条
2025-07-18 09:34:15.579 [main] INFO  c.o.n.b.g.v.EnterpriseDataValidator - 企业数据批量校验完成，总数: 16, 有效: 3, 无效: 13
2025-07-18 09:34:15.580 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - 企业数据校验结果 - 总数: 16, 有效: 3, 无效: 13
2025-07-18 09:34:15.580 [main] INFO  c.o.n.b.g.processor.TxtFileProcessor - 成功写入16条数据到文件: test-data/generated/enterprise/enterprise_20250718_093415/321EXPORTTRADEINFO.txt
2025-07-18 09:34:15.582 [main] INFO  c.o.n.b.g.processor.ZipFileProcessor - 文件压缩成功: test-data/generated/enterprise/enterprise_20250718_093415/321EXPORTTRADEINFO.txt -> test-data/generated/enterprise/enterprise_20250718_093415/097967874202507180934320001.zip
2025-07-18 09:34:15.582 [main] INFO  c.o.n.b.g.processor.EncFileProcessor - 开始加密文件: test-data/generated/enterprise/enterprise_20250718_093415/097967874202507180934320001.zip -> test-data/generated/enterprise/enterprise_20250718_093415/097967874202507180934320001.enc
2025-07-18 09:34:15.582 [main] INFO  c.o.n.b.c.JarBasedSM2Implementation - 使用降级实现进行加密
2025-07-18 09:34:15.586 [main] INFO  c.o.n.b.utils.JarBasedCryptoUtils - 文件加密成功: test-data/generated/enterprise/enterprise_20250718_093415/097967874202507180934320001.zip -> test-data/generated/enterprise/enterprise_20250718_093415/097967874202507180934320001.enc, 大小: 1486 -> 1583
2025-07-18 09:34:15.586 [main] INFO  c.o.n.b.g.processor.EncFileProcessor - 文件加密成功: test-data/generated/enterprise/enterprise_20250718_093415/097967874202507180934320001.enc, 大小: 1583 bytes
2025-07-18 09:34:15.586 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - 企业债权融资测试数据加密成功
2025-07-18 09:34:15.591 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - 企业债权融资测试数据生成完成:
2025-07-18 09:34:15.591 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - - TXT文件: test-data/generated/enterprise/enterprise_20250718_093415/321EXPORTTRADEINFO.txt
2025-07-18 09:34:15.591 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - - ZIP文件: test-data/generated/enterprise/enterprise_20250718_093415/097967874202507180934320001.zip
2025-07-18 09:34:15.591 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - - ENC文件: test-data/generated/enterprise/enterprise_20250718_093415/097967874202507180934320001.enc
2025-07-18 09:34:15.591 [main] INFO  c.o.n.b.g.TestDataGeneratorMainV2 - === 测试数据生成完成 ===
