package cn.org.nifa.bxcredit.exception;

/**
 * NIFA API异常类
 * 统一处理API调用过程中的异常
 */
public class NifaApiException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private final String errorCode;
    
    /**
     * 创建API异常
     * 
     * @param message 错误消息
     */
    public NifaApiException(String message) {
        this("UNKNOWN", message);
    }
    
    /**
     * 创建API异常
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     */
    public NifaApiException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    /**
     * 创建API异常
     * 
     * @param message 错误消息
     * @param cause 原始异常
     */
    public NifaApiException(String message, Throwable cause) {
        this("UNKNOWN", message, cause);
    }
    
    /**
     * 创建API异常
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param cause 原始异常
     */
    public NifaApiException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    /**
     * 获取错误代码
     * 
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }
}