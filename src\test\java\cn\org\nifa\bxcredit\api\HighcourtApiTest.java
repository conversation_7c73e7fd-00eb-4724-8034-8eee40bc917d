package cn.org.nifa.bxcredit.api;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.notNullValue;

import java.util.HashMap;
import java.util.Map;

import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import cn.org.nifa.bxcredit.config.ConfigManager;
import cn.org.nifa.bxcredit.constants.TestDataConstants;
import cn.org.nifa.bxcredit.utils.SignatureUtils;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;

/**
 * 司法数据查询接口测试
 */
public class HighcourtApiTest extends BaseApiTest {
    
    private RequestSpecification requestSpec;
    
    @BeforeClass
    public void setup() {
        requestSpec = new RequestSpecBuilder()
                .setBaseUri(ConfigManager.getHighcourtUri())
                .setContentType(ContentType.JSON)
                .build();
    }
    
    @Test
    public void testHighcourtQuery() {
        // 构建请求体
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("sname", TestDataConstants.PersonalInfo.TEST_NAME);
        bodyMap.put("stype", TestDataConstants.PersonalInfo.TEST_TYPE);
        bodyMap.put("sreason", TestDataConstants.PersonalInfo.TEST_REASON_B);
        bodyMap.put("sno", TestDataConstants.PersonalInfo.TEST_ID_NUMBER);
        
        // 生成随机码
        String randomCode = SignatureUtils.generateRandomNumber(10);
        
        // 构建签名
        String sign = SignatureUtils.generateInfoQuerySignature(
                ConfigManager.getOrgCode(),
                randomCode,
                bodyMap.get("sname"),
                bodyMap.get("stype"),
                bodyMap.get("sreason"),
                bodyMap.get("sno"),
                ConfigManager.getApiKey());
        
        // 构建请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("sbankcode", ConfigManager.getOrgCode());
        headers.put("scode", randomCode);
        headers.put("sign", sign);
        
        // 发送请求并验证
        given()
            .spec(requestSpec)
            .headers(headers)
            .body(bodyMap)
        .when()
            .post()
        .then()
            .statusCode(200)
            .body("msgCode", notNullValue());
    }
}