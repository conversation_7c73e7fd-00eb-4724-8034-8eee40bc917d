package cn.org.nifa.bxcredit.demo;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.databind.JsonNode;

import cn.org.nifa.bxcredit.config.ConfigManager;
import cn.org.nifa.bxcredit.constants.TestDataConstants;
import cn.org.nifa.bxcredit.utils.HttpClientUtils;
import cn.org.nifa.bxcredit.utils.JsonUtils;
import cn.org.nifa.bxcredit.utils.SignatureUtils;

/**
 * 现代化API请求示例
 * 使用OkHttp和Jackson实现高性能API请求
 * 
 * <AUTHOR> Team
 */
public class ModernApiDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Modern API Demo ===");
        
        try {
            // 执行个人信用查询
            queryPersonalCredit();
            
        } catch (Exception e) {
            System.err.println("API request failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 个人信用查询示例
     */
    private static void queryPersonalCredit() throws IOException {
        System.out.println("\n=== Personal Credit Query ===");
        
        // 生成随机码
        String randomCode = SignatureUtils.generateRandomNumber(10);
        
        // 构建请求体
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("sreason", TestDataConstants.PersonalInfo.TEST_REASON_B);
        bodyMap.put("sname", TestDataConstants.PersonalInfo.TEST_NAME);
        bodyMap.put("stype", TestDataConstants.PersonalInfo.TEST_TYPE);
        bodyMap.put("sno", TestDataConstants.PersonalInfo.TEST_ID_NUMBER);
        
        // 生成签名
        String preparedSign = ConfigManager.getOrgCode()
                + randomCode
                + bodyMap.get("sname")
                + bodyMap.get("stype")
                + bodyMap.get("sreason")
                + bodyMap.get("sno")
                + ConfigManager.getApiKey();
        String sign = SignatureUtils.sha256(preparedSign);
        
        // 构建请求头
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("sbankcode", ConfigManager.getOrgCode());
        headerMap.put("scode", randomCode);
        headerMap.put("sign", sign);
        
        // 打印请求信息
        System.out.println("Request URL: " + ConfigManager.getInfoTestUri());
        System.out.println("Request Headers: " + JsonUtils.toJson(headerMap));
        System.out.println("Request Body: " + JsonUtils.toJson(bodyMap));
        
        // 发送请求
        String response = HttpClientUtils.post(
                ConfigManager.getInfoTestUri(),
                headerMap,
                bodyMap
        );
        
        // 解析响应
        JsonNode responseJson = JsonUtils.parseJson(response);
        System.out.println("\nResponse: " + responseJson.toPrettyString());
        
        // 检查响应状态
        String msgCode = responseJson.path("msgCode").asText();
        String msgContent = responseJson.path("msgContent").asText();
        
        System.out.println("\nResult: " + msgCode + " - " + msgContent);
    }
}