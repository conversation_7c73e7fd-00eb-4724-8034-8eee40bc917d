# 测试环境配置
nifa.org.code=097967874
nifa.api.key=mNak7HBrKFFXMn72w5

# API端点配置 - 测试环境
nifa.endpoints.info.test=http://172.18.13.78:81//NewNifaServer/context/info/json/upload
nifa.endpoints.info.md5=http://172.18.13.78:81//NifaCreditServer/context/info/json/uploadnew
nifa.endpoints.data=http://172.18.13.78:81//NewNifaServer1/context/data/json/asynlist
nifa.endpoints.task=http://172.18.13.78:81//NewNifaServer1/context/task/json/upload
nifa.endpoints.query.count=http://172.18.13.78:81//NewNifaServer/context/info/json/querycontralNew
nifa.endpoints.highcourt=http://172.18.13.78:81//NifaCreditServer/nifa/sxzbr/json/people

# 加密密钥配置
nifa.crypto.pub.x=da68acf0ae676725fbd70894dfe7aaac5af008009bc30c13daf4e691f575d12a
nifa.crypto.pub.y=76d4a0f90065ad86221287a74bf99862e92124282dba02b94782ff50f8ea6701
nifa.crypto.private=143c18f085e49697b7918d1a03a90c49bbe8ca1b741511bbac9e81cceb7563d5