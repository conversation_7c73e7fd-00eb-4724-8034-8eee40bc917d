package cn.org.nifa.bxcredit.generator.util;

import org.testng.Assert;
import org.testng.annotations.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * LargeScaleNameGenerator 工具类单元测试
 * 
 * <AUTHOR> Team
 */
public class LargeScaleNameGeneratorTest {
    
    /**
     * 测试生成配置类
     */
    @Test
    public void testGenerationConfig() {
        LargeScaleNameGenerator.GenerationConfig config = 
            new LargeScaleNameGenerator.GenerationConfig(
                1000L, NameGenerator.NameType.CHINESE_TWO_CHAR, "test.txt");
        
        Assert.assertEquals(config.getTotalCount(), 1000L);
        Assert.assertEquals(config.getNameType(), NameGenerator.NameType.CHINESE_TWO_CHAR);
        Assert.assertEquals(config.getOutputFilePath(), "test.txt");
        Assert.assertTrue(config.getThreadCount() > 0);
        Assert.assertTrue(config.getBatchSize() > 0);
        Assert.assertTrue(config.getQueueCapacity() > 0);
        Assert.assertTrue(config.isEnableProgressReport());
        Assert.assertTrue(config.getProgressReportInterval() > 0);
        
        // 测试设置方法
        config.setThreadCount(8);
        config.setBatchSize(5000);
        config.setEnableProgressReport(false);
        
        Assert.assertEquals(config.getThreadCount(), 8);
        Assert.assertEquals(config.getBatchSize(), 5000);
        Assert.assertFalse(config.isEnableProgressReport());
        
        System.out.println("生成配置测试通过");
    }
    
    /**
     * 测试生成统计信息类
     */
    @Test
    public void testGenerationStatistics() {
        LargeScaleNameGenerator.GenerationStatistics stats = 
            new LargeScaleNameGenerator.GenerationStatistics(1000L, 1000L, 2000L);
        
        Assert.assertEquals(stats.getTotalCount(), 1000L);
        Assert.assertEquals(stats.getActualGenerated(), 1000L);
        Assert.assertEquals(stats.getTotalTimeMs(), 2000L);
        Assert.assertEquals(stats.getAverageSpeed(), 500.0, 0.1); // 1000 * 1000 / 2000 = 500
        Assert.assertTrue(stats.getEstimatedFileSizeBytes() > 0);
        
        String statsString = stats.toString();
        Assert.assertTrue(statsString.contains("生成统计"));
        Assert.assertTrue(statsString.contains("1,000"));
        Assert.assertTrue(statsString.contains("500"));
        
        System.out.println("生成统计信息测试通过");
        System.out.println(stats);
    }
    
    /**
     * 测试小规模生成（用于验证功能）
     */
    @Test
    public void testSmallScaleGeneration() throws IOException, InterruptedException {
        String testFilePath = "test_large_scale_small.txt";
        long testCount = 100L;
        
        try {
            LargeScaleNameGenerator.GenerationConfig config = 
                new LargeScaleNameGenerator.GenerationConfig(
                    testCount, NameGenerator.NameType.CHINESE_TWO_CHAR, testFilePath);
            
            config.setThreadCount(2);
            config.setBatchSize(50);
            config.setProgressReportInterval(50);
            
            LargeScaleNameGenerator.GenerationStatistics stats = 
                LargeScaleNameGenerator.generateLargeScale(config);
            
            // 验证统计信息
            Assert.assertEquals(stats.getTotalCount(), testCount);
            Assert.assertEquals(stats.getActualGenerated(), testCount);
            Assert.assertTrue(stats.getTotalTimeMs() > 0);
            Assert.assertTrue(stats.getAverageSpeed() > 0);
            
            // 验证文件
            Path filePath = Paths.get(testFilePath);
            Assert.assertTrue(Files.exists(filePath), "输出文件应该存在");
            
            List<String> lines = Files.readAllLines(filePath);
            Assert.assertEquals(lines.size(), testCount, "文件行数应该等于生成数量");
            
            // 验证每行都是有效的两字中文姓名
            for (String line : lines) {
                Assert.assertEquals(line.length(), 2, "每行应该是两字姓名");
                Assert.assertTrue(isChineseCharacters(line), "每行应该只包含中文字符");
            }
            
            System.out.println("小规模生成测试成功");
            System.out.println(stats);
            
        } finally {
            // 清理测试文件
            Files.deleteIfExists(Paths.get(testFilePath));
        }
    }
    
    /**
     * 测试中等规模生成
     */
    @Test
    public void testMediumScaleGeneration() throws IOException, InterruptedException {
        String testFilePath = "test_large_scale_medium.txt";
        long testCount = 10000L;
        
        try {
            LargeScaleNameGenerator.GenerationConfig config = 
                new LargeScaleNameGenerator.GenerationConfig(
                    testCount, NameGenerator.NameType.CHINESE_THREE_CHAR, testFilePath);
            
            config.setThreadCount(4);
            config.setBatchSize(1000);
            config.setProgressReportInterval(5000);
            
            long startTime = System.currentTimeMillis();
            LargeScaleNameGenerator.GenerationStatistics stats = 
                LargeScaleNameGenerator.generateLargeScale(config);
            long endTime = System.currentTimeMillis();
            
            // 验证统计信息
            Assert.assertEquals(stats.getTotalCount(), testCount);
            Assert.assertEquals(stats.getActualGenerated(), testCount);
            Assert.assertTrue(stats.getTotalTimeMs() > 0);
            Assert.assertTrue(stats.getAverageSpeed() > 0);
            
            // 验证文件
            Path filePath = Paths.get(testFilePath);
            Assert.assertTrue(Files.exists(filePath), "输出文件应该存在");
            
            List<String> lines = Files.readAllLines(filePath);
            Assert.assertEquals(lines.size(), testCount, "文件行数应该等于生成数量");
            
            // 抽样验证姓名格式
            for (int i = 0; i < Math.min(100, lines.size()); i++) {
                String line = lines.get(i);
                Assert.assertEquals(line.length(), 3, "每行应该是三字姓名");
                Assert.assertTrue(isChineseCharacters(line), "每行应该只包含中文字符");
            }
            
            System.out.println("中等规模生成测试成功");
            System.out.println(stats);
            System.out.println("实际耗时: " + (endTime - startTime) + "ms");
            
        } finally {
            // 清理测试文件
            Files.deleteIfExists(Paths.get(testFilePath));
        }
    }
    
    /**
     * 测试时间估算功能
     */
    @Test
    public void testTimeEstimation() {
        double estimatedTime1 = LargeScaleNameGenerator.estimateGenerationTime(
            1_000_000L, NameGenerator.NameType.CHINESE_TWO_CHAR);
        double estimatedTime2 = LargeScaleNameGenerator.estimateGenerationTime(
            50_000_000L, NameGenerator.NameType.CHINESE_THREE_CHAR);
        
        Assert.assertTrue(estimatedTime1 > 0, "估算时间应该大于0");
        Assert.assertTrue(estimatedTime2 > estimatedTime1, "更大数量的估算时间应该更长");
        
        System.out.println("时间估算测试:");
        System.out.println("100万条中文两字姓名估算时间: " + String.format("%.2f", estimatedTime1) + "秒");
        System.out.println("5000万条中文三字姓名估算时间: " + String.format("%.2f", estimatedTime2) + "秒");
    }
    
    /**
     * 测试文件大小估算功能
     */
    @Test
    public void testFileSizeEstimation() {
        long estimatedSize1 = LargeScaleNameGenerator.estimateFileSize(
            1_000_000L, NameGenerator.NameType.CHINESE_TWO_CHAR);
        long estimatedSize2 = LargeScaleNameGenerator.estimateFileSize(
            1_000_000L, NameGenerator.NameType.CHINESE_THREE_CHAR);
        long estimatedSize3 = LargeScaleNameGenerator.estimateFileSize(
            1_000_000L, NameGenerator.NameType.ENGLISH);
        
        Assert.assertTrue(estimatedSize1 > 0, "估算文件大小应该大于0");
        Assert.assertTrue(estimatedSize2 > estimatedSize1, "三字姓名文件应该比两字姓名文件大");
        Assert.assertTrue(estimatedSize3 > 0, "英文姓名文件大小应该大于0");
        
        System.out.println("文件大小估算测试:");
        System.out.println("100万条中文两字姓名估算大小: " + String.format("%.2f", estimatedSize1 / 1024.0 / 1024.0) + "MB");
        System.out.println("100万条中文三字姓名估算大小: " + String.format("%.2f", estimatedSize2 / 1024.0 / 1024.0) + "MB");
        System.out.println("100万条英文姓名估算大小: " + String.format("%.2f", estimatedSize3 / 1024.0 / 1024.0) + "MB");
    }
    
    /**
     * 测试不同姓名类型的生成
     */
    @Test
    public void testDifferentNameTypes() throws IOException, InterruptedException {
        NameGenerator.NameType[] nameTypes = {
            NameGenerator.NameType.CHINESE_TWO_CHAR,
            NameGenerator.NameType.CHINESE_THREE_CHAR,
            NameGenerator.NameType.CHINESE_COMPOUND,
            NameGenerator.NameType.ENGLISH,
            NameGenerator.NameType.CHINESE_RANDOM
        };
        
        for (NameGenerator.NameType nameType : nameTypes) {
            String testFilePath = "test_type_" + nameType.name().toLowerCase() + ".txt";
            long testCount = 50L;
            
            try {
                LargeScaleNameGenerator.GenerationConfig config = 
                    new LargeScaleNameGenerator.GenerationConfig(testCount, nameType, testFilePath);
                
                config.setThreadCount(2);
                config.setBatchSize(25);
                config.setEnableProgressReport(false); // 关闭进度报告以减少输出
                
                LargeScaleNameGenerator.GenerationStatistics stats = 
                    LargeScaleNameGenerator.generateLargeScale(config);
                
                // 验证统计信息
                Assert.assertEquals(stats.getActualGenerated(), testCount);
                
                // 验证文件
                Path filePath = Paths.get(testFilePath);
                Assert.assertTrue(Files.exists(filePath), "输出文件应该存在");
                
                List<String> lines = Files.readAllLines(filePath);
                Assert.assertEquals(lines.size(), testCount, "文件行数应该等于生成数量");
                
                // 验证姓名格式
                validateNameFormat(lines, nameType);
                
                System.out.println("姓名类型 " + nameType + " 测试通过，生成速度: " + 
                                 String.format("%.0f", stats.getAverageSpeed()) + " 个/秒");
                
            } finally {
                // 清理测试文件
                Files.deleteIfExists(Paths.get(testFilePath));
            }
        }
    }
    
    /**
     * 性能基准测试
     */
    @Test
    public void testPerformanceBenchmark() throws IOException, InterruptedException {
        String testFilePath = "test_performance_benchmark.txt";
        long testCount = 50000L; // 5万条数据用于性能测试
        
        try {
            LargeScaleNameGenerator.GenerationConfig config = 
                new LargeScaleNameGenerator.GenerationConfig(
                    testCount, NameGenerator.NameType.CHINESE_TWO_CHAR, testFilePath);
            
            config.setThreadCount(Runtime.getRuntime().availableProcessors());
            config.setBatchSize(10000);
            config.setProgressReportInterval(25000);
            
            LargeScaleNameGenerator.GenerationStatistics stats = 
                LargeScaleNameGenerator.generateLargeScale(config);
            
            // 性能断言
            Assert.assertTrue(stats.getAverageSpeed() > 10000, "生成速度应该超过每秒1万个"); // 最低性能要求
            Assert.assertEquals(stats.getActualGenerated(), testCount);
            
            System.out.println("性能基准测试结果:");
            System.out.println(stats);
            
            // 验证文件完整性
            Path filePath = Paths.get(testFilePath);
            List<String> lines = Files.readAllLines(filePath);
            Assert.assertEquals(lines.size(), testCount);
            
        } finally {
            // 清理测试文件
            Files.deleteIfExists(Paths.get(testFilePath));
        }
    }
    
    /**
     * 验证姓名格式
     */
    private void validateNameFormat(List<String> names, NameGenerator.NameType nameType) {
        for (String name : names) {
            switch (nameType) {
                case CHINESE_TWO_CHAR:
                    Assert.assertEquals(name.length(), 2, "两字姓名长度应为2");
                    Assert.assertTrue(isChineseCharacters(name), "应该只包含中文字符");
                    break;
                case CHINESE_THREE_CHAR:
                    Assert.assertEquals(name.length(), 3, "三字姓名长度应为3");
                    Assert.assertTrue(isChineseCharacters(name), "应该只包含中文字符");
                    break;
                case CHINESE_COMPOUND:
                    Assert.assertEquals(name.length(), 4, "复姓四字姓名长度应为4");
                    Assert.assertTrue(isChineseCharacters(name), "应该只包含中文字符");
                    break;
                case ENGLISH:
                    Assert.assertTrue(name.contains(" "), "英文姓名应包含空格");
                    Assert.assertTrue(isEnglishName(name), "应该是有效的英文姓名格式");
                    break;
                case CHINESE_RANDOM:
                    Assert.assertTrue(name.length() >= 2 && name.length() <= 4, "中文姓名长度应在2-4之间");
                    Assert.assertTrue(isChineseCharacters(name), "应该只包含中文字符");
                    break;
            }
        }
    }
    
    /**
     * 检查字符串是否只包含中文字符
     */
    private boolean isChineseCharacters(String str) {
        return str.matches("[\u4e00-\u9fa5]+");
    }
    
    /**
     * 检查字符串是否是有效的英文姓名格式
     */
    private boolean isEnglishName(String str) {
        return str.matches("[A-Za-z]+ [A-Za-z]+");
    }
}
