package cn.org.nifa.bxcredit.generator.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 个人债权融资数据模型
 * 
 * <AUTHOR> Team
 */
public class PersonalLoanData {
    
    private String name;                  // 姓名
    private String certificateType;       // 证件类型
    private String certificateNumber;     // 证件号码
    private String businessInstitution;   // 业务发生机构
    private String businessNumber;        // 业务号
    private String businessType;          // 业务类型
    private String businessCategory;      // 业务种类
    private String openDate;              // 开户日期
    private String dueDate;               // 到期日期
    private String creditLimit;           // 授信额度
    private String businessDate;          // 业务发生日期
    private String repaymentStatus;       // 本月还款状态
    private String balance;               // 余额
    private String overdueAmount;         // 当前逾期总额
    
    // 构造函数
    public PersonalLoanData() {}
    
    // Builder模式构造函数
    private PersonalLoanData(Builder builder) {
        this.name = builder.name;
        this.certificateType = builder.certificateType;
        this.certificateNumber = builder.certificateNumber;
        this.businessInstitution = builder.businessInstitution;
        this.businessNumber = builder.businessNumber;
        this.businessType = builder.businessType;
        this.businessCategory = builder.businessCategory;
        this.openDate = builder.openDate;
        this.dueDate = builder.dueDate;
        this.creditLimit = builder.creditLimit;
        this.businessDate = builder.businessDate;
        this.repaymentStatus = builder.repaymentStatus;
        this.balance = builder.balance;
        this.overdueAmount = builder.overdueAmount;
    }
    
    // Getter和Setter方法
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getCertificateType() { return certificateType; }
    public void setCertificateType(String certificateType) { this.certificateType = certificateType; }
    
    public String getCertificateNumber() { return certificateNumber; }
    public void setCertificateNumber(String certificateNumber) { this.certificateNumber = certificateNumber; }
    
    public String getBusinessInstitution() { return businessInstitution; }
    public void setBusinessInstitution(String businessInstitution) { this.businessInstitution = businessInstitution; }
    
    public String getBusinessNumber() { return businessNumber; }
    public void setBusinessNumber(String businessNumber) { this.businessNumber = businessNumber; }
    
    public String getBusinessType() { return businessType; }
    public void setBusinessType(String businessType) { this.businessType = businessType; }
    
    public String getBusinessCategory() { return businessCategory; }
    public void setBusinessCategory(String businessCategory) { this.businessCategory = businessCategory; }
    
    public String getOpenDate() { return openDate; }
    public void setOpenDate(String openDate) { this.openDate = openDate; }
    
    public String getDueDate() { return dueDate; }
    public void setDueDate(String dueDate) { this.dueDate = dueDate; }
    
    public String getCreditLimit() { return creditLimit; }
    public void setCreditLimit(String creditLimit) { this.creditLimit = creditLimit; }
    
    public String getBusinessDate() { return businessDate; }
    public void setBusinessDate(String businessDate) { this.businessDate = businessDate; }
    
    public String getRepaymentStatus() { return repaymentStatus; }
    public void setRepaymentStatus(String repaymentStatus) { this.repaymentStatus = repaymentStatus; }
    
    public String getBalance() { return balance; }
    public void setBalance(String balance) { this.balance = balance; }
    
    public String getOverdueAmount() { return overdueAmount; }
    public void setOverdueAmount(String overdueAmount) { this.overdueAmount = overdueAmount; }
    
    /**
     * 校验数据
     * 
     * @return 校验错误列表
     */
    public List<String> validate() {
        List<String> errors = new ArrayList<>();
        
        // 基础字段校验
        if (name == null || name.trim().isEmpty()) {
            errors.add("姓名不能为空");
        } else if (name.length() > 30) {
            errors.add("姓名长度不能超过30");
        }
        if (certificateType == null || certificateType.trim().isEmpty()) {
            errors.add("证件类型不能为空");
        }
        if (certificateNumber == null || certificateNumber.trim().isEmpty()) {
            errors.add("证件号码不能为空");
        }
        if (businessNumber == null || businessNumber.trim().isEmpty()) {
            errors.add("业务号不能为空");
        }
        
        return errors;
    }
    
    /**
     * Builder类
     */
    public static class Builder {
        private String name;
        private String certificateType;
        private String certificateNumber;
        private String businessInstitution;
        private String businessNumber;
        private String businessType;
        private String businessCategory;
        private String openDate;
        private String dueDate;
        private String creditLimit;
        private String businessDate;
        private String repaymentStatus;
        private String balance;
        private String overdueAmount;
        
        public Builder name(String name) {
            this.name = name;
            return this;
        }
        
        public Builder certificateType(String certificateType) {
            this.certificateType = certificateType;
            return this;
        }
        
        public Builder certificateNumber(String certificateNumber) {
            this.certificateNumber = certificateNumber;
            return this;
        }
        
        public Builder businessInstitution(String businessInstitution) {
            this.businessInstitution = businessInstitution;
            return this;
        }
        
        public Builder businessNumber(String businessNumber) {
            this.businessNumber = businessNumber;
            return this;
        }
        
        public Builder businessType(String businessType) {
            this.businessType = businessType;
            return this;
        }
        
        public Builder businessCategory(String businessCategory) {
            this.businessCategory = businessCategory;
            return this;
        }
        
        public Builder openDate(String openDate) {
            this.openDate = openDate;
            return this;
        }
        
        public Builder dueDate(String dueDate) {
            this.dueDate = dueDate;
            return this;
        }
        
        public Builder creditLimit(String creditLimit) {
            this.creditLimit = creditLimit;
            return this;
        }
        
        public Builder businessDate(String businessDate) {
            this.businessDate = businessDate;
            return this;
        }
        
        public Builder repaymentStatus(String repaymentStatus) {
            this.repaymentStatus = repaymentStatus;
            return this;
        }
        
        public Builder balance(String balance) {
            this.balance = balance;
            return this;
        }
        
        public Builder overdueAmount(String overdueAmount) {
            this.overdueAmount = overdueAmount;
            return this;
        }
        
        public PersonalLoanData build() {
            return new PersonalLoanData(this);
        }
    }
    
    public static Builder builder() {
        return new Builder();
    }
}