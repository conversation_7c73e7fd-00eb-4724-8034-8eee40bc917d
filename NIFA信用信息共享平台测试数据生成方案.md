NIFA信用信息共享平台测试数据生成方案
1. 项目概述
本文档详细描述了基于NIFA信用信息共享平台API测试框架的测试数据生成方案，包括个人债权融资类和企业债权融资类业务数据的生成、校验、压缩和加密流程。

2. 需求分析
2.1 业务需求
需要生成符合NIFA信用信息共享平台规范的测试数据，包括：

个人债权融资类业务数据（121EXPORTTRADEINFO.txt）

企业债权融资类业务数据（321EXPORTTRADEINFO.txt）

每种数据类型需要包含正向测试数据（符合规则）和反向测试数据（违反特定规则）。

2.2 技术需求
数据生成：根据规则生成符合要求的数据

数据校验：确保生成的数据符合业务规则

文件处理：生成TXT文件，压缩为ZIP文件，加密为ENC文件

命名规则：确保文件名符合平台要求

3. 技术栈选择
3.1 核心技术
技术领域	选择方案	说明
编程语言	Java 11+	与现有项目保持一致
构建工具	Maven	依赖管理和项目构建
测试框架	TestNG	支持数据驱动测试
3.2 关键依赖库
依赖库	版本	用途
java.time	JDK内置	日期处理和计算
commons-lang3	3.12.0	字符串处理、随机数生成
jackson-databind	2.16.1	JSON处理
commons-io	2.15.1	文件操作
java.util.zip	JDK内置	ZIP文件压缩
bcprov-jdk18on	1.77	SM2加密算法支持
lombok	1.18.30	简化POJO类开发
slf4j + logback	2.0.9	日志记录
4. 系统架构设计
4.1 模块划分
cn.org.nifa.bxcredit.generator/
├── model/                  # 数据模型
│   ├── PersonalLoanData.java   # 个人债权融资数据模型
│   └── EnterpriseLoanData.java # 企业债权融资数据模型
├── generator/              # 数据生成器
│   ├── DataGenerator.java      # 数据生成器接口
│   ├── PersonalDataGenerator.java  # 个人数据生成器
│   └── EnterpriseDataGenerator.java # 企业数据生成器
├── validator/              # 数据校验器
│   ├── DataValidator.java      # 数据校验器接口
│   ├── PersonalDataValidator.java  # 个人数据校验器
│   └── EnterpriseDataValidator.java # 企业数据校验器
├── processor/              # 文件处理器
│   ├── FileProcessor.java      # 文件处理接口
│   ├── TxtFileProcessor.java   # TXT文件生成
│   ├── ZipFileProcessor.java   # ZIP文件压缩
│   └── EncFileProcessor.java   # ENC文件加密
└── util/                   # 工具类
    ├── IdCardGenerator.java    # 身份证号生成器
    ├── BusinessCodeGenerator.java # 业务号生成器
    ├── DateUtils.java          # 日期工具类
    └── ValidationUtils.java    # 校验工具类

Copy
4.2 数据流程
数据模型定义 → 数据生成 → 数据校验 → TXT文件生成 → ZIP文件压缩 → SM2加密 → ENC文件生成

Copy
5. 详细设计
5.1 数据模型设计
5.1.1 个人债权融资数据模型
@Data
@Builder
public class PersonalLoanData {
    private String certificateType;       // 证件类型
    private String certificateNumber;     // 证件号码
    private String businessInstitution;   // 业务发生机构
    private String businessNumber;        // 业务号
    private String businessType;          // 业务类型
    private String businessCategory;      // 业务种类
    private String openDate;              // 开户日期
    private String dueDate;               // 到期日期
    private String creditLimit;           // 授信额度
    private String businessDate;          // 业务发生日期
    private String repaymentStatus;       // 本月还款状态
    private String balance;               // 余额
    private String overdueAmount;         // 当前逾期总额
    
    // 校验方法
    public List<String> validate() {
        // 实现校验逻辑
    }
}

Copy
java
5.1.2 企业债权融资数据模型
@Data
@Builder
public class EnterpriseLoanData {
    private String enterpriseName;        // 企业名称
    private String enterpriseCertType;    // 企业证件类型
    private String enterpriseCode;        // 企业代码
    private String legalPersonName;       // 企业法人姓名
    private String legalPersonCertType;   // 企业法人证件类型
    private String legalPersonCertNumber; // 企业法人证件号码
    private String businessInstitution;   // 业务发生机构
    private String businessNumber;        // 业务号
    private String businessType;          // 业务类型
    private String businessCategory;      // 业务种类
    private String openDate;              // 开户日期
    private String dueDate;               // 到期日期
    private String creditLimit;           // 授信额度
    private String businessDate;          // 业务发生日期
    private String repaymentStatus;       // 本月还款状态
    private String balance;               // 余额
    private String overdueAmount;         // 当前逾期总额
    private String compensationInstitution; // 当前代偿机构
    private String compensationAmount;    // 当前代偿总额
    
    // 校验方法
    public List<String> validate() {
        // 实现校验逻辑
    }
}


Copy
java
5.2 数据生成器设计
5.2.1 数据生成器接口
public interface DataGenerator<T> {
    // 生成单条正向测试数据
    T generatePositiveData();
    
    // 生成多条正向测试数据
    List<T> generatePositiveDataBatch(int count);
    
    // 生成违反特定规则的反向测试数据
    T generateNegativeData(String ruleViolation);
    
    // 生成多条反向测试数据，覆盖所有规则
    List<T> generateAllNegativeData();
}

Copy
5.2.2 个人数据生成器实现
@Slf4j
public class PersonalDataGenerator implements DataGenerator<PersonalLoanData> {
    // 实现数据生成接口方法
    
    // 生成证件号码
    private String generateCertificateNumber(String type) {
        if ("0".equals(type)) {
            return IdCardGenerator.generateRandomIdCard();
        }
        // 其他证件类型的生成逻辑
        return null;
    }
    
    // 生成业务号
    private String generateBusinessNumber() {
        return "BN" + RandomStringUtils.randomNumeric(30);
    }
    
    // 其他辅助方法
}

Copy
java
5.3 数据校验器设计
5.3.1 校验器接口
public interface DataValidator<T> {
    // 校验单条数据
    ValidationResult validate(T data);
    
    // 校验数据集合
    List<ValidationResult> validateBatch(List<T> dataList);
}

@Data
public class ValidationResult {
    private boolean valid;
    private List<String> errors;
}

Copy
java
5.3.2 企业数据校验器实现
@Slf4j
public class EnterpriseDataValidator implements DataValidator<EnterpriseLoanData> {
    @Override
    public ValidationResult validate(EnterpriseLoanData data) {
        ValidationResult result = new ValidationResult();
        List<String> errors = new ArrayList<>();
        
        // 校验企业名称
        if (data.getEnterpriseName() == null || 
            data.getEnterpriseName().length() > 60) {
            errors.add("企业名称长度必须大于0小于等于60");
        }
        
        // 校验企业证件类型
        if (!Arrays.asList("a", "b").contains(data.getEnterpriseCertType())) {
            errors.add("企业证件类型必须为a或b");
        }
        
        // 更多校验规则...
        
        result.setValid(errors.isEmpty());
        result.setErrors(errors);
        return result;
    }
    
    // 实现批量校验方法
}


Copy
java
5.4 文件处理器设计
5.4.1 TXT文件处理器
@Slf4j
public class TxtFileProcessor<T> {
    private static final String DELIMITER = "|";  // 字段分隔符
    
    // 将数据写入TXT文件
    public void writeToFile(List<T> dataList, String filePath, Function<T, String> converter) {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(filePath), StandardCharsets.UTF_8))) {
            for (T data : dataList) {
                writer.write(converter.apply(data));
                writer.newLine();
            }
            log.info("成功写入{}条数据到文件: {}", dataList.size(), filePath);
        } catch (IOException e) {
            log.error("写入文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("写入文件失败", e);
        }
    }
    
    // 将对象转换为分隔符分隔的字符串
    public String convertToDelimitedString(Object... values) {
        return Arrays.stream(values)
                .map(String::valueOf)
                .collect(Collectors.joining(DELIMITER));
    }
}


Copy
5.4.2 ZIP文件处理器
@Slf4j
public class ZipFileProcessor {
    // 压缩文件
    public void zipFile(String sourceFile, String zipFile) {
        try (FileOutputStream fos = new FileOutputStream(zipFile);
             ZipOutputStream zos = new ZipOutputStream(fos);
             FileInputStream fis = new FileInputStream(sourceFile)) {
            
            ZipEntry zipEntry = new ZipEntry(new File(sourceFile).getName());
            zos.putNextEntry(zipEntry);
            
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                zos.write(buffer, 0, length);
            }
            
            zos.closeEntry();
            log.info("文件压缩成功: {}", zipFile);
        } catch (IOException e) {
            log.error("文件压缩失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件压缩失败", e);
        }
    }
}

Copy
5.4.3 ENC文件处理器
@Slf4j
public class EncFileProcessor {
    // SM2加密
    public void encryptFile(String sourceFile, String encFile, 
                           String publicKeyX, String publicKeyY) {
        try {
            // 使用SM2CryptoService进行加密
            SM2CryptoService cryptoService = new SM2CryptoService(publicKeyX, publicKeyY);
            File encryptedFile = cryptoService.encryptFile(sourceFile, encFile);
            
            log.info("文件加密成功: {}", encFile);
        } catch (Exception e) {
            log.error("文件加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件加密失败", e);
        }
    }
}

Copy
5.5 工具类设计
5.5.1 身份证号生成器
public class IdCardGenerator {
    // 生成随机身份证号
    public static String generateRandomIdCard() {
        // 实现身份证号生成逻辑
    }
    
    // 校验身份证号
    public static boolean validateIdCard(String idCard) {
        // 实现身份证号校验逻辑
    }
}

Copy
5.5.2 日期工具类
public class DateUtils {
    // 获取当前日期，格式YYYYMMDD
    public static String getCurrentDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }
    
    // 获取当月最后一天，格式YYYYMMDD
    public static String getLastDayOfMonth() {
        LocalDate now = LocalDate.now();
        LocalDate lastDay = now.withDayOfMonth(now.lengthOfMonth());
        return lastDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }
    
    // 计算两个日期之间的天数
    public static long daysBetween(String date1, String date2) {
        LocalDate d1 = LocalDate.parse(date1, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate d2 = LocalDate.parse(date2, DateTimeFormatter.ofPattern("yyyyMMdd"));
        return ChronoUnit.DAYS.between(d1, d2);
    }
    
    // 校验日期格式和有效性
    public static boolean isValidDate(String date) {
        try {
            LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyyMMdd"));
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}


Copy
java
6. 实现计划
6.1 开发阶段
阶段	任务	时间估计	优先级
1	数据模型实现	2天	高
2	工具类开发	3天	高
3	数据生成器实现	5天	高
4	数据校验器实现	4天	高
5	文件处理器实现	3天	中
6	单元测试	3天	中
7	集成测试	2天	中
8	文档完善	2天	低
6.2 测试计划
单元测试：

测试各个工具类的功能

测试数据生成器的正向和反向数据生成

测试数据校验器的校验逻辑

集成测试：

测试完整的数据生成、校验、文件处理流程

测试生成的文件是否符合规范

性能测试：

测试大批量数据生成的性能

测试文件压缩和加密的性能

6.3 交付物
源代码：包含所有实现的Java类

单元测试：覆盖主要功能点的测试用例

使用文档：详细说明如何使用数据生成工具

示例数据：生成的示例测试数据文件

7. 使用示例
7.1 生成个人债权融资测试数据
// 创建数据生成器
PersonalDataGenerator generator = new PersonalDataGenerator();

// 生成10条正向测试数据
List<PersonalLoanData> positiveData = generator.generatePositiveDataBatch(10);

// 生成反向测试数据
List<PersonalLoanData> negativeData = generator.generateAllNegativeData();

// 合并数据
List<PersonalLoanData> allData = new ArrayList<>();
allData.addAll(positiveData);
allData.addAll(negativeData);

// 校验数据
PersonalDataValidator validator = new PersonalDataValidator();
List<ValidationResult> results = validator.validateBatch(allData);

// 写入TXT文件
TxtFileProcessor<PersonalLoanData> txtProcessor = new TxtFileProcessor<>();
txtProcessor.writeToFile(allData, "121EXPORTTRADEINFO.txt", 
    data -> txtProcessor.convertToDelimitedString(
        data.getCertificateType(),
        data.getCertificateNumber(),
        // 其他字段...
    )
);

// 压缩文件
ZipFileProcessor zipProcessor = new ZipFileProcessor();
String zipFileName = generateFileName("12");
zipProcessor.zipFile("121EXPORTTRADEINFO.txt", zipFileName + ".zip");

// 加密文件
EncFileProcessor encProcessor = new EncFileProcessor();
encProcessor.encryptFile(zipFileName + ".zip", zipFileName + ".enc", 
    ConfigManager.getPubXKey(), ConfigManager.getPubYKey());


Copy
7.2 生成企业债权融资测试数据
// 创建数据生成器
EnterpriseDataGenerator generator = new EnterpriseDataGenerator();

// 生成10条正向测试数据
List<EnterpriseLoanData> positiveData = generator.generatePositiveDataBatch(10);

// 生成反向测试数据
List<EnterpriseLoanData> negativeData = generator.generateAllNegativeData();

// 合并数据
List<EnterpriseLoanData> allData = new ArrayList<>();
allData.addAll(positiveData);
allData.addAll(negativeData);

// 校验数据
EnterpriseDataValidator validator = new EnterpriseDataValidator();
List<ValidationResult> results = validator.validateBatch(allData);

// 写入TXT文件
TxtFileProcessor<EnterpriseLoanData> txtProcessor = new TxtFileProcessor<>();
txtProcessor.writeToFile(allData, "321EXPORTTRADEINFO.txt", 
    data -> txtProcessor.convertToDelimitedString(
        data.getEnterpriseName(),
        data.getEnterpriseCertType(),
        // 其他字段...
    )
);

// 压缩文件
ZipFileProcessor zipProcessor = new ZipFileProcessor();
String zipFileName = generateFileName("32");
zipProcessor.zipFile("321EXPORTTRADEINFO.txt", zipFileName + ".zip");

// 加密文件
EncFileProcessor encProcessor = new EncFileProcessor();
encProcessor.encryptFile(zipFileName + ".zip", zipFileName + ".enc", 
    ConfigManager.getPubXKey(), ConfigManager.getPubYKey());


Copy
java
8. 风险与缓解措施
风险	影响	缓解措施
数据规则变更	高	设计灵活的数据模型和校验规则，便于快速适应变更
SM2加密兼容性问题	中	充分测试不同版本的Bouncy Castle库，确保加密兼容性
性能问题	中	优化数据生成和文件处理逻辑，采用批处理方式
文件命名冲突	低	实现严格的文件命名规则，确保唯一性
9. 结论
本方案提供了一个完整的NIFA信用信息共享平台测试数据生成解决方案，包括个人债权融资类和企业债权融资类业务数据的生成、校验、压缩和加密流程。通过实现这一方案，可以有效支持NIFA信用信息共享平台API的自动化测试工作，提高测试效率和质量。