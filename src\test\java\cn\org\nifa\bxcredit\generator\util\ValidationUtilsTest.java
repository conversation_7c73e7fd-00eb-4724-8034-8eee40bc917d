package cn.org.nifa.bxcredit.generator.util;

import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * ValidationUtils 工具类单元测试
 * 
 * <AUTHOR> Team
 */
public class ValidationUtilsTest {
    
    /**
     * 测试统一社会信用代码校验
     */
    @Test
    public void testValidateUnifiedSocialCreditCode() {
        // 生成有效的统一社会信用代码进行测试
        String validCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();
        Assert.assertTrue(ValidationUtils.validateUnifiedSocialCreditCode(validCode), 
            "有效的统一社会信用代码应该通过校验");
        
        // 测试无效代码
        Assert.assertFalse(ValidationUtils.validateUnifiedSocialCreditCode(null), 
            "null代码应该校验失败");
        Assert.assertFalse(ValidationUtils.validateUnifiedSocialCreditCode(""), 
            "空代码应该校验失败");
        Assert.assertFalse(ValidationUtils.validateUnifiedSocialCreditCode("123456789012345678"), 
            "错误格式的代码应该校验失败");
        Assert.assertFalse(ValidationUtils.validateUnifiedSocialCreditCode("12345"), 
            "长度不正确的代码应该校验失败");
        
        System.out.println("统一社会信用代码校验测试通过");
    }
    
    /**
     * 测试组织机构代码校验
     */
    @Test
    public void testValidateOrganizationCode() {
        // 测试有效的组织机构代码格式
        Assert.assertTrue(ValidationUtils.validateOrganizationCode("12345678A"), 
            "有效格式的组织机构代码应该通过校验");
        Assert.assertTrue(ValidationUtils.validateOrganizationCode("ABCDEFGH1"), 
            "包含字母的组织机构代码应该通过校验");
        
        // 测试无效代码
        Assert.assertFalse(ValidationUtils.validateOrganizationCode(null), 
            "null代码应该校验失败");
        Assert.assertFalse(ValidationUtils.validateOrganizationCode(""), 
            "空代码应该校验失败");
        Assert.assertFalse(ValidationUtils.validateOrganizationCode("12345"), 
            "长度不正确的代码应该校验失败");
        Assert.assertFalse(ValidationUtils.validateOrganizationCode("123456789012345678"), 
            "长度过长的代码应该校验失败");
        Assert.assertFalse(ValidationUtils.validateOrganizationCode("12345678a"), 
            "包含小写字母的代码应该校验失败");
        
        System.out.println("组织机构代码校验测试通过");
    }
    
    /**
     * 测试扩展的validateOrgCode方法
     */
    @Test
    public void testValidateOrgCode() {
        // 测试18位统一社会信用代码
        String validUnifiedCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();
        Assert.assertTrue(ValidationUtils.validateOrgCode(validUnifiedCode), 
            "有效的统一社会信用代码应该通过校验");
        
        // 测试9位组织机构代码
        Assert.assertTrue(ValidationUtils.validateOrgCode("12345678A"), 
            "有效的组织机构代码应该通过校验");
        
        // 测试无效代码
        Assert.assertFalse(ValidationUtils.validateOrgCode(null), 
            "null代码应该校验失败");
        Assert.assertFalse(ValidationUtils.validateOrgCode(""), 
            "空代码应该校验失败");
        Assert.assertFalse(ValidationUtils.validateOrgCode("12345"), 
            "长度不正确的代码应该校验失败");
        
        System.out.println("扩展的组织机构代码校验测试通过");
    }
    
    /**
     * 测试从统一社会信用代码中提取组织机构代码
     */
    @Test
    public void testExtractOrganizationCodeFromUnified() {
        // 生成有效的统一社会信用代码
        String validUnifiedCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();
        String extractedOrgCode = ValidationUtils.extractOrganizationCodeFromUnified(validUnifiedCode);
        
        Assert.assertNotNull(extractedOrgCode, "提取的组织机构代码不应该为null");
        Assert.assertEquals(extractedOrgCode.length(), 9, "组织机构代码长度应为9位");
        Assert.assertEquals(extractedOrgCode, validUnifiedCode.substring(8, 17), 
            "提取的代码应该匹配原代码的第9-17位");
        
        // 测试无效输入
        Assert.assertNull(ValidationUtils.extractOrganizationCodeFromUnified(null), 
            "无效输入应该返回null");
        Assert.assertNull(ValidationUtils.extractOrganizationCodeFromUnified(""), 
            "空输入应该返回null");
        Assert.assertNull(ValidationUtils.extractOrganizationCodeFromUnified("123456789012345678"), 
            "无效格式应该返回null");
        
        System.out.println("统一社会信用代码: " + validUnifiedCode);
        System.out.println("提取的组织机构代码: " + extractedOrgCode);
    }
    
    /**
     * 测试代码类型识别
     */
    @Test
    public void testGetCodeType() {
        // 测试统一社会信用代码
        String validUnifiedCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();
        Assert.assertEquals(ValidationUtils.getCodeType(validUnifiedCode), "统一社会信用代码", 
            "应该正确识别统一社会信用代码");
        
        // 测试组织机构代码
        Assert.assertEquals(ValidationUtils.getCodeType("12345678A"), "组织机构代码", 
            "应该正确识别组织机构代码");
        
        // 测试无效代码
        Assert.assertEquals(ValidationUtils.getCodeType(null), "无效代码", 
            "null应该识别为无效代码");
        Assert.assertEquals(ValidationUtils.getCodeType(""), "无效代码", 
            "空字符串应该识别为无效代码");
        Assert.assertEquals(ValidationUtils.getCodeType("123456789012345678"), "无效的统一社会信用代码", 
            "无效的18位代码应该正确识别");
        Assert.assertEquals(ValidationUtils.getCodeType("123456789"), "组织机构代码",
            "有效格式的9位代码应该正确识别");
        Assert.assertEquals(ValidationUtils.getCodeType("12345"), "未知代码类型", 
            "其他长度的代码应该识别为未知类型");
        
        System.out.println("代码类型识别测试通过");
    }
    
    /**
     * 测试基础校验功能
     */
    @Test
    public void testBasicValidation() {
        // 测试长度校验
        Assert.assertTrue(ValidationUtils.validateLength("test", 1, 10), 
            "符合长度要求的字符串应该通过校验");
        Assert.assertFalse(ValidationUtils.validateLength("test", 5, 10), 
            "不符合最小长度的字符串应该校验失败");
        Assert.assertFalse(ValidationUtils.validateLength("test", 1, 3), 
            "超过最大长度的字符串应该校验失败");
        
        // 测试空值校验
        Assert.assertTrue(ValidationUtils.isEmpty(null), "null应该被识别为空");
        Assert.assertTrue(ValidationUtils.isEmpty(""), "空字符串应该被识别为空");
        Assert.assertTrue(ValidationUtils.isEmpty("   "), "空格字符串应该被识别为空");
        Assert.assertFalse(ValidationUtils.isEmpty("test"), "非空字符串不应该被识别为空");
        
        // 测试数字校验
        Assert.assertTrue(ValidationUtils.isNumeric("123"), "数字字符串应该通过校验");
        Assert.assertFalse(ValidationUtils.isNumeric("12a"), "包含字母的字符串应该校验失败");
        Assert.assertFalse(ValidationUtils.isNumeric(null), "null应该校验失败");
        
        System.out.println("基础校验功能测试通过");
    }
    
    /**
     * 测试枚举值校验
     */
    @Test
    public void testValidateEnum() {
        Assert.assertTrue(ValidationUtils.validateEnum("2", ValidationUtils.BUSINESS_TYPES),
            "有效的枚举值应该通过校验");
        Assert.assertFalse(ValidationUtils.validateEnum("invalid", ValidationUtils.BUSINESS_TYPES),
            "无效的枚举值应该校验失败");

        System.out.println("枚举值校验测试通过");
        System.out.println("业务类型枚举: " + ValidationUtils.BUSINESS_TYPES);
    }
    
    /**
     * 集成测试：测试统一社会信用代码生成和校验的完整流程
     */
    @Test
    public void testIntegrationUnifiedCodeGenerationAndValidation() {
        // 生成多个代码并验证
        for (int i = 0; i < 100; i++) {
            String code = UnifiedSocialCreditCodeGenerator.generateRandomCode();
            
            // 使用ValidationUtils进行校验
            Assert.assertTrue(ValidationUtils.validateUnifiedSocialCreditCode(code), 
                "生成的代码应该通过ValidationUtils校验");
            Assert.assertTrue(ValidationUtils.validateOrgCode(code), 
                "生成的代码应该通过组织机构代码校验");
            Assert.assertEquals(ValidationUtils.getCodeType(code), "统一社会信用代码", 
                "代码类型应该正确识别");
            
            // 提取组织机构代码并验证
            String orgCode = ValidationUtils.extractOrganizationCodeFromUnified(code);
            Assert.assertNotNull(orgCode, "应该能够提取组织机构代码");
            Assert.assertTrue(ValidationUtils.validateOrganizationCode(orgCode), 
                "提取的组织机构代码应该通过校验");
            Assert.assertEquals(ValidationUtils.getCodeType(orgCode), "组织机构代码", 
                "组织机构代码类型应该正确识别");
        }
        
        System.out.println("集成测试：统一社会信用代码生成和校验流程测试通过");
    }
    
    /**
     * 性能测试：大量代码的校验性能
     */
    @Test
    public void testValidationPerformance() {
        int testCount = 10000;

        // 预生成测试代码
        String[] testCodes = new String[testCount];
        for (int i = 0; i < testCount; i++) {
            testCodes[i] = UnifiedSocialCreditCodeGenerator.generateRandomCode();
        }

        // 测试校验性能
        long startTime = System.currentTimeMillis();
        for (String code : testCodes) {
            ValidationUtils.validateUnifiedSocialCreditCode(code);
        }
        long validationTime = System.currentTimeMillis() - startTime;

        // 测试类型识别性能
        startTime = System.currentTimeMillis();
        for (String code : testCodes) {
            ValidationUtils.getCodeType(code);
        }
        long typeRecognitionTime = System.currentTimeMillis() - startTime;

        System.out.println("校验性能测试结果:");
        System.out.println("校验 " + testCount + " 个代码耗时: " + validationTime + "ms");
        System.out.println("类型识别 " + testCount + " 个代码耗时: " + typeRecognitionTime + "ms");
        System.out.println("校验速度: " + (testCount * 1000L / Math.max(validationTime, 1)) + " 个/秒");
        System.out.println("类型识别速度: " + (testCount * 1000L / Math.max(typeRecognitionTime, 1)) + " 个/秒");
    }

    /**
     * 测试公司名称校验功能
     */
    @Test
    public void testValidateCompanyName() {
        // 生成有效的公司名称进行测试
        String validCompanyName = CompanyNameGenerator.generateRandomCompanyName();
        Assert.assertTrue(ValidationUtils.validateCompanyName(validCompanyName),
            "有效的公司名称应该通过校验");

        // 测试无效公司名称
        Assert.assertFalse(ValidationUtils.validateCompanyName(null),
            "null公司名称应该校验失败");
        Assert.assertFalse(ValidationUtils.validateCompanyName(""),
            "空公司名称应该校验失败");
        Assert.assertFalse(ValidationUtils.validateCompanyName("短"),
            "过短的公司名称应该校验失败");

        System.out.println("公司名称校验测试通过");
        System.out.println("测试的有效公司名称: " + validCompanyName);
    }

    /**
     * 测试公司名称长度校验
     */
    @Test
    public void testValidateCompanyNameLength() {
        Assert.assertTrue(ValidationUtils.validateCompanyNameLength("测试公司有限公司", 4, 20),
            "符合长度要求的公司名称应该通过校验");
        Assert.assertFalse(ValidationUtils.validateCompanyNameLength("短", 4, 20),
            "不符合最小长度的公司名称应该校验失败");
        Assert.assertFalse(ValidationUtils.validateCompanyNameLength("这是一个非常非常非常长的公司名称", 4, 10),
            "超过最大长度的公司名称应该校验失败");

        System.out.println("公司名称长度校验测试通过");
    }

    /**
     * 测试公司名称后缀校验
     */
    @Test
    public void testValidateCompanyNameSuffix() {
        Assert.assertTrue(ValidationUtils.validateCompanyNameSuffix("测试有限公司"),
            "包含有效后缀的公司名称应该通过校验");
        Assert.assertTrue(ValidationUtils.validateCompanyNameSuffix("创新科技集团"),
            "包含集团后缀的公司名称应该通过校验");
        Assert.assertFalse(ValidationUtils.validateCompanyNameSuffix("测试名称"),
            "不包含有效后缀的名称应该校验失败");

        System.out.println("公司名称后缀校验测试通过");
    }

    /**
     * 测试公司名称非法字符检查
     */
    @Test
    public void testContainsIllegalCharacters() {
        Assert.assertFalse(ValidationUtils.containsIllegalCharacters("正常公司名称有限公司"),
            "正常公司名称不应该包含非法字符");
        Assert.assertTrue(ValidationUtils.containsIllegalCharacters("测试@公司"),
            "包含@符号的名称应该被识别为包含非法字符");
        Assert.assertTrue(ValidationUtils.containsIllegalCharacters("测试#公司"),
            "包含#符号的名称应该被识别为包含非法字符");

        System.out.println("公司名称非法字符检查测试通过");
    }

    /**
     * 测试综合公司名称校验
     */
    @Test
    public void testValidateCompanyNameComprehensive() {
        // 测试有效公司名称
        String validName = CompanyNameGenerator.generateRandomCompanyName();
        String result = ValidationUtils.validateCompanyNameComprehensive(validName);
        Assert.assertEquals(result, "有效的公司名称", "有效公司名称应该返回正确结果");

        // 测试各种无效情况
        Assert.assertEquals(ValidationUtils.validateCompanyNameComprehensive(null),
            "公司名称不能为空", "null名称应该返回正确错误信息");
        Assert.assertEquals(ValidationUtils.validateCompanyNameComprehensive("短"),
            "公司名称长度应在4-50个字符之间", "过短名称应该返回正确错误信息");
        Assert.assertEquals(ValidationUtils.validateCompanyNameComprehensive("测试@公司有限公司"),
            "公司名称包含非法字符", "包含非法字符的名称应该返回正确错误信息");
        Assert.assertEquals(ValidationUtils.validateCompanyNameComprehensive("测试名称"),
            "公司名称缺少有效的公司类型后缀", "缺少后缀的名称应该返回正确错误信息");

        System.out.println("综合公司名称校验测试通过");
        System.out.println("测试的有效公司名称: " + validName + " -> " + result);
    }
}
