package cn.org.nifa.bxcredit.generator.demo;

import cn.org.nifa.bxcredit.generator.util.UnifiedSocialCreditCodeGenerator;
import cn.org.nifa.bxcredit.generator.util.ValidationUtils;

import java.io.IOException;
import java.util.Scanner;

/**
 * 统一社会信用代码生成器演示类
 * 
 * 演示如何使用 UnifiedSocialCreditCodeGenerator 进行各种类型的代码生成，
 * 包括小批量生成和大数据量生成（如5000万条）
 * 
 * <AUTHOR> Team
 */
public class UnifiedSocialCreditCodeDemo {
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("=== 统一社会信用代码生成器演示程序 ===");
        System.out.println();
        
        while (true) {
            showMenu();
            System.out.print("请选择功能 (输入数字): ");
            
            try {
                int choice = scanner.nextInt();
                scanner.nextLine(); // 消费换行符
                
                switch (choice) {
                    case 1:
                        demonstrateBasicGeneration();
                        break;
                    case 2:
                        demonstrateValidation();
                        break;
                    case 3:
                        demonstrateOrganizationCodeExtraction();
                        break;
                    case 4:
                        demonstrateCustomGeneration(scanner);
                        break;
                    case 5:
                        demonstrateBatchGeneration(scanner);
                        break;
                    case 6:
                        demonstrateMultiThreadedGeneration(scanner);
                        break;
                    case 7:
                        demonstratePerformanceTest();
                        break;
                    case 8:
                        demonstrateLargeScaleGeneration(scanner);
                        break;
                    case 9:
                        demonstrateIntegrationWithValidationUtils();
                        break;
                    case 0:
                        System.out.println("感谢使用统一社会信用代码生成器演示程序！");
                        return;
                    default:
                        System.out.println("无效选择，请重新输入。");
                }
                
                System.out.println();
                System.out.println("按回车键继续...");
                scanner.nextLine();
                System.out.println();
                
            } catch (Exception e) {
                System.err.println("发生错误: " + e.getMessage());
                scanner.nextLine(); // 清除无效输入
            }
        }
    }
    
    /**
     * 显示菜单
     */
    private static void showMenu() {
        System.out.println("功能菜单:");
        System.out.println("1. 基础代码生成演示");
        System.out.println("2. 代码校验演示");
        System.out.println("3. 组织机构代码提取演示");
        System.out.println("4. 自定义前缀代码生成");
        System.out.println("5. 批量生成到文件");
        System.out.println("6. 多线程批量生成到文件");
        System.out.println("7. 性能测试");
        System.out.println("8. 大规模数据生成（5000万条）");
        System.out.println("9. ValidationUtils集成演示");
        System.out.println("0. 退出程序");
        System.out.println();
    }
    
    /**
     * 演示基础代码生成功能
     */
    private static void demonstrateBasicGeneration() {
        System.out.println("=== 基础代码生成演示 ===");
        
        System.out.println("随机生成的统一社会信用代码示例:");
        for (int i = 0; i < 10; i++) {
            String code = UnifiedSocialCreditCodeGenerator.generateRandomCode();
            String orgCode = UnifiedSocialCreditCodeGenerator.extractOrganizationCode(code);
            System.out.printf("%d. %s (组织机构代码: %s)\n", i + 1, code, orgCode);
        }
        
        System.out.println("\n线程安全生成示例:");
        for (int i = 0; i < 5; i++) {
            String code = UnifiedSocialCreditCodeGenerator.generateRandomCodeThreadSafe();
            System.out.printf("%d. %s\n", i + 1, code);
        }
    }
    
    /**
     * 演示代码校验功能
     */
    private static void demonstrateValidation() {
        System.out.println("=== 代码校验演示 ===");
        
        // 生成有效代码并校验
        String validCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();
        System.out.println("有效代码: " + validCode);
        System.out.println("校验结果: " + UnifiedSocialCreditCodeGenerator.validateCode(validCode));
        
        // 测试无效代码
        String[] invalidCodes = {
            "123456789012345678", // 错误格式
            "91110000MA01A1B2CX", // 错误校验码
            "12345",               // 长度错误
            null                   // null值
        };
        
        System.out.println("\n无效代码校验:");
        for (String code : invalidCodes) {
            System.out.printf("代码: %s -> 校验结果: %s\n", 
                code, UnifiedSocialCreditCodeGenerator.validateCode(code));
        }
    }
    
    /**
     * 演示组织机构代码提取功能
     */
    private static void demonstrateOrganizationCodeExtraction() {
        System.out.println("=== 组织机构代码提取演示 ===");
        
        for (int i = 0; i < 5; i++) {
            String unifiedCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();
            String orgCode = UnifiedSocialCreditCodeGenerator.extractOrganizationCode(unifiedCode);
            
            System.out.printf("统一社会信用代码: %s\n", unifiedCode);
            System.out.printf("组织机构代码: %s (第9-17位)\n", orgCode);
            System.out.println("---");
        }
    }
    
    /**
     * 演示自定义前缀代码生成
     */
    private static void demonstrateCustomGeneration(Scanner scanner) {
        System.out.println("=== 自定义前缀代码生成演示 ===");
        
        System.out.println("登记管理部门代码选项: " + UnifiedSocialCreditCodeGenerator.getRegistrationDeptCodes());
        System.out.print("请输入登记管理部门代码 (1位): ");
        String deptCode = scanner.nextLine();
        
        System.out.println("机构类别代码选项: " + UnifiedSocialCreditCodeGenerator.getOrganizationTypeCodes());
        System.out.print("请输入机构类别代码 (1位): ");
        String typeCode = scanner.nextLine();
        
        System.out.print("请输入行政区划代码 (6位，如110000): ");
        String adminCode = scanner.nextLine();
        
        try {
            System.out.println("\n生成的自定义前缀代码:");
            for (int i = 0; i < 5; i++) {
                String code = UnifiedSocialCreditCodeGenerator.generateCodeWithPrefix(deptCode, typeCode, adminCode);
                System.out.printf("%d. %s\n", i + 1, code);
            }
        } catch (Exception e) {
            System.err.println("生成失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示批量生成到文件
     */
    private static void demonstrateBatchGeneration(Scanner scanner) throws IOException {
        System.out.println("=== 批量生成到文件演示 ===");
        
        System.out.print("请输入生成数量: ");
        long count = scanner.nextLong();
        scanner.nextLine();
        
        System.out.print("是否包含组织机构代码 (y/n): ");
        boolean includeOrgCode = scanner.nextLine().toLowerCase().startsWith("y");
        
        System.out.print("请输入输出文件路径 (例: uscc_codes.csv): ");
        String filePath = scanner.nextLine();
        
        System.out.println("开始生成...");
        long startTime = System.currentTimeMillis();
        
        UnifiedSocialCreditCodeGenerator.BatchGenerationResult result = 
            UnifiedSocialCreditCodeGenerator.generateCodesToFile(count, filePath, includeOrgCode);
        
        long endTime = System.currentTimeMillis();
        System.out.println("生成完成，总耗时: " + (endTime - startTime) + "ms");
        System.out.println(result);
    }
    
    /**
     * 演示多线程批量生成到文件
     */
    private static void demonstrateMultiThreadedGeneration(Scanner scanner) throws IOException, InterruptedException {
        System.out.println("=== 多线程批量生成到文件演示 ===");
        
        System.out.print("请输入生成数量: ");
        long count = scanner.nextLong();
        scanner.nextLine();
        
        System.out.print("是否包含组织机构代码 (y/n): ");
        boolean includeOrgCode = scanner.nextLine().toLowerCase().startsWith("y");
        
        System.out.print("请输入线程数量 (建议: " + Runtime.getRuntime().availableProcessors() + "): ");
        int threadCount = scanner.nextInt();
        scanner.nextLine();
        
        System.out.print("请输入输出文件路径 (例: uscc_codes_mt.csv): ");
        String filePath = scanner.nextLine();
        
        System.out.println("开始多线程生成...");
        long startTime = System.currentTimeMillis();
        
        UnifiedSocialCreditCodeGenerator.BatchGenerationResult result = 
            UnifiedSocialCreditCodeGenerator.generateCodesToFileMultiThreaded(
                count, filePath, includeOrgCode, threadCount);
        
        long endTime = System.currentTimeMillis();
        System.out.println("多线程生成完成，总耗时: " + (endTime - startTime) + "ms");
        System.out.println(result);
    }
    
    /**
     * 演示性能测试
     */
    private static void demonstratePerformanceTest() {
        System.out.println("=== 性能测试演示 ===");
        
        int[] testCounts = {1000, 10000, 100000};
        
        for (int testCount : testCounts) {
            System.out.println("\n测试数量: " + testCount);
            
            // 单线程性能测试
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < testCount; i++) {
                UnifiedSocialCreditCodeGenerator.generateRandomCode();
            }
            long singleThreadTime = System.currentTimeMillis() - startTime;
            
            // 线程安全方法性能测试
            startTime = System.currentTimeMillis();
            for (int i = 0; i < testCount; i++) {
                UnifiedSocialCreditCodeGenerator.generateRandomCodeThreadSafe();
            }
            long threadSafeTime = System.currentTimeMillis() - startTime;
            
            // 校验性能测试
            String testCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();
            startTime = System.currentTimeMillis();
            for (int i = 0; i < testCount; i++) {
                UnifiedSocialCreditCodeGenerator.validateCode(testCode);
            }
            long validationTime = System.currentTimeMillis() - startTime;
            
            System.out.println("单线程生成耗时: " + singleThreadTime + "ms, 速度: " + 
                             (testCount * 1000L / Math.max(singleThreadTime, 1)) + " 个/秒");
            System.out.println("线程安全生成耗时: " + threadSafeTime + "ms, 速度: " + 
                             (testCount * 1000L / Math.max(threadSafeTime, 1)) + " 个/秒");
            System.out.println("校验耗时: " + validationTime + "ms, 速度: " + 
                             (testCount * 1000L / Math.max(validationTime, 1)) + " 个/秒");
        }
    }
    
    /**
     * 演示大规模数据生成（5000万条）
     */
    private static void demonstrateLargeScaleGeneration(Scanner scanner) throws IOException, InterruptedException {
        System.out.println("=== 大规模数据生成演示（5000万条） ===");
        System.out.println("警告: 这将生成5000万条统一社会信用代码，可能需要较长时间和大量磁盘空间！");
        System.out.print("确认继续？(y/N): ");
        
        String confirm = scanner.nextLine();
        if (!confirm.equalsIgnoreCase("y") && !confirm.equalsIgnoreCase("yes")) {
            System.out.println("已取消大规模数据生成");
            return;
        }
        
        long count = 50_000_000L; // 5000万
        int threadCount = Runtime.getRuntime().availableProcessors();
        String filePath = "large_scale_uscc_codes.csv";
        
        System.out.println("开始生成5000万条统一社会信用代码...");
        System.out.println("使用线程数: " + threadCount);
        System.out.println("输出文件: " + filePath);
        
        long startTime = System.currentTimeMillis();
        
        UnifiedSocialCreditCodeGenerator.BatchGenerationResult result = 
            UnifiedSocialCreditCodeGenerator.generateCodesToFileMultiThreaded(
                count, filePath, true, threadCount);
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        System.out.println("\n=== 大规模生成完成 ===");
        System.out.println("总耗时: " + totalTime + "ms (" + (totalTime / 1000.0) + "秒)");
        System.out.println(result);
        System.out.println("文件大小约: " + (count * 30 / 1024 / 1024) + "MB"); // 每行约30字节
    }
    
    /**
     * 演示与ValidationUtils的集成
     */
    private static void demonstrateIntegrationWithValidationUtils() {
        System.out.println("=== ValidationUtils集成演示 ===");
        
        // 生成代码并使用ValidationUtils校验
        for (int i = 0; i < 5; i++) {
            String code = UnifiedSocialCreditCodeGenerator.generateRandomCode();
            
            System.out.printf("代码 %d: %s\n", i + 1, code);
            System.out.printf("  代码类型: %s\n", ValidationUtils.getCodeType(code));
            System.out.printf("  校验结果: %s\n", ValidationUtils.validateUnifiedSocialCreditCode(code));
            System.out.printf("  组织机构代码校验: %s\n", ValidationUtils.validateOrgCode(code));
            
            // 提取组织机构代码
            String orgCode = ValidationUtils.extractOrganizationCodeFromUnified(code);
            if (orgCode != null) {
                System.out.printf("  组织机构代码: %s\n", orgCode);
                System.out.printf("  组织机构代码类型: %s\n", ValidationUtils.getCodeType(orgCode));
            }
            System.out.println();
        }
    }
}
