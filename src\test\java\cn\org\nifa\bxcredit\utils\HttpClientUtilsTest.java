package cn.org.nifa.bxcredit.utils;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;

import java.util.HashMap;
import java.util.Map;

import org.testng.annotations.Test;

import cn.org.nifa.bxcredit.exception.NifaApiException;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;

/**
 * HttpClientUtils测试类
 */
public class HttpClientUtilsTest {
    
    @Test
    public void testCreateCustomClient() {
        assertNotNull(HttpClientUtils.createCustomClient(10, 20, 30));
    }
    
    @Test
    public void testGetClient() {
        assertNotNull(HttpClientUtils.getClient());
    }
    
    @Test
    public void testPostWithMap() throws Exception {
        try (MockWebServer server = new MockWebServer()) {
            // 设置模拟响应
            server.enqueue(new MockResponse()
                    .setResponseCode(200)
                    .setBody("{\"result\":\"success\"}"));
            
            // 启动服务器
            server.start();
            
            // 准备请求数据
            String url = server.url("/test").toString();
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            
            Map<String, String> body = new HashMap<>();
            body.put("key", "value");
            
            // 发送请求
            String response = HttpClientUtils.post(url, headers, body);
            
            // 验证响应
            assertEquals("{\"result\":\"success\"}", response);
        }
    }
    
    @Test(expectedExceptions = NifaApiException.class)
    public void testPostWithError() throws Exception {
        try (MockWebServer server = new MockWebServer()) {
            // 设置模拟响应
            server.enqueue(new MockResponse()
                    .setResponseCode(500)
                    .setBody("{\"error\":\"server error\"}"));
            
            // 启动服务器
            server.start();
            
            // 准备请求数据
            String url = server.url("/test").toString();
            Map<String, String> headers = new HashMap<>();
            
            // 发送请求 - 应该抛出异常
            HttpClientUtils.post(url, headers, "{}");
        }
    }
    
    @Test
    public void testGet() throws Exception {
        try (MockWebServer server = new MockWebServer()) {
            // 设置模拟响应
            server.enqueue(new MockResponse()
                    .setResponseCode(200)
                    .setBody("{\"result\":\"success\"}"));
            
            // 启动服务器
            server.start();
            
            // 准备请求数据
            String url = server.url("/test").toString();
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer token");
            
            // 发送请求
            String response = HttpClientUtils.get(url, headers);
            
            // 验证响应
            assertEquals("{\"result\":\"success\"}", response);
        }
    }
}