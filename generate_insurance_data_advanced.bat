@echo off
REM 信用保证保险测试数据生成高级脚本
REM 支持更多配置选项和环境切换

REM 设置Java环境
set JAVA_HOME=%JAVA_HOME%
set PATH=%JAVA_HOME%\bin;%PATH%

REM 默认配置
set ENV=dev
set ORG_CODE=123456789
set OUTPUT_DIR=
set UNDERWRITING_COUNT=10
set COMPENSATION_COUNT=10
set RECOVERY_COUNT=10
set INCLUDE_NEGATIVE=false

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :execute

if /i "%~1"=="-e" (
    set ENV=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--env" (
    set ENV=%~2
    shift
    shift
    goto :parse_args
)

if /i "%~1"=="-o" (
    set ORG_CODE=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--org-code" (
    set ORG_CODE=%~2
    shift
    shift
    goto :parse_args
)

if /i "%~1"=="-d" (
    set OUTPUT_DIR=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--output-dir" (
    set OUTPUT_DIR=%~2
    shift
    shift
    goto :parse_args
)

if /i "%~1"=="-u" (
    set UNDERWRITING_COUNT=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--underwriting" (
    set UNDERWRITING_COUNT=%~2
    shift
    shift
    goto :parse_args
)

if /i "%~1"=="-c" (
    set COMPENSATION_COUNT=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--compensation" (
    set COMPENSATION_COUNT=%~2
    shift
    shift
    goto :parse_args
)

if /i "%~1"=="-r" (
    set RECOVERY_COUNT=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--recovery" (
    set RECOVERY_COUNT=%~2
    shift
    shift
    goto :parse_args
)

if /i "%~1"=="-n" (
    set INCLUDE_NEGATIVE=true
    shift
    goto :parse_args
)
if /i "%~1"=="--negative" (
    set INCLUDE_NEGATIVE=true
    shift
    goto :parse_args
)

if /i "%~1"=="-h" (
    goto :show_help
)
if /i "%~1"=="--help" (
    goto :show_help
)

echo 未知选项: %~1
goto :show_help

:show_help
echo 信用保证保险测试数据生成工具
echo 用法: %0 [选项]
echo 选项:
echo   -e, --env ENV             设置环境 (dev/test/prod)
echo   -o, --org-code CODE       设置机构代码
echo   -d, --output-dir DIR      设置输出目录
echo   -u, --underwriting COUNT  设置承保信息数量
echo   -c, --compensation COUNT  设置代偿信息数量
echo   -r, --recovery COUNT      设置追偿信息数量
echo   -n, --negative            包含反向测试数据
echo   -h, --help                显示帮助信息
echo 示例:
echo   %0 -e test -u 20 -c 15 -r 10 -n
exit /b 1

:execute
REM 设置环境变量
set NIFA_ENV=%ENV%
set NIFA_ORG_CODE=%ORG_CODE%

REM 构建命令行参数
set ARGS=-u %UNDERWRITING_COUNT% -c %COMPENSATION_COUNT% -r %RECOVERY_COUNT%

if "%INCLUDE_NEGATIVE%"=="true" (
    set ARGS=%ARGS% -n
)

if not "%OUTPUT_DIR%"=="" (
    set ARGS=%ARGS% -o "%OUTPUT_DIR%"
)

REM 显示配置信息
echo === 信用保证保险测试数据生成配置 ===
echo 环境: %ENV%
echo 机构代码: %ORG_CODE%
echo 承保信息数量: %UNDERWRITING_COUNT%
echo 代偿信息数量: %COMPENSATION_COUNT%
echo 追偿信息数量: %RECOVERY_COUNT%
echo 包含反向测试数据: %INCLUDE_NEGATIVE%
if not "%OUTPUT_DIR%"=="" (
    echo 输出目录: %OUTPUT_DIR%
) else (
    echo 输出目录: 默认
)
echo =======================================

REM 执行数据生成命令
echo 开始生成信用保证保险测试数据...
java -cp target\bxcredit-1.0.0.jar cn.org.nifa.bxcredit.generator.CreditGuaranteeInsuranceDataGeneratorCLI %ARGS%

if %ERRORLEVEL% NEQ 0 (
    echo 数据生成失败，错误代码: %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)

echo 数据生成完成