2025-07-15 18:34:29.859 [main] ERROR c.o.n.bxcredit.utils.HttpClientUtils - IO error during API request: Failed to connect to /192.168.2.99:80
java.net.ConnectException: Failed to connect to /192.168.2.99:80
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297)
	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
	at cn.org.nifa.bxcredit.utils.HttpClientUtils.post(HttpClientUtils.java:64)
	at cn.org.nifa.bxcredit.utils.HttpClientUtils.post(HttpClientUtils.java:92)
	at cn.org.nifa.bxcredit.HighcourtDemo.main(HighcourtDemo.java:63)
Caused by: java.net.ConnectException: Connection timed out: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(Unknown Source)
	at java.base/sun.nio.ch.NioSocketImpl.connect(Unknown Source)
	at java.base/java.net.SocksSocketImpl.connect(Unknown Source)
	at java.base/java.net.Socket.connect(Unknown Source)
	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
	... 18 common frames omitted
2025-07-15 18:43:04.704 [main] INFO  cn.org.nifa.bxcredit.DataDemo - === Data Submission Demo ===
2025-07-15 18:43:04.708 [main] INFO  cn.org.nifa.bxcredit.DataDemo - Environment: null
2025-07-15 18:43:05.278 [main] ERROR cn.org.nifa.bxcredit.DataDemo - Demo execution failed: File encryption failed
java.lang.RuntimeException: File encryption failed
	at cn.org.nifa.bxcredit.crypto.ModernSM2Utils.encryptFile(ModernSM2Utils.java:113)
	at cn.org.nifa.bxcredit.crypto.SM2CryptoService.encryptFile(SM2CryptoService.java:105)
	at cn.org.nifa.bxcredit.DataDemo.main(DataDemo.java:56)
Caused by: java.nio.file.NoSuchFileException: tmp\2RR3PEGT2202309061715127112.zip
	at java.base/sun.nio.fs.WindowsException.translateToIOException(Unknown Source)
	at java.base/sun.nio.fs.WindowsException.rethrowAsIOException(Unknown Source)
	at java.base/sun.nio.fs.WindowsException.rethrowAsIOException(Unknown Source)
	at java.base/sun.nio.fs.WindowsFileSystemProvider.newByteChannel(Unknown Source)
	at java.base/java.nio.file.Files.newByteChannel(Unknown Source)
	at java.base/java.nio.file.Files.newByteChannel(Unknown Source)
	at java.base/java.nio.file.Files.readAllBytes(Unknown Source)
	at cn.org.nifa.bxcredit.crypto.ModernSM2Utils.encryptFile(ModernSM2Utils.java:106)
	... 2 common frames omitted
