package cn.org.nifa.bxcredit.generator.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 企业债权融资数据模型
 * 
 * <AUTHOR> Team
 */
public class EnterpriseLoanData {
    
    private String enterpriseName;        // 企业名称
    private String enterpriseCertType;    // 企业证件类型
    private String enterpriseCode;        // 企业代码
    private String legalPersonName;       // 企业法人姓名
    private String legalPersonCertType;   // 企业法人证件类型
    private String legalPersonCertNumber; // 企业法人证件号码
    private String businessInstitution;   // 业务发生机构
    private String businessNumber;        // 业务号
    private String businessType;          // 业务类型
    private String businessCategory;      // 业务种类
    private String openDate;              // 开户日期
    private String dueDate;               // 到期日期
    private String creditLimit;           // 授信额度
    private String businessDate;          // 业务发生日期
    private String repaymentStatus;       // 本月还款状态
    private String balance;               // 余额
    private String overdueAmount;         // 当前逾期总额
    private String compensationInstitution; // 当前代偿机构
    private String compensationAmount;    // 当前代偿总额
    
    // 构造函数
    public EnterpriseLoanData() {}
    
    // Builder模式构造函数
    private EnterpriseLoanData(Builder builder) {
        this.enterpriseName = builder.enterpriseName;
        this.enterpriseCertType = builder.enterpriseCertType;
        this.enterpriseCode = builder.enterpriseCode;
        this.legalPersonName = builder.legalPersonName;
        this.legalPersonCertType = builder.legalPersonCertType;
        this.legalPersonCertNumber = builder.legalPersonCertNumber;
        this.businessInstitution = builder.businessInstitution;
        this.businessNumber = builder.businessNumber;
        this.businessType = builder.businessType;
        this.businessCategory = builder.businessCategory;
        this.openDate = builder.openDate;
        this.dueDate = builder.dueDate;
        this.creditLimit = builder.creditLimit;
        this.businessDate = builder.businessDate;
        this.repaymentStatus = builder.repaymentStatus;
        this.balance = builder.balance;
        this.overdueAmount = builder.overdueAmount;
        this.compensationInstitution = builder.compensationInstitution;
        this.compensationAmount = builder.compensationAmount;
    }
    
    // Getter和Setter方法
    public String getEnterpriseName() { return enterpriseName; }
    public void setEnterpriseName(String enterpriseName) { this.enterpriseName = enterpriseName; }
    
    public String getEnterpriseCertType() { return enterpriseCertType; }
    public void setEnterpriseCertType(String enterpriseCertType) { this.enterpriseCertType = enterpriseCertType; }
    
    public String getEnterpriseCode() { return enterpriseCode; }
    public void setEnterpriseCode(String enterpriseCode) { this.enterpriseCode = enterpriseCode; }
    
    public String getLegalPersonName() { return legalPersonName; }
    public void setLegalPersonName(String legalPersonName) { this.legalPersonName = legalPersonName; }
    
    public String getLegalPersonCertType() { return legalPersonCertType; }
    public void setLegalPersonCertType(String legalPersonCertType) { this.legalPersonCertType = legalPersonCertType; }
    
    public String getLegalPersonCertNumber() { return legalPersonCertNumber; }
    public void setLegalPersonCertNumber(String legalPersonCertNumber) { this.legalPersonCertNumber = legalPersonCertNumber; }
    
    public String getBusinessInstitution() { return businessInstitution; }
    public void setBusinessInstitution(String businessInstitution) { this.businessInstitution = businessInstitution; }
    
    public String getBusinessNumber() { return businessNumber; }
    public void setBusinessNumber(String businessNumber) { this.businessNumber = businessNumber; }
    
    public String getBusinessType() { return businessType; }
    public void setBusinessType(String businessType) { this.businessType = businessType; }
    
    public String getBusinessCategory() { return businessCategory; }
    public void setBusinessCategory(String businessCategory) { this.businessCategory = businessCategory; }
    
    public String getOpenDate() { return openDate; }
    public void setOpenDate(String openDate) { this.openDate = openDate; }
    
    public String getDueDate() { return dueDate; }
    public void setDueDate(String dueDate) { this.dueDate = dueDate; }
    
    public String getCreditLimit() { return creditLimit; }
    public void setCreditLimit(String creditLimit) { this.creditLimit = creditLimit; }
    
    public String getBusinessDate() { return businessDate; }
    public void setBusinessDate(String businessDate) { this.businessDate = businessDate; }
    
    public String getRepaymentStatus() { return repaymentStatus; }
    public void setRepaymentStatus(String repaymentStatus) { this.repaymentStatus = repaymentStatus; }
    
    public String getBalance() { return balance; }
    public void setBalance(String balance) { this.balance = balance; }
    
    public String getOverdueAmount() { return overdueAmount; }
    public void setOverdueAmount(String overdueAmount) { this.overdueAmount = overdueAmount; }
    
    public String getCompensationInstitution() { return compensationInstitution; }
    public void setCompensationInstitution(String compensationInstitution) { this.compensationInstitution = compensationInstitution; }
    
    public String getCompensationAmount() { return compensationAmount; }
    public void setCompensationAmount(String compensationAmount) { this.compensationAmount = compensationAmount; }
    
    /**
     * 校验数据
     * 
     * @return 校验错误列表
     */
    public List<String> validate() {
        List<String> errors = new ArrayList<>();
        
        // 基础字段校验
        if (enterpriseName == null || enterpriseName.trim().isEmpty()) {
            errors.add("企业名称不能为空");
        }
        if (enterpriseName != null && enterpriseName.length() > 60) {
            errors.add("企业名称长度不能超过60");
        }
        if (enterpriseCertType == null || enterpriseCertType.trim().isEmpty()) {
            errors.add("企业证件类型不能为空");
        }
        if (businessNumber == null || businessNumber.trim().isEmpty()) {
            errors.add("业务号不能为空");
        }
        
        return errors;
    }
    
    /**
     * Builder类
     */
    public static class Builder {
        private String enterpriseName;
        private String enterpriseCertType;
        private String enterpriseCode;
        private String legalPersonName;
        private String legalPersonCertType;
        private String legalPersonCertNumber;
        private String businessInstitution;
        private String businessNumber;
        private String businessType;
        private String businessCategory;
        private String openDate;
        private String dueDate;
        private String creditLimit;
        private String businessDate;
        private String repaymentStatus;
        private String balance;
        private String overdueAmount;
        private String compensationInstitution;
        private String compensationAmount;
        
        public Builder enterpriseName(String enterpriseName) {
            this.enterpriseName = enterpriseName;
            return this;
        }
        
        public Builder enterpriseCertType(String enterpriseCertType) {
            this.enterpriseCertType = enterpriseCertType;
            return this;
        }
        
        public Builder enterpriseCode(String enterpriseCode) {
            this.enterpriseCode = enterpriseCode;
            return this;
        }
        
        public Builder legalPersonName(String legalPersonName) {
            this.legalPersonName = legalPersonName;
            return this;
        }
        
        public Builder legalPersonCertType(String legalPersonCertType) {
            this.legalPersonCertType = legalPersonCertType;
            return this;
        }
        
        public Builder legalPersonCertNumber(String legalPersonCertNumber) {
            this.legalPersonCertNumber = legalPersonCertNumber;
            return this;
        }
        
        public Builder businessInstitution(String businessInstitution) {
            this.businessInstitution = businessInstitution;
            return this;
        }
        
        public Builder businessNumber(String businessNumber) {
            this.businessNumber = businessNumber;
            return this;
        }
        
        public Builder businessType(String businessType) {
            this.businessType = businessType;
            return this;
        }
        
        public Builder businessCategory(String businessCategory) {
            this.businessCategory = businessCategory;
            return this;
        }
        
        public Builder openDate(String openDate) {
            this.openDate = openDate;
            return this;
        }
        
        public Builder dueDate(String dueDate) {
            this.dueDate = dueDate;
            return this;
        }
        
        public Builder creditLimit(String creditLimit) {
            this.creditLimit = creditLimit;
            return this;
        }
        
        public Builder businessDate(String businessDate) {
            this.businessDate = businessDate;
            return this;
        }
        
        public Builder repaymentStatus(String repaymentStatus) {
            this.repaymentStatus = repaymentStatus;
            return this;
        }
        
        public Builder balance(String balance) {
            this.balance = balance;
            return this;
        }
        
        public Builder overdueAmount(String overdueAmount) {
            this.overdueAmount = overdueAmount;
            return this;
        }
        
        public Builder compensationInstitution(String compensationInstitution) {
            this.compensationInstitution = compensationInstitution;
            return this;
        }
        
        public Builder compensationAmount(String compensationAmount) {
            this.compensationAmount = compensationAmount;
            return this;
        }
        
        public EnterpriseLoanData build() {
            return new EnterpriseLoanData(this);
        }
    }
    
    public static Builder builder() {
        return new Builder();
    }
}