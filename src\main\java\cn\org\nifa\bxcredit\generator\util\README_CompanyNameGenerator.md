# 公司名称生成工具类使用说明

## 概述

`CompanyNameGenerator` 是一个用于生成公司名称的工具类，支持多种名称模板和大规模（高达5000万）唯一公司名称生成。该工具类采用"基于组件的组合生成与实时唯一性校验"的核心设计理念，通过组合不同的词库元素生成多样化的公司名称。

## 主要功能

1. **单个公司名称生成**：支持多种名称模板
2. **批量公司名称生成**：支持单线程和多线程生成
3. **大规模唯一名称生成**：优化的内存管理和性能
4. **公司名称校验**：验证生成的名称是否符合规范
5. **与其他工具类集成**：可与统一社会信用代码生成器等工具类协同工作

## 名称模板

工具类支持以下几种名称模板：

1. **前缀+核心词+后缀**：如"未来科技有限公司"
2. **核心词+核心词+后缀**：如"创新发展集团"
3. **核心词+数字/字母+后缀**：如"数字时代A888科技"
4. **地名+核心词+后缀**：如"上海创新信息咨询"
5. **随机模板**：随机选择上述模板之一

## 使用方法

### 1. 单个公司名称生成

```java
// 生成随机模板的公司名称
String randomName = CompanyNameGenerator.generateRandomCompanyName();

// 使用指定模板生成公司名称
String prefixCoreSuffixName = CompanyNameGenerator.generateCompanyName(
    CompanyNameGenerator.NameTemplate.PREFIX_CORE_SUFFIX);
String coreCoreSuffixName = CompanyNameGenerator.generateCompanyName(
    CompanyNameGenerator.NameTemplate.CORE_CORE_SUFFIX);
String coreDigitSuffixName = CompanyNameGenerator.generateCompanyName(
    CompanyNameGenerator.NameTemplate.CORE_DIGIT_SUFFIX);
String locationCoreSuffixName = CompanyNameGenerator.generateCompanyName(
    CompanyNameGenerator.NameTemplate.LOCATION_CORE_SUFFIX);
```

### 2. 批量公司名称生成

```java
// 单线程批量生成
CompanyNameGenerator.BatchGenerationResult result = 
    CompanyNameGenerator.generateNamesToFile(
        10000,                                         // 生成数量
        "company_names.csv",                           // 输出文件路径
        CompanyNameGenerator.NameTemplate.RANDOM);     // 名称模板

// 多线程批量生成
CompanyNameGenerator.BatchGenerationResult mtResult = 
    CompanyNameGenerator.generateNamesToFileMultiThreaded(
        1000000,                                       // 生成数量
        "company_names_mt.csv",                        // 输出文件路径
        CompanyNameGenerator.NameTemplate.RANDOM,      // 名称模板
        8,                                             // 线程数量
        10000);                                        // 批处理大小
```

### 3. 大规模生成（5000万）

```java
// 生成5000万条公司名称
CompanyNameGenerator.BatchGenerationResult largeResult = 
    CompanyNameGenerator.generate50Million(
        CompanyNameGenerator.NameTemplate.CORE_DIGIT_SUFFIX,  // 名称模板
        "fifty_million_companies.csv");                       // 输出文件路径
```

### 4. 公司名称校验

```java
String companyName = "未来科技有限公司";
boolean isValid = CompanyNameGenerator.validateCompanyName(companyName);
```

### 5. 与统一社会信用代码生成器集成

```java
// 生成公司名称和统一社会信用代码
String companyName = CompanyNameGenerator.generateRandomCompanyName();
String creditCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();

System.out.println("公司名称: " + companyName);
System.out.println("统一社会信用代码: " + creditCode);
```

### 6. 估算生成时间和文件大小

```java
// 估算生成时间（秒）
double estimatedTime = CompanyNameGenerator.estimateGenerationTime(
    50_000_000L,                                      // 生成数量
    CompanyNameGenerator.NameTemplate.RANDOM);        // 名称模板

// 估算文件大小（字节）
long estimatedSize = CompanyNameGenerator.estimateFileSize(
    50_000_000L,                                      // 生成数量
    CompanyNameGenerator.NameTemplate.RANDOM);        // 名称模板
```

## 性能优化

工具类采用了多种性能优化策略：

1. **分批处理**：将大量数据分批处理，避免内存溢出
2. **多线程并发**：利用多核CPU提高生成效率
3. **批量I/O**：减少磁盘I/O操作，提高写入效率
4. **线程安全设计**：使用ThreadLocalRandom等线程安全组件
5. **唯一性校验优化**：使用ConcurrentHashMap等高效数据结构

## 扩展词库

工具类默认包含基础词库，但可以通过添加以下资源文件来扩展词库：

- `/data/company_prefix_words.txt` - 前缀词库
- `/data/company_core_words.txt` - 核心词库
- `/data/company_suffix_words.txt` - 后缀词库
- `/data/company_location_words.txt` - 地名词库
- `/data/company_industry_words.txt` - 行业词库

文件格式为每行一个词汇，使用UTF-8编码。

## 示例程序

项目包含一个演示程序 `CompanyNameGeneratorDemo`，展示了工具类的各种功能：

```java
public class CompanyNameGeneratorDemo {
    public static void main(String[] args) {
        // 详见演示程序代码
    }
}
```

## 注意事项

1. 生成大量数据时，请确保有足够的内存和磁盘空间
2. 多线程生成时，建议线程数不超过CPU核心数
3. 5000万级别的生成任务可能需要较长时间，请耐心等待
4. 生成的文件可能较大，请确保文件系统支持大文件

## 单元测试

工具类附带完整的单元测试 `CompanyNameGeneratorTest`，覆盖了所有主要功能：

```java
public class CompanyNameGeneratorTest {
    // 详见测试类代码
}
```

## 与其他工具类的集成

`CompanyNameGenerator` 可以与项目中的其他工具类协同工作：

- `UnifiedSocialCreditCodeGenerator` - 生成统一社会信用代码
- `ValidationUtils` - 提供各种校验功能
- `TestDataManager` - 管理测试数据文件

## 作者

NIFA Team