package cn.org.nifa.bxcredit.generator.validator;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.org.nifa.bxcredit.generator.model.EnterpriseLoanData;
import cn.org.nifa.bxcredit.generator.util.DateUtils;
import cn.org.nifa.bxcredit.generator.util.ValidationUtils;

/**
 * 企业数据校验器
 * 
 * <AUTHOR> Team
 */
public class EnterpriseDataValidator implements DataValidator<EnterpriseLoanData> {
    
    private static final Logger logger = LoggerFactory.getLogger(EnterpriseDataValidator.class);
    
    @Override
    public ValidationResult validate(EnterpriseLoanData data) {
        List<String> errors = new ArrayList<>();
        
        // 企业名称校验
        if (ValidationUtils.isEmpty(data.getEnterpriseName())) {
            errors.add("企业名称不能为空");
        } else if (!ValidationUtils.validateLength(data.getEnterpriseName(), 1, 60)) {
            errors.add("企业名称长度必须大于0小于等于60");
        }
        
        // 企业证件类型校验
        if (!ValidationUtils.validateEnum(data.getEnterpriseCertType(), ValidationUtils.ENTERPRISE_CERT_TYPES)) {
            errors.add("企业证件类型必须为a或b");
        }
        
        // 企业代码校验
        if (!ValidationUtils.validateOrgCode(data.getEnterpriseCode())) {
            errors.add("企业代码长度必须为9位或18位");
        }
        
        // 企业法人姓名校验
        if (ValidationUtils.isEmpty(data.getLegalPersonName())) {
            errors.add("企业法人姓名不能为空");
        } else if (!ValidationUtils.validateLength(data.getLegalPersonName(), 1, 10)) {
            errors.add("企业法人姓名长度必须小于等于10");
        }
        
        // 企业法人证件校验
        if (!ValidationUtils.validateEnum(data.getLegalPersonCertType(), ValidationUtils.PERSONAL_CERT_TYPES)) {
            errors.add("企业法人证件类型不在有效范围内");
        }
        
        if (ValidationUtils.isEmpty(data.getLegalPersonCertNumber())) {
            errors.add("企业法人证件号码不能为空");
        } else if (!ValidationUtils.validateCertNumber(data.getLegalPersonCertType(), data.getLegalPersonCertNumber())) {
            errors.add("企业法人证件号码格式不正确");
        }
        
        // 业务发生机构校验
        if (!ValidationUtils.validateOrgCode(data.getBusinessInstitution())) {
            errors.add("业务发生机构代码长度必须为9位或18位");
        }
        
        // 业务号校验
        if (!ValidationUtils.validateBusinessNumber(data.getBusinessNumber())) {
            errors.add("业务号格式不正确，应为BN+30位数字");
        }
        
        // 业务类型校验
        if (!ValidationUtils.validateEnum(data.getBusinessType(), ValidationUtils.BUSINESS_TYPES)) {
            errors.add("业务类型不在有效范围内");
        }
        
        // 业务种类校验
        if (!ValidationUtils.validateEnum(data.getBusinessCategory(), ValidationUtils.ENTERPRISE_BUSINESS_CATEGORIES)) {
            errors.add("业务种类不在有效范围内");
        }
        
        // 日期格式校验
        if (!ValidationUtils.validateDateFormat(data.getOpenDate())) {
            errors.add("开户日期格式不正确");
        }
        if (!ValidationUtils.validateDateFormat(data.getDueDate())) {
            errors.add("到期日期格式不正确");
        }
        if (!ValidationUtils.validateDateFormat(data.getBusinessDate())) {
            errors.add("业务发生日期格式不正确");
        }
        
        // 日期逻辑校验
        if (ValidationUtils.validateDateFormat(data.getOpenDate()) && 
            ValidationUtils.validateDateFormat(data.getDueDate())) {
            if (DateUtils.compareDates(data.getOpenDate(), data.getDueDate()) > 0) {
                errors.add("开户日期不能晚于到期日期");
            }
        }
        
        // 循环业务到期日期校验
        if ("4".equals(data.getBusinessType()) && !"20991231".equals(data.getDueDate())) {
            errors.add("循环业务到期日期必须为20991231");
        }
        
        // 授信额度校验
        if (!ValidationUtils.isNonNegativeInteger(data.getCreditLimit())) {
            errors.add("授信额度必须为非负整数");
        }
        
        // 余额校验
        if (!ValidationUtils.isNonNegativeInteger(data.getBalance())) {
            errors.add("余额必须为非负整数");
        }
        
        // 逾期金额校验
        if (!ValidationUtils.isNonNegativeInteger(data.getOverdueAmount())) {
            errors.add("当前逾期总额必须为非负整数");
        }
        
        // 还款状态校验
        if (!ValidationUtils.validateEnum(data.getRepaymentStatus(), ValidationUtils.ENTERPRISE_REPAYMENT_STATUS)) {
            errors.add("本月还款状态不在有效范围内");
        }
        
        // 业务逻辑校验
        validateBusinessLogic(data, errors);
        
        boolean isValid = errors.isEmpty();
        if (!isValid) {
            logger.debug("企业数据校验失败: {}", errors);
        }
        
        return new ValidationResult(isValid, errors);
    }
    
    @Override
    public List<ValidationResult> validateBatch(List<EnterpriseLoanData> dataList) {
        List<ValidationResult> results = new ArrayList<>();
        
        for (EnterpriseLoanData data : dataList) {
            results.add(validate(data));
        }
        
        long validCount = results.stream().mapToLong(r -> r.isValid() ? 1 : 0).sum();
        logger.info("企业数据批量校验完成，总数: {}, 有效: {}, 无效: {}", 
                   dataList.size(), validCount, dataList.size() - validCount);
        
        return results;
    }
    
    private void validateBusinessLogic(EnterpriseLoanData data, List<String> errors) {
        String repaymentStatus = data.getRepaymentStatus();
        
        // 结清状态校验
        if ("C".equals(repaymentStatus)) {
            if (!"0".equals(data.getBalance())) {
                errors.add("结清状态时余额必须为0");
            }
            if (!"0".equals(data.getOverdueAmount())) {
                errors.add("结清状态时当前逾期总额必须为0");
            }
        }
        
        // 第三方代偿状态校验
        if ("D".equals(repaymentStatus)) {
            if (ValidationUtils.isEmpty(data.getCompensationInstitution())) {
                errors.add("第三方代偿状态时当前代偿机构不能为空");
            }
            if ("0".equals(data.getCompensationAmount())) {
                errors.add("第三方代偿状态时当前代偿总额不能为0");
            }
        } else {
            // 非代偿状态时，代偿相关字段应为空
            if (!ValidationUtils.isEmpty(data.getCompensationInstitution()) || 
                !"0".equals(data.getCompensationAmount())) {
                errors.add("非代偿状态时代偿机构和代偿总额应为空或0");
            }
        }
        
        // 逾期状态校验
        if (repaymentStatus.matches("[1-7]")) {
            if ("0".equals(data.getOverdueAmount())) {
                errors.add("逾期状态时当前逾期总额不能为0");
            }
        }
        
        // 授信额度与余额关系校验
        if (!"4".equals(data.getBusinessType())) {
            try {
                long creditLimit = Long.parseLong(data.getCreditLimit());
                long balance = Long.parseLong(data.getBalance());
                if (creditLimit < balance) {
                    errors.add("非循环业务时授信额度必须大于等于余额");
                }
            } catch (NumberFormatException e) {
                // 数字格式错误已在前面校验
            }
        }
    }
}