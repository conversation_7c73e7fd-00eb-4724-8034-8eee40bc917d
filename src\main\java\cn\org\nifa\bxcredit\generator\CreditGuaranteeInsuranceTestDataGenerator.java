package cn.org.nifa.bxcredit.generator;

import java.io.File;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.org.nifa.bxcredit.generator.generator.CreditGuaranteeInsuranceDataGenerator;
import cn.org.nifa.bxcredit.generator.model.CreditGuaranteeInsuranceData;
import cn.org.nifa.bxcredit.generator.processor.CreditGuaranteeInsuranceFileProcessor;
import cn.org.nifa.bxcredit.generator.util.TestDataManager;
import cn.org.nifa.bxcredit.generator.validator.CreditGuaranteeInsuranceRuleValidator;
import cn.org.nifa.bxcredit.generator.validator.ValidationResult;

/**
 * 信用保证保险测试数据生成主类
 * 
 * <AUTHOR> Team
 */
public class CreditGuaranteeInsuranceTestDataGenerator {

    private static final Logger logger = LoggerFactory.getLogger(CreditGuaranteeInsuranceTestDataGenerator.class);

    public static void main(String[] args) {
        logger.info("=== 信用保证保险测试数据生成工具启动 ===");

        try {
            generateCreditGuaranteeInsuranceTestData();
            logger.info("=== 信用保证保险测试数据生成完成 ===");
        } catch (Exception e) {
            logger.error("信用保证保险测试数据生成失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 生成信用保证保险测试数据
     */
    private static void generateCreditGuaranteeInsuranceTestData() {
        logger.info("开始生成信用保证保险测试数据...");

        // 1. 数据生成
        CreditGuaranteeInsuranceDataGenerator generator = new CreditGuaranteeInsuranceDataGenerator();

        // 生成正向测试数据（每种类型各10条）
        List<CreditGuaranteeInsuranceData> positiveData = generator.generatePositiveDataBatch(30);

        // 生成反向测试数据
        List<CreditGuaranteeInsuranceData> negativeData = generator.generateAllNegativeData();

        // 合并数据
        positiveData.addAll(negativeData);
        logger.info("生成信用保证保险数据总量: {} 条", positiveData.size());

        // 2. 数据校验
        CreditGuaranteeInsuranceRuleValidator validator = new CreditGuaranteeInsuranceRuleValidator();
        List<ValidationResult> results = validator.validateBatch(positiveData);

        long validCount = results.stream().filter(ValidationResult::isValid).count();
        logger.info("信用保证保险数据校验结果 - 总数: {}, 有效: {}, 无效: {}",
                results.size(), validCount, results.size() - validCount);

        // 3. 创建输出目录
        String timestampDir = TestDataManager.generateTimestampedFileName("insurance", "");
        String outputDir = TestDataManager.getInsuranceDataPath(timestampDir);

        // 确保目录存在
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs();
        }

        // 4. 文件处理
        CreditGuaranteeInsuranceFileProcessor fileProcessor = new CreditGuaranteeInsuranceFileProcessor();

        // 处理承保信息
        CreditGuaranteeInsuranceFileProcessor.ProcessResult underwritingResult = fileProcessor
                .processUnderwritingData(positiveData, outputDir);
        logProcessResult("承保信息", underwritingResult);

        // 处理代偿信息
        CreditGuaranteeInsuranceFileProcessor.ProcessResult compensationResult = fileProcessor
                .processCompensationData(positiveData, outputDir);
        logProcessResult("代偿信息", compensationResult);

        // 处理追偿信息
        CreditGuaranteeInsuranceFileProcessor.ProcessResult recoveryResult = fileProcessor
                .processRecoveryData(positiveData, outputDir);
        logProcessResult("追偿信息", recoveryResult);

        // 5. 生成数据报告
        generateDataReport(positiveData, results, outputDir);

        // 6. 归档旧数据
        TestDataManager.archiveOldData("test-data/generated/insurance", 10);

        logger.info("信用保证保险测试数据生成完成，输出目录: {}", outputDir);
    }

    /**
     * 记录处理结果
     */
    private static void logProcessResult(String dataType, CreditGuaranteeInsuranceFileProcessor.ProcessResult result) {
        if (result.isSuccess()) {
            logger.info("{}处理成功:", dataType);
            if (result.getTxtFilePath() != null) {
                logger.info("- TXT文件: {}", result.getTxtFilePath());
            }
            if (result.getZipFilePath() != null) {
                logger.info("- ZIP文件: {}", result.getZipFilePath());
            }
            if (result.getEncFilePath() != null) {
                logger.info("- ENC文件: {}", result.getEncFilePath());
            } else {
                logger.warn("- ENC文件: 加密失败，仅生成ZIP文件");
            }
        } else {
            logger.error("{}处理失败: {}", dataType, result.getMessage());
        }
    }

    /**
     * 生成数据报告
     */
    private static void generateDataReport(List<CreditGuaranteeInsuranceData> dataList,
            List<ValidationResult> results, String outputDir) {
        try {
            StringBuilder report = new StringBuilder();
            report.append("信用保证保险测试数据生成报告\n");
            // 添加50个等号作为分隔线
            StringBuilder separator = new StringBuilder();
            for (int i = 0; i < 50; i++) {
                separator.append("=");
            }
            report.append(separator).append("\n\n");

            // 数据统计
            long underwritingCount = dataList.stream().filter(d -> "UNDERWRITING".equals(d.getDataType())).count();
            long compensationCount = dataList.stream().filter(d -> "COMPENSATION".equals(d.getDataType())).count();
            long recoveryCount = dataList.stream().filter(d -> "RECOVERY".equals(d.getDataType())).count();

            report.append("数据统计:\n");
            report.append("- 承保信息: ").append(underwritingCount).append(" 条\n");
            report.append("- 代偿信息: ").append(compensationCount).append(" 条\n");
            report.append("- 追偿信息: ").append(recoveryCount).append(" 条\n");
            report.append("- 总计: ").append(dataList.size()).append(" 条\n\n");

            // 校验统计
            long validCount = results.stream().filter(ValidationResult::isValid).count();
            report.append("校验统计:\n");
            report.append("- 有效数据: ").append(validCount).append(" 条\n");
            report.append("- 无效数据: ").append(results.size() - validCount).append(" 条\n\n");

            // 错误统计
            report.append("错误统计:\n");
            results.stream()
                    .filter(r -> !r.isValid())
                    .flatMap(r -> r.getErrors().stream())
                    .distinct()
                    .forEach(error -> report.append("- ").append(error).append("\n"));

            // 写入报告文件
            String reportPath = outputDir + File.separator + "data_generation_report.txt";
            java.nio.file.Files.write(java.nio.file.Paths.get(reportPath), report.toString().getBytes());

            logger.info("数据生成报告已保存: {}", reportPath);

        } catch (Exception e) {
            logger.error("生成数据报告失败: {}", e.getMessage(), e);
        }
    }
}