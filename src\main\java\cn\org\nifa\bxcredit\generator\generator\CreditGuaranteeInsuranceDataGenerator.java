package cn.org.nifa.bxcredit.generator.generator;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import cn.org.nifa.bxcredit.config.ConfigManager;
import cn.org.nifa.bxcredit.generator.model.CreditGuaranteeInsuranceData;
import cn.org.nifa.bxcredit.generator.util.BusinessCodeGenerator;
import cn.org.nifa.bxcredit.generator.util.CompanyNameGenerator;
import cn.org.nifa.bxcredit.generator.util.IdCardGenerator;
import cn.org.nifa.bxcredit.generator.util.NameGenerator;
import cn.org.nifa.bxcredit.generator.util.UnifiedSocialCreditCodeGenerator;

/**
 * 信用保证保险数据生成器
 * 
 * <AUTHOR> Team
 */
public class CreditGuaranteeInsuranceDataGenerator implements DataGenerator<CreditGuaranteeInsuranceData> {

    private static final Logger logger = LoggerFactory.getLogger(CreditGuaranteeInsuranceDataGenerator.class);
    private static final Random random = new Random();

    // 被保证人证件类型（自然人）
    private static final String[] PERSON_CERT_TYPES = { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C",
            "X" };

    // 被保证人证件类型（企业）
    private static final String[] ENTERPRISE_CERT_TYPES = { "a", "b" };

    // 企业法人证件类型
    private static final String[] LEGAL_PERSON_CERT_TYPES = { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A",
            "B", "C", "X" };

    // 保险业务种类
    private static final String[] INSURANCE_BUSINESS_TYPES = { "01", "02", "03", "99" };

    // 保险方式
    private static final String[] INSURANCE_METHODS = { "1", "2" };

    // 企业名称样本
    private static final String[] ENTERPRISE_NAMES = {
            "北京科技有限公司", "上海贸易股份有限公司", "深圳创新科技公司", "广州制造业有限公司",
            "杭州电子商务公司", "成都软件开发有限公司", "武汉建筑工程公司", "西安新能源有限公司"
    };

    // 个人姓名样本
    private static final String[] PERSON_NAMES = {
            "张三", "李四", "王五", "赵六", "陈七", "刘八", "杨九", "黄十",
            "周明", "吴亮", "郑强", "王芳", "李娜", "张伟", "刘洋", "陈静"
    };

    // 企业法人姓名样本
    private static final String[] LEGAL_PERSON_NAMES = {
            "张总", "李董", "王经理", "赵总裁", "陈主席", "刘总监", "杨CEO", "黄董事长",
            "周总经理", "吴副总", "郑执行官", "王主任", "李部长", "张厂长", "刘所长", "陈院长"
    };

    @Override
    public CreditGuaranteeInsuranceData generatePositiveData() {
        return generatePositiveData("UNDERWRITING");
    }

    /**
     * 生成指定类型的正向测试数据
     * 
     * @param dataType 数据类型 (UNDERWRITING-承保, COMPENSATION-代偿, RECOVERY-追偿)
     * @return 正向测试数据
     */
    public CreditGuaranteeInsuranceData generatePositiveData(String dataType) {
        CreditGuaranteeInsuranceData.Builder builder = CreditGuaranteeInsuranceData.builder();

        // 随机选择被保证人类型
        String guaranteedPersonType = random.nextBoolean() ? "1" : "2"; // 1-企业, 2-自然人
        builder.guaranteedPersonType(guaranteedPersonType);
        builder.dataType(dataType);

        // 根据被保证人类型生成相应数据
        if ("1".equals(guaranteedPersonType)) {
            // 企业
            try {
                builder.guaranteedPersonName(CompanyNameGenerator.generateRandomCompanyName());
            } catch (Exception e) {
                logger.warn("使用CompanyNameGenerator生成公司名称失败: {}", e.getMessage());
                builder.guaranteedPersonName(ENTERPRISE_NAMES[random.nextInt(ENTERPRISE_NAMES.length)]);
            }
            builder.guaranteedPersonCertType(ENTERPRISE_CERT_TYPES[random.nextInt(ENTERPRISE_CERT_TYPES.length)]);
            builder.guaranteedPersonCertNumber(generateEnterpriseCode());

            // 企业法人信息（必填）
            try {
                builder.legalPersonName(NameGenerator.generateChineseNameTwoChar());
            } catch (Exception e) {
                logger.warn("使用NameGenerator生成法人姓名失败: {}", e.getMessage());
                builder.legalPersonName(LEGAL_PERSON_NAMES[random.nextInt(LEGAL_PERSON_NAMES.length)]);
            }
            builder.legalPersonCertType(LEGAL_PERSON_CERT_TYPES[random.nextInt(LEGAL_PERSON_CERT_TYPES.length)]);
            builder.legalPersonCertNumber(IdCardGenerator.generateRandomIdCard());
        } else {
            // 自然人
            try {
                builder.guaranteedPersonName(NameGenerator.generateChineseNameTwoChar());
            } catch (Exception e) {
                logger.warn("使用NameGenerator生成姓名失败: {}", e.getMessage());
                builder.guaranteedPersonName(PERSON_NAMES[random.nextInt(PERSON_NAMES.length)]);
            }
            builder.guaranteedPersonCertType(PERSON_CERT_TYPES[random.nextInt(PERSON_CERT_TYPES.length)]);
            builder.guaranteedPersonCertNumber(IdCardGenerator.generateRandomIdCard());
        }

        // 业务标识信息
        builder.businessInstitution(generateBusinessInstitution());
        builder.policyNumber(generatePolicyNumber());

        // 根据数据类型生成相应的业务信息
        switch (dataType) {
            case "UNDERWRITING":
                generateUnderwritingInfo(builder);
                break;
            case "COMPENSATION":
                generateCompensationInfo(builder);
                break;
            case "RECOVERY":
                generateRecoveryInfo(builder);
                break;
        }

        return builder.build();
    }

    @Override
    public List<CreditGuaranteeInsuranceData> generatePositiveDataBatch(int count) {
        List<CreditGuaranteeInsuranceData> dataList = new ArrayList<>();

        // 生成承保信息
        int underwritingCount = count / 3;
        for (int i = 0; i < underwritingCount; i++) {
            dataList.add(generatePositiveData("UNDERWRITING"));
        }

        // 生成代偿信息
        int compensationCount = count / 3;
        for (int i = 0; i < compensationCount; i++) {
            dataList.add(generatePositiveData("COMPENSATION"));
        }

        // 生成追偿信息
        int recoveryCount = count - underwritingCount - compensationCount;
        for (int i = 0; i < recoveryCount; i++) {
            dataList.add(generatePositiveData("RECOVERY"));
        }

        logger.info("生成正向测试数据: 承保{}条, 代偿{}条, 追偿{}条",
                underwritingCount, compensationCount, recoveryCount);

        return dataList;
    }
    
    /**
     * 生成指定类型和数量的测试数据
     * 
     * @param dataType 数据类型 (UNDERWRITING-承保, COMPENSATION-代偿, RECOVERY-追偿)
     * @param count 数据数量
     * @return 测试数据列表
     */
    public List<CreditGuaranteeInsuranceData> generateDataBatch(String dataType, int count) {
        List<CreditGuaranteeInsuranceData> dataList = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            dataList.add(generatePositiveData(dataType));
        }
        
        logger.info("生成{}条{}数据", count, getDataTypeDisplayName(dataType));
        
        return dataList;
    }
    
    /**
     * 获取数据类型的显示名称
     */
    private String getDataTypeDisplayName(String dataType) {
        switch (dataType) {
            case "UNDERWRITING":
                return "承保信息";
            case "COMPENSATION":
                return "代偿信息";
            case "RECOVERY":
                return "追偿信息";
            default:
                return dataType;
        }
    }

    @Override
    public CreditGuaranteeInsuranceData generateNegativeData(String ruleViolation) {
        CreditGuaranteeInsuranceData.Builder builder = CreditGuaranteeInsuranceData.builder();

        // 先生成基础正向数据
        CreditGuaranteeInsuranceData baseData = generatePositiveData();
        copyBaseData(builder, baseData);

        // 根据违反的规则类型生成错误数据
        switch (ruleViolation) {
            case "GUARANTEED_PERSON_TYPE_INVALID":
                builder.guaranteedPersonType("3"); // 无效的被保证人类型
                break;
            case "GUARANTEED_PERSON_NAME_EMPTY":
                builder.guaranteedPersonName("");
                break;
            case "GUARANTEED_PERSON_NAME_TOO_LONG":
                builder.guaranteedPersonName("这是一个非常长的被保证人名称，超过了64个字符的限制，用于测试长度校验规则的有效性和系统的健壮性");
                break;
            case "GUARANTEED_PERSON_CERT_NUMBER_INVALID_LENGTH":
                builder.guaranteedPersonCertNumber("12345"); // 长度不为18
                break;
            case "GUARANTEED_PERSON_CERT_NUMBER_INVALID_LAST_CHAR":
                builder.guaranteedPersonCertNumber("11010119900101001x"); // 最后一位为小写x
                break;
            case "ENTERPRISE_CERT_TYPE_INVALID":
                builder.guaranteedPersonType("1"); // 企业
                builder.guaranteedPersonCertType("0"); // 企业证件类型应为a或b
                break;
            case "PERSON_CERT_TYPE_INVALID":
                builder.guaranteedPersonType("2"); // 自然人
                builder.guaranteedPersonCertType("a"); // 自然人证件类型不应为a或b
                break;
            case "LEGAL_PERSON_NAME_MISSING":
                builder.guaranteedPersonType("1"); // 企业
                builder.legalPersonName(""); // 企业法人姓名为必填项
                break;
            case "LEGAL_PERSON_CERT_TYPE_MISSING":
                builder.guaranteedPersonType("1"); // 企业
                builder.legalPersonCertType(""); // 企业法人证件类型为必填项
                break;
            case "LEGAL_PERSON_CERT_NUMBER_MISSING":
                builder.guaranteedPersonType("1"); // 企业
                builder.legalPersonCertNumber(""); // 企业法人证件号码为必填项
                break;
            case "BUSINESS_INSTITUTION_INVALID_LENGTH":
                builder.businessInstitution("12345678"); // 长度不为9或18
                break;
            case "POLICY_NUMBER_INVALID_FORMAT":
                builder.policyNumber("ABC123456"); // 不以POL开头
                break;
            case "POLICY_NUMBER_TOO_LONG":
                StringBuilder longNumber = new StringBuilder("POL");
                for (int i = 0; i < 70; i++) {
                    longNumber.append("1");
                }
                builder.policyNumber(longNumber.toString()); // 超过64字符
                break;
            case "INSURANCE_BUSINESS_TYPE_INVALID":
                builder.insuranceBusinessType("05"); // 无效的保险业务种类
                break;
            case "INSURANCE_METHOD_INVALID":
                builder.insuranceMethod("3"); // 无效的保险方式
                break;
            case "INSURANCE_START_DATE_INVALID":
                builder.insuranceStartDate("20230230"); // 无效日期
                break;
            case "INSURANCE_START_DATE_AFTER_END_DATE":
                builder.insuranceStartDate("20241201");
                builder.insuranceEndDate("20241130"); // 起始日期晚于结束日期
                break;
            case "INSURANCE_START_DATE_FUTURE":
                LocalDate futureDate = LocalDate.now().plusDays(30);
                builder.insuranceStartDate(futureDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                break;
        }

        return builder.build();
    }

    @Override
    public List<CreditGuaranteeInsuranceData> generateAllNegativeData() {
        List<String> ruleViolations = Arrays.asList(
                "GUARANTEED_PERSON_TYPE_INVALID",
                "GUARANTEED_PERSON_NAME_EMPTY",
                "GUARANTEED_PERSON_NAME_TOO_LONG",
                "GUARANTEED_PERSON_CERT_NUMBER_INVALID_LENGTH",
                "GUARANTEED_PERSON_CERT_NUMBER_INVALID_LAST_CHAR",
                "ENTERPRISE_CERT_TYPE_INVALID",
                "PERSON_CERT_TYPE_INVALID",
                "LEGAL_PERSON_NAME_MISSING",
                "LEGAL_PERSON_CERT_TYPE_MISSING",
                "LEGAL_PERSON_CERT_NUMBER_MISSING",
                "BUSINESS_INSTITUTION_INVALID_LENGTH",
                "POLICY_NUMBER_INVALID_FORMAT",
                "POLICY_NUMBER_TOO_LONG",
                "INSURANCE_BUSINESS_TYPE_INVALID",
                "INSURANCE_METHOD_INVALID",
                "INSURANCE_START_DATE_INVALID",
                "INSURANCE_START_DATE_AFTER_END_DATE",
                "INSURANCE_START_DATE_FUTURE");

        List<CreditGuaranteeInsuranceData> negativeDataList = new ArrayList<>();
        for (String ruleViolation : ruleViolations) {
            negativeDataList.add(generateNegativeData(ruleViolation));
        }

        logger.info("生成反向测试数据: {}条", negativeDataList.size());
        return negativeDataList;
    }

    /**
     * 生成承保信息
     */
    private void generateUnderwritingInfo(CreditGuaranteeInsuranceData.Builder builder) {
        builder.insuranceBusinessType(INSURANCE_BUSINESS_TYPES[random.nextInt(INSURANCE_BUSINESS_TYPES.length)]);
        builder.insuranceMethod(INSURANCE_METHODS[random.nextInt(INSURANCE_METHODS.length)]);

        // 生成保险起始日期（过去的日期）
        LocalDate startDate = LocalDate.now().minusDays(random.nextInt(365) + 1);
        builder.insuranceStartDate(startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        // 生成保险到期日期（起始日期之后）
        LocalDate endDate = startDate.plusDays(random.nextInt(365) + 30);
        builder.insuranceEndDate(endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        // 生成保险金额（1-10位数字）
        builder.insuranceAmount(String.valueOf(random.nextInt(9999999) + 1));
    }

    /**
     * 生成代偿信息
     */
    private void generateCompensationInfo(CreditGuaranteeInsuranceData.Builder builder) {
        // 生成代偿日期
        LocalDate compensationDate = LocalDate.now().minusDays(random.nextInt(180) + 1);
        builder.compensationDate(compensationDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        // 生成代偿金额
        builder.compensationAmount(String.valueOf(random.nextInt(999999) + 1));
    }

    /**
     * 生成追偿信息
     */
    private void generateRecoveryInfo(CreditGuaranteeInsuranceData.Builder builder) {
        // 生成追偿日期
        LocalDate recoveryDate = LocalDate.now().minusDays(random.nextInt(90) + 1);
        builder.recoveryDate(recoveryDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        // 生成追偿金额
        builder.recoveryAmount(String.valueOf(random.nextInt(999999) + 1));
    }

    /**
     * 生成业务发生机构代码
     */
    private String generateBusinessInstitution() {
        try {
            // 使用配置文件中的机构代码
            return ConfigManager.getOrgCode();
        } catch (Exception e) {
            logger.warn("从配置文件获取机构代码失败: {}", e.getMessage());
            // 降级处理，使用原有方法
            if (random.nextBoolean()) {
                // 9位组织机构代码
                return BusinessCodeGenerator.generateOrgCode();
            } else {
                // 18位社会信用代码
                return UnifiedSocialCreditCodeGenerator.generateRandomCode();
            }
        }
    }

    /**
     * 生成保单编号
     */
    private String generatePolicyNumber() {
        return "POL" + String.format("%010d", random.nextInt(1000000000));
    }

    /**
     * 生成企业代码
     */
    private String generateEnterpriseCode() {
        // 随机选择9位或18位
        if (random.nextBoolean()) {
            return BusinessCodeGenerator.generateOrgCode();
        } else {
            return BusinessCodeGenerator.generateSocialCreditCode();
        }
    }

    /**
     * 复制基础数据到Builder
     */
    private void copyBaseData(CreditGuaranteeInsuranceData.Builder builder, CreditGuaranteeInsuranceData baseData) {
        builder.guaranteedPersonType(baseData.getGuaranteedPersonType())
                .guaranteedPersonName(baseData.getGuaranteedPersonName())
                .guaranteedPersonCertType(baseData.getGuaranteedPersonCertType())
                .guaranteedPersonCertNumber(baseData.getGuaranteedPersonCertNumber())
                .legalPersonName(baseData.getLegalPersonName())
                .legalPersonCertType(baseData.getLegalPersonCertType())
                .legalPersonCertNumber(baseData.getLegalPersonCertNumber())
                .businessInstitution(baseData.getBusinessInstitution())
                .policyNumber(baseData.getPolicyNumber())
                .insuranceBusinessType(baseData.getInsuranceBusinessType())
                .insuranceMethod(baseData.getInsuranceMethod())
                .insuranceStartDate(baseData.getInsuranceStartDate())
                .insuranceEndDate(baseData.getInsuranceEndDate())
                .insuranceAmount(baseData.getInsuranceAmount())
                .compensationDate(baseData.getCompensationDate())
                .compensationAmount(baseData.getCompensationAmount())
                .recoveryDate(baseData.getRecoveryDate())
                .recoveryAmount(baseData.getRecoveryAmount())
                .dataType(baseData.getDataType());
    }
}