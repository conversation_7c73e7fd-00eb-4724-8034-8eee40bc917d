package cn.org.nifa.bxcredit.crypto;

import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.file.Files;

import org.bouncycastle.math.ec.ECPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 标准SM工具类
 * 提供与com.cfcc.jaf.crypto.sm.SMUtil完全兼容的接口
 * 基于StandardSM2Implementation实现
 * 
 * <AUTHOR> Team
 */
public class StandardSMUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(StandardSMUtil.class);
    
    /**
     * 创建椭圆曲线点（从字节数组）
     */
    public static ECPoint createECPoint(byte[] point) {
        return StandardSM2Implementation.getInstance().createECPoint(point);
    }
    
    /**
     * 创建椭圆曲线点（从坐标字节数组）
     */
    public static ECPoint createECPoint(byte[] x, byte[] y) {
        return StandardSM2Implementation.getInstance().createECPoint(x, y);
    }
    
    /**
     * 创建椭圆曲线点（从BigInteger坐标）
     */
    public static ECPoint createECPoint(BigInteger x, BigInteger y) {
        return StandardSM2Implementation.getInstance().createECPoint(x, y);
    }
    
    /**
     * 创建椭圆曲线点（从十六进制坐标）
     */
    public static ECPoint createECPoint(String hexX, String hexY) {
        return StandardSM2Implementation.getInstance().createECPoint(hexX, hexY);
    }
    
    /**
     * SM2加密
     * 
     * @param pubKey 公钥点
     * @param plaintext 明文数据
     * @return 加密后的数据
     */
    public static byte[] encryptBySM2(ECPoint pubKey, byte[] plaintext) {
        return StandardSM2Implementation.getInstance().encryptBySM2(pubKey, plaintext);
    }
    
    /**
     * SM2加密（字符串）
     * 
     * @param pubKey 公钥点
     * @param plaintext 明文字符串
     * @return 加密后的Base64字符串
     */
    public static String encryptBySM2(ECPoint pubKey, String plaintext) {
        if (plaintext == null) {
            throw new IllegalArgumentException("明文不能为空");
        }
        byte[] cipher = encryptBySM2(pubKey, plaintext.getBytes());
        return java.util.Base64.getEncoder().encodeToString(cipher);
    }
    
    /**
     * SM2解密（从BigInteger私钥）
     * 
     * @param prvKey 私钥
     * @param ciphertext 密文数据
     * @param needverify 是否需要C3校验
     * @return 解密后的数据
     */
    public static byte[] decryptBySM2(BigInteger prvKey, byte[] ciphertext, boolean needverify) {
        return StandardSM2Implementation.getInstance().decryptBySM2(prvKey, ciphertext, needverify);
    }
    
    /**
     * SM2解密（从字节数组私钥）
     * 
     * @param prvKey 私钥字节数组
     * @param ciphertext 密文数据
     * @return 解密后的数据
     */
    public static byte[] decryptBySM2(byte[] prvKey, byte[] ciphertext) {
        return StandardSM2Implementation.getInstance().decryptBySM2(prvKey, ciphertext);
    }
    
    /**
     * SM2解密（从十六进制私钥）
     * 
     * @param privateKeyHex 私钥十六进制字符串
     * @param ciphertext 密文数据
     * @return 解密后的数据
     */
    public static byte[] decryptBySM2(String privateKeyHex, byte[] ciphertext) {
        return StandardSM2Implementation.getInstance().decryptBySM2(privateKeyHex, ciphertext);
    }
    
    /**
     * SM2解密（字符串）
     * 
     * @param privateKeyHex 私钥十六进制字符串
     * @param ciphertext Base64密文字符串
     * @return 解密后的字符串
     */
    public static String decryptBySM2(String privateKeyHex, String ciphertext) {
        if (ciphertext == null) {
            throw new IllegalArgumentException("密文不能为空");
        }
        byte[] cipher = java.util.Base64.getDecoder().decode(ciphertext);
        byte[] plaintext = decryptBySM2(privateKeyHex, cipher);
        return new String(plaintext);
    }
    
    /**
     * 生成SM2密钥对
     * 
     * @return 密钥对字节数组 [私钥 + 公钥X + 公钥Y]
     */
    public static byte[] genSM2KeyPair() {
        return StandardSM2Implementation.getInstance().genSM2KeyPair();
    }
    
    /**
     * 根据私钥生成公钥
     * 
     * @param prvKey 私钥字节数组
     * @return 公钥点
     */
    public static ECPoint genSM2PubKey(byte[] prvKey) {
        return StandardSM2Implementation.getInstance().genSM2PubKey(prvKey);
    }
    
    /**
     * 根据私钥生成公钥（十六进制）
     * 
     * @param privateKeyHex 私钥十六进制字符串
     * @return 公钥点
     */
    public static ECPoint genSM2PubKey(String privateKeyHex) {
        return StandardSM2Implementation.getInstance().genSM2PubKey(privateKeyHex);
    }
    
    /**
     * 工具方法：字节数组转BigInteger
     */
    public static BigInteger toBigInteger(byte[] ba) {
        return StandardSM2Implementation.toBigInteger(ba);
    }
    
    /**
     * 工具方法：十六进制字符串转字节数组
     */
    public static byte[] hexToBytes(String hex) {
        return StandardSM2Implementation.hexToBytes(hex);
    }
    
    /**
     * 工具方法：字节数组转十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        return StandardSM2Implementation.bytesToHex(bytes);
    }
    
    /**
     * 加密文件
     * 
     * @param sourceFile 源文件路径
     * @param targetFile 目标文件路径
     * @param publicKeyX 公钥X坐标（十六进制）
     * @param publicKeyY 公钥Y坐标（十六进制）
     * @return 加密后的文件
     */
    public static File encryptFile(String sourceFile, String targetFile, String publicKeyX, String publicKeyY) {
        return StandardSM2Implementation.getInstance().encryptFile(sourceFile, targetFile, publicKeyX, publicKeyY);
    }
    
    /**
     * 解密文件
     * 
     * @param sourceFile 源文件路径
     * @param targetFile 目标文件路径
     * @param privateKeyHex 私钥（十六进制）
     * @return 解密后的文件
     */
    public static File decryptFile(String sourceFile, String targetFile, String privateKeyHex) {
        return StandardSM2Implementation.getInstance().decryptFile(sourceFile, targetFile, privateKeyHex);
    }
    
    /**
     * 简化的文件加密方法
     * 
     * @param file 要加密的文件
     * @param publicKeyX 公钥X坐标
     * @param publicKeyY 公钥Y坐标
     * @return 加密后的文件字节数组
     */
    public static byte[] encryptFileToBytes(File file, String publicKeyX, String publicKeyY) {
        try {
            byte[] data = Files.readAllBytes(file.toPath());
            ECPoint pubKey = createECPoint(publicKeyX, publicKeyY);
            return encryptBySM2(pubKey, data);
        } catch (IOException e) {
            throw new RuntimeException("文件读取失败", e);
        }
    }
    
    /**
     * 简化的文件解密方法
     * 
     * @param encryptedData 加密的文件数据
     * @param targetFile 目标文件
     * @param privateKeyHex 私钥
     * @return 解密后的文件
     */
    public static File decryptBytesToFile(byte[] encryptedData, File targetFile, String privateKeyHex) {
        try {
            byte[] decrypted = decryptBySM2(privateKeyHex, encryptedData);
            if (!targetFile.getParentFile().exists()) {
                targetFile.getParentFile().mkdirs();
            }
            Files.write(targetFile.toPath(), decrypted);
            logger.info("文件解密完成: {}, 大小: {}", targetFile.getAbsolutePath(), decrypted.length);
            return targetFile;
        } catch (IOException e) {
            throw new RuntimeException("文件写入失败", e);
        }
    }
    
    /**
     * 验证密钥对是否匹配
     * 
     * @param privateKeyHex 私钥十六进制
     * @param publicKeyX 公钥X坐标
     * @param publicKeyY 公钥Y坐标
     * @return 是否匹配
     */
    public static boolean verifyKeyPair(String privateKeyHex, String publicKeyX, String publicKeyY) {
        try {
            ECPoint generatedPubKey = genSM2PubKey(privateKeyHex);
            ECPoint providedPubKey = createECPoint(publicKeyX, publicKeyY);
            
            return generatedPubKey.normalize().equals(providedPubKey.normalize());
        } catch (Exception e) {
            logger.warn("密钥对验证失败", e);
            return false;
        }
    }
    
    /**
     * 测试加解密功能
     * 
     * @param testData 测试数据
     * @param publicKeyX 公钥X坐标
     * @param publicKeyY 公钥Y坐标
     * @param privateKeyHex 私钥
     * @return 测试是否成功
     */
    public static boolean testEncryptionDecryption(String testData, String publicKeyX, String publicKeyY, String privateKeyHex) {
        try {
            // 加密
            ECPoint pubKey = createECPoint(publicKeyX, publicKeyY);
            byte[] encrypted = encryptBySM2(pubKey, testData.getBytes());
            
            // 解密
            byte[] decrypted = decryptBySM2(privateKeyHex, encrypted);
            String result = new String(decrypted);
            
            boolean success = testData.equals(result);
            if (success) {
                logger.info("SM2加解密测试成功");
            } else {
                logger.error("SM2加解密测试失败，原始数据：{}，解密结果：{}", testData, result);
            }
            return success;
        } catch (Exception e) {
            logger.error("SM2加解密测试出现异常", e);
            return false;
        }
    }
} 