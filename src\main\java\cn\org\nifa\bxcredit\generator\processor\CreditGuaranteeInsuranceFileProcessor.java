package cn.org.nifa.bxcredit.generator.processor;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.org.nifa.bxcredit.config.ConfigManager;
import cn.org.nifa.bxcredit.generator.model.CreditGuaranteeInsuranceData;

/**
 * 信用保证保险文件处理器
 * 负责生成承保信息、代偿信息、追偿信息的TXT、ZIP、ENC文件
 * 
 * <AUTHOR> Team
 */
public class CreditGuaranteeInsuranceFileProcessor {

    private static final Logger logger = LoggerFactory.getLogger(CreditGuaranteeInsuranceFileProcessor.class);

    private final TxtFileProcessor<CreditGuaranteeInsuranceData> txtProcessor;
    private final ZipFileProcessor zipProcessor;
    private final EncFileProcessor encProcessor;

    public CreditGuaranteeInsuranceFileProcessor() {
        this.txtProcessor = new TxtFileProcessor<>();
        this.zipProcessor = new ZipFileProcessor();
        this.encProcessor = new EncFileProcessor();
    }

    /**
     * 处理承保信息数据
     * 
     * @param dataList  承保信息数据列表
     * @param outputDir 输出目录
     * @return 处理结果信息
     */
    public ProcessResult processUnderwritingData(List<CreditGuaranteeInsuranceData> dataList, String outputDir) {
        logger.info("开始处理承保信息数据，共{}条", dataList.size());

        // 过滤承保信息数据
        List<CreditGuaranteeInsuranceData> underwritingData = dataList.stream()
                .filter(data -> "UNDERWRITING".equals(data.getDataType()))
                .collect(Collectors.toList());

        if (underwritingData.isEmpty()) {
            logger.warn("没有找到承保信息数据");
            return new ProcessResult(false, "没有找到承保信息数据", null, null, null);
        }

        try {
            // 1. 生成TXT文件
            String txtFileName = "221GUARINFO.txt";
            String txtFilePath = outputDir + File.separator + txtFileName;

            txtProcessor.writeToFile(underwritingData, txtFilePath, this::convertUnderwritingDataToString);
            logger.info("承保信息TXT文件生成成功: {}", txtFilePath);

            // 2. 生成ZIP文件
            String zipFileName = generateZipFileName("22");
            String zipFilePath = outputDir + File.separator + zipFileName;

            zipProcessor.zipFile(txtFilePath, zipFilePath);
            logger.info("承保信息ZIP文件生成成功: {}", zipFilePath);

            // 3. 生成ENC文件
            String encFileName = encProcessor.generateEncFileName(zipFileName);
            String encFilePath = outputDir + File.separator + encFileName;

            try {
                encProcessor.encryptFile(zipFilePath, encFilePath);
                logger.info("承保信息ENC文件生成成功: {}", encFilePath);
                return new ProcessResult(true, "承保信息处理成功", txtFilePath, zipFilePath, encFilePath);
            } catch (Exception e) {
                logger.error("承保信息加密失败: {}", e.getMessage());
                return new ProcessResult(true, "承保信息处理成功（加密失败）", txtFilePath, zipFilePath, null);
            }

        } catch (Exception e) {
            logger.error("承保信息处理失败: {}", e.getMessage(), e);
            return new ProcessResult(false, "承保信息处理失败: " + e.getMessage(), null, null, null);
        }
    }

    /**
     * 处理代偿信息数据
     * 
     * @param dataList  代偿信息数据列表
     * @param outputDir 输出目录
     * @return 处理结果信息
     */
    public ProcessResult processCompensationData(List<CreditGuaranteeInsuranceData> dataList, String outputDir) {
        logger.info("开始处理代偿信息数据，共{}条", dataList.size());

        // 过滤代偿信息数据
        List<CreditGuaranteeInsuranceData> compensationData = dataList.stream()
                .filter(data -> "COMPENSATION".equals(data.getDataType()))
                .collect(Collectors.toList());

        if (compensationData.isEmpty()) {
            logger.warn("没有找到代偿信息数据");
            return new ProcessResult(false, "没有找到代偿信息数据", null, null, null);
        }

        try {
            // 1. 生成TXT文件
            String txtFileName = "222COMPENSATORYINFO.txt";
            String txtFilePath = outputDir + File.separator + txtFileName;

            txtProcessor.writeToFile(compensationData, txtFilePath, this::convertCompensationDataToString);
            logger.info("代偿信息TXT文件生成成功: {}", txtFilePath);

            // 2. 生成ZIP文件
            String zipFileName = generateZipFileName("22");
            String zipFilePath = outputDir + File.separator + zipFileName;

            zipProcessor.zipFile(txtFilePath, zipFilePath);
            logger.info("代偿信息ZIP文件生成成功: {}", zipFilePath);

            // 3. 生成ENC文件
            String encFileName = encProcessor.generateEncFileName(zipFileName);
            String encFilePath = outputDir + File.separator + encFileName;

            try {
                encProcessor.encryptFile(zipFilePath, encFilePath);
                logger.info("代偿信息ENC文件生成成功: {}", encFilePath);
                return new ProcessResult(true, "代偿信息处理成功", txtFilePath, zipFilePath, encFilePath);
            } catch (Exception e) {
                logger.error("代偿信息加密失败: {}", e.getMessage());
                return new ProcessResult(true, "代偿信息处理成功（加密失败）", txtFilePath, zipFilePath, null);
            }

        } catch (Exception e) {
            logger.error("代偿信息处理失败: {}", e.getMessage(), e);
            return new ProcessResult(false, "代偿信息处理失败: " + e.getMessage(), null, null, null);
        }
    }

    /**
     * 处理追偿信息数据
     * 
     * @param dataList  追偿信息数据列表
     * @param outputDir 输出目录
     * @return 处理结果信息
     */
    public ProcessResult processRecoveryData(List<CreditGuaranteeInsuranceData> dataList, String outputDir) {
        logger.info("开始处理追偿信息数据，共{}条", dataList.size());

        // 过滤追偿信息数据
        List<CreditGuaranteeInsuranceData> recoveryData = dataList.stream()
                .filter(data -> "RECOVERY".equals(data.getDataType()))
                .collect(Collectors.toList());

        if (recoveryData.isEmpty()) {
            logger.warn("没有找到追偿信息数据");
            return new ProcessResult(false, "没有找到追偿信息数据", null, null, null);
        }

        try {
            // 1. 生成TXT文件
            String txtFileName = "223DUNINFO.txt";
            String txtFilePath = outputDir + File.separator + txtFileName;

            txtProcessor.writeToFile(recoveryData, txtFilePath, this::convertRecoveryDataToString);
            logger.info("追偿信息TXT文件生成成功: {}", txtFilePath);

            // 2. 生成ZIP文件
            String zipFileName = generateZipFileName("22");
            String zipFilePath = outputDir + File.separator + zipFileName;

            zipProcessor.zipFile(txtFilePath, zipFilePath);
            logger.info("追偿信息ZIP文件生成成功: {}", zipFilePath);

            // 3. 生成ENC文件
            String encFileName = encProcessor.generateEncFileName(zipFileName);
            String encFilePath = outputDir + File.separator + encFileName;

            try {
                encProcessor.encryptFile(zipFilePath, encFilePath);
                logger.info("追偿信息ENC文件生成成功: {}", encFilePath);
                return new ProcessResult(true, "追偿信息处理成功", txtFilePath, zipFilePath, encFilePath);
            } catch (Exception e) {
                logger.error("追偿信息加密失败: {}", e.getMessage());
                return new ProcessResult(true, "追偿信息处理成功（加密失败）", txtFilePath, zipFilePath, null);
            }

        } catch (Exception e) {
            logger.error("追偿信息处理失败: {}", e.getMessage(), e);
            return new ProcessResult(false, "追偿信息处理失败: " + e.getMessage(), null, null, null);
        }
    }

    /**
     * 将承保信息数据转换为字符串
     */
    private String convertUnderwritingDataToString(CreditGuaranteeInsuranceData data) {
        return txtProcessor.convertToDelimitedString(
                data.getGuaranteedPersonType(), // 被保证人类型
                data.getGuaranteedPersonName(), // 被保证人名称
                data.getGuaranteedPersonCertType(), // 被保证人证件类型
                data.getGuaranteedPersonCertNumber(), // 被保证人证件号码
                data.getLegalPersonName() != null ? data.getLegalPersonName() : "", // 企业法人姓名
                data.getLegalPersonCertType() != null ? data.getLegalPersonCertType() : "", // 企业法人证件类型
                data.getLegalPersonCertNumber() != null ? data.getLegalPersonCertNumber() : "", // 企业法人证件号码
                data.getBusinessInstitution(), // 业务发生机构
                data.getPolicyNumber(), // 保单编号
                data.getInsuranceBusinessType(), // 保险业务种类
                data.getInsuranceMethod(), // 保险方式
                data.getInsuranceStartDate(), // 保险起始日期
                data.getInsuranceEndDate(), // 保险到期日期
                data.getInsuranceAmount() // 保险金额
        );
    }

    /**
     * 将代偿信息数据转换为字符串
     */
    private String convertCompensationDataToString(CreditGuaranteeInsuranceData data) {
        return txtProcessor.convertToDelimitedString(
                data.getGuaranteedPersonType(), // 被保证人类型
                data.getGuaranteedPersonName(), // 被保证人名称
                data.getGuaranteedPersonCertType(), // 被保证人证件类型
                data.getGuaranteedPersonCertNumber(), // 被保证人证件号码
                data.getLegalPersonName() != null ? data.getLegalPersonName() : "", // 企业法人姓名
                data.getLegalPersonCertType() != null ? data.getLegalPersonCertType() : "", // 企业法人证件类型
                data.getLegalPersonCertNumber() != null ? data.getLegalPersonCertNumber() : "", // 企业法人证件号码
                data.getBusinessInstitution(), // 业务发生机构
                data.getPolicyNumber(), // 保单编号
                data.getCompensationDate(), // 代偿日期
                data.getCompensationAmount() // 代偿金额
        );
    }

    /**
     * 将追偿信息数据转换为字符串
     */
    private String convertRecoveryDataToString(CreditGuaranteeInsuranceData data) {
        return txtProcessor.convertToDelimitedString(
                data.getGuaranteedPersonType(), // 被保证人类型
                data.getGuaranteedPersonName(), // 被保证人名称
                data.getGuaranteedPersonCertType(), // 被保证人证件类型
                data.getGuaranteedPersonCertNumber(), // 被保证人证件号码
                data.getLegalPersonName() != null ? data.getLegalPersonName() : "", // 企业法人姓名
                data.getLegalPersonCertType() != null ? data.getLegalPersonCertType() : "", // 企业法人证件类型
                data.getLegalPersonCertNumber() != null ? data.getLegalPersonCertNumber() : "", // 企业法人证件号码
                data.getBusinessInstitution(), // 业务发生机构
                data.getPolicyNumber(), // 保单编号
                data.getRecoveryDate(), // 追偿日期
                data.getRecoveryAmount() // 追偿金额
        );
    }

    /**
     * 生成ZIP文件名
     * 
     * @param typeCode 类型代码 (22表示信用保证保险)
     * @return ZIP文件名
     */
    private String generateZipFileName(String typeCode) {
        String orgCode = ConfigManager.getOrgCode();

        // 取组织机构代码的前9位或社会信用代码的第9-17位
        String orgPrefix;
        if (orgCode.length() == 9) {
            orgPrefix = orgCode;
        } else if (orgCode.length() == 18) {
            orgPrefix = orgCode.substring(8, 17);
        } else {
            // 如果长度不符合，使用默认值
            orgPrefix = "123456789";
        }

        // 生成当前日期时间
        LocalDateTime now = LocalDateTime.now();
        String dateTime = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));

        // 生成流水号（0000-9999）
        String serialNumber = String.format("%04d", (int) (Math.random() * 10000));

        // 组装文件名：机构代码(9位) + 日期(8位) + 类型代码(2位) + 流水号(4位) + 其他(4位)
        // 总长度27位
        String fileName = orgPrefix + dateTime + typeCode + serialNumber + ".zip";

        logger.debug("生成ZIP文件名: {}", fileName);
        return fileName;
    }

    /**
     * 处理结果类
     */
    public static class ProcessResult {
        private final boolean success;
        private final String message;
        private final String txtFilePath;
        private final String zipFilePath;
        private final String encFilePath;

        public ProcessResult(boolean success, String message, String txtFilePath, String zipFilePath,
                String encFilePath) {
            this.success = success;
            this.message = message;
            this.txtFilePath = txtFilePath;
            this.zipFilePath = zipFilePath;
            this.encFilePath = encFilePath;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public String getTxtFilePath() {
            return txtFilePath;
        }

        public String getZipFilePath() {
            return zipFilePath;
        }

        public String getEncFilePath() {
            return encFilePath;
        }

        @Override
        public String toString() {
            return String.format("ProcessResult{success=%s, message='%s', txtFile='%s', zipFile='%s', encFile='%s'}",
                    success, message, txtFilePath, zipFilePath, encFilePath);
        }
    }
}