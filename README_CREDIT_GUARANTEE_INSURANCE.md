# 信用保证保险测试数据生成器

## 概述

信用保证保险测试数据生成器是NIFA信用信息共享平台API测试框架的一部分，用于生成符合规范的信用保证保险测试数据，包括承保信息、代偿信息和追偿信息。

## 数据规则

数据生成和校验严格遵循《信用保证保险类业务数据规则及操作步骤》文档中的规定，主要包括以下几个方面：

### 人员标识信息段

- **被保证人类型**：1-企业或其他组织；2-自然人
- **被保证人名称**：长度大于0，小于等于64
- **被保证人证件类型**：
  - 自然人：0-9、A、B、C或X
  - 企业：a-组织机构代码；b-社会信用代码
- **被保证人证件号码**：长度等于18
- **企业法人信息**：当被保证人类型为企业时必填

### 业务标识信息段

- **业务发生机构**：长度9或18
- **保单编号**：格式为POL+数字，长度大于0小于等于64

### 信用保证保险承保信息段

- **保险业务种类**：01-信用保证保险；02-国内贸易信用保险；03-个人消费信用保险；99-其他
- **保险方式**：1-保证；2-信用
- **保险起始日期**：格式为YYYYMMDD，不能晚于当前日期
- **保险到期日期**：格式为YYYYMMDD，不能早于起始日期
- **保险金额**：正整数，长度大于0小于等于10

### 信用保证保险代偿信息段

- **代偿日期**：格式为YYYYMMDD
- **代偿金额**：正整数，长度大于0小于等于10

### 信用保证保险追偿信息段

- **追偿日期**：格式为YYYYMMDD
- **追偿金额**：正整数，长度大于0小于等于10

## 文件格式

### 承保信息

- 文件名：221GUARINFO.txt
- 包含：人员标识信息段、业务标识信息段、信用保证保险承保信息段

### 代偿信息

- 文件名：222COMPENSATORYINFO.txt
- 包含：人员标识信息段、业务标识信息段、信用保证保险代偿信息段

### 追偿信息

- 文件名：223DUNINFO.txt
- 包含：人员标识信息段、业务标识信息段、信用保证保险追偿信息段

## 文件处理流程

1. 生成TXT格式的数据文件
2. 将TXT文件压缩为ZIP包
3. 将ZIP包加密为ENC文件

## 文件命名规则

- ZIP文件命名：机构代码(9位) + 日期(8位) + 类型代码(2位) + 流水号(4位) + .zip
- ENC文件命名：与ZIP文件名前缀一致，后缀为.enc

## 使用方法

### 运行测试数据生成器

```bash
# 方式一：直接运行主类
java -cp bxcredit.jar cn.org.nifa.bxcredit.generator.CreditGuaranteeInsuranceTestDataGenerator

# 方式二：通过Maven运行
mvn exec:java -Dexec.mainClass="cn.org.nifa.bxcredit.generator.CreditGuaranteeInsuranceTestDataGenerator"
```

### 配置参数

在运行前，可以通过环境变量或系统属性配置以下参数：

- `NIFA_ORG_CODE`：机构代码
- `NIFA_ENV`：环境（dev/test/prod）

### 输出目录

生成的测试数据文件将保存在以下目录：

```
test-data/generated/insurance/[timestamp]/
```

每次运行会创建一个以时间戳命名的新目录，避免覆盖之前的测试数据。

## 数据校验

生成的数据会经过严格的校验，确保符合规范要求。校验规则包括：

1. 字段格式和长度校验
2. 字段值范围和枚举值校验
3. 字段间关联性校验
4. 日期有效性校验
5. 文件级唯一性约束校验

校验结果会记录在数据生成报告中，包括有效数据数量、无效数据数量以及具体的错误信息。

## 数据报告

每次生成测试数据后，会在输出目录下生成一个数据报告文件（data_generation_report.txt），包含以下内容：

- 数据统计（承保信息、代偿信息、追偿信息的数量）
- 校验统计（有效数据、无效数据的数量）
- 错误统计（具体的错误信息）

## 代码结构

- `CreditGuaranteeInsuranceTestDataGenerator`：测试数据生成主类
- `CreditGuaranteeInsuranceDataGenerator`：数据生成器
- `CreditGuaranteeInsuranceRuleValidator`：数据规则校验器
- `CreditGuaranteeInsuranceFileProcessor`：文件处理器
- `CreditGuaranteeInsuranceData`：数据模型

## 注意事项

1. 确保机构代码正确配置，否则生成的文件名可能不符合规范
2. 生成的测试数据仅用于测试目的，不应用于生产环境
3. 加密功能依赖于正确配置的密钥，请确保密钥文件存在且有效