package cn.org.nifa.bxcredit.generator.util;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 校验工具类
 * 
 * <AUTHOR> Team
 */
public class ValidationUtils {
    
    // 证件类型枚举
    public static final List<String> PERSONAL_CERT_TYPES = Arrays.asList(
        "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "X"
    );
    
    public static final List<String> ENTERPRISE_CERT_TYPES = Arrays.asList("a", "b");
    
    // 业务类型枚举
    public static final List<String> BUSINESS_TYPES = Arrays.asList("2", "3", "4", "5");
    
    // 个人业务种类枚举
    public static final List<String> PERSONAL_BUSINESS_CATEGORIES = Arrays.asList(
        "11", "21", "31", "41", "51", "71", "99"
    );
    
    // 企业业务种类枚举
    public static final List<String> ENTERPRISE_BUSINESS_CATEGORIES = Arrays.asList(
        "11", "21", "31", "41", "51", "61", "71", "99"
    );
    
    // 还款状态枚举
    public static final List<String> REPAYMENT_STATUS = Arrays.asList(
        "*", "N", "1", "2", "3", "4", "5", "6", "7", "C", "D", "I", "Z", "G"
    );
    
    // 企业还款状态枚举（包含D-第三方代偿）
    public static final List<String> ENTERPRISE_REPAYMENT_STATUS = Arrays.asList(
        "*", "N", "1", "2", "3", "4", "5", "6", "7", "C", "D", "Z", "G"
    );
    
    /**
     * 校验字符串长度
     * 
     * @param value 待校验值
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 是否有效
     */
    public static boolean validateLength(String value, int minLength, int maxLength) {
        if (value == null) {
            return minLength == 0;
        }
        int length = value.length();
        return length >= minLength && length <= maxLength;
    }
    
    /**
     * 校验是否为空或空格
     * 
     * @param value 待校验值
     * @return 是否为空
     */
    public static boolean isEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }
    
    /**
     * 校验是否为数字
     * 
     * @param value 待校验值
     * @return 是否为数字
     */
    public static boolean isNumeric(String value) {
        if (isEmpty(value)) {
            return false;
        }
        return Pattern.matches("\\d+", value);
    }
    
    /**
     * 校验是否为非负整数
     * 
     * @param value 待校验值
     * @return 是否为非负整数
     */
    public static boolean isNonNegativeInteger(String value) {
        if (!isNumeric(value)) {
            return false;
        }
        try {
            long num = Long.parseLong(value);
            return num >= 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 校验业务号格式
     * 
     * @param businessNumber 业务号
     * @return 是否有效
     */
    public static boolean validateBusinessNumber(String businessNumber) {
        if (businessNumber == null || businessNumber.length() != 32) {
            return false;
        }
        if (!businessNumber.startsWith("BN")) {
            return false;
        }
        return isNumeric(businessNumber.substring(2));
    }
    
    /**
     * 校验组织机构代码或社会信用代码
     * 
     * @param code 代码
     * @return 是否有效
     */
    public static boolean validateOrgCode(String code) {
        if (code == null) {
            return false;
        }
        return code.length() == 9 || code.length() == 18;
    }
    
    /**
     * 校验证件号码格式
     * 
     * @param certType 证件类型
     * @param certNumber 证件号码
     * @return 是否有效
     */
    public static boolean validateCertNumber(String certType, String certNumber) {
        if (isEmpty(certNumber)) {
            return false;
        }
        
        switch (certType) {
            case "0": // 身份证
                return IdCardGenerator.validateIdCard(certNumber);
            case "2": // 护照
                return Pattern.matches("E\\d{8}", certNumber);
            case "3": // 军官证
                return Pattern.matches("军\\d{7}", certNumber);
            case "4": // 士官证
                return Pattern.matches("士\\d{7}", certNumber);
            case "5": // 港澳居民来往内地通行证
                return Pattern.matches("[HM]\\d{8}", certNumber);
            case "6": // 台湾同胞来往内地通行证
                return Pattern.matches("L\\d{8}", certNumber);
            case "8": // 外国人居留证
                return Pattern.matches("CAN\\d{12}", certNumber);
            case "9": // 警官证
                return Pattern.matches("\\d{6}", certNumber);
            case "A": // 香港身份证
                return Pattern.matches("[A-Z]\\w{6,7}", certNumber);
            case "B": // 澳门身份证
                return Pattern.matches("\\w{8}", certNumber);
            case "C": // 台湾身份证
                return Pattern.matches("\\w{10}", certNumber);
            default:
                return certNumber.length() <= 18;
        }
    }
    
    /**
     * 校验日期格式
     * 
     * @param date 日期字符串
     * @return 是否有效
     */
    public static boolean validateDateFormat(String date) {
        return DateUtils.isValidDate(date);
    }
    
    /**
     * 校验枚举值
     * 
     * @param value 待校验值
     * @param validValues 有效值列表
     * @return 是否有效
     */
    public static boolean validateEnum(String value, List<String> validValues) {
        return validValues.contains(value);
    }
}