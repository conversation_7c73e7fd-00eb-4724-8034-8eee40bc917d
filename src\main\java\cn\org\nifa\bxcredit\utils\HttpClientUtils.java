package cn.org.nifa.bxcredit.utils;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.org.nifa.bxcredit.exception.NifaApiException;
import okhttp3.ConnectionPool;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * HTTP客户端工具类
 * 使用OkHttp实现高性能HTTP请求
 * 
 * <AUTHOR> Team
 */
public class HttpClientUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final int DEFAULT_TIMEOUT = 30; // 默认超时时间（秒）
    
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(5, 5, TimeUnit.MINUTES))
            .build();
    
    /**
     * 发送POST请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param jsonBody JSON请求体
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    public static String post(String url, Map<String, String> headers, String jsonBody) {
        logger.debug("Sending POST request to: {}", url);
        logger.debug("Request headers: {}", headers);
        logger.debug("Request body: {}", jsonBody);
        
        RequestBody body = RequestBody.create(jsonBody, JSON);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);
        
        // 添加请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        
        Request request = requestBuilder.build();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "";
            
            if (!response.isSuccessful()) {
                logger.error("Request failed with code: {}, response: {}", response.code(), responseBody);
                throw new NifaApiException(String.valueOf(response.code()), 
                        "API request failed with status code: " + response.code() + ", response: " + responseBody);
            }
            
            logger.debug("Response received: {}", responseBody);
            return responseBody;
        } catch (IOException e) {
            logger.error("IO error during API request: {}", e.getMessage(), e);
            throw new NifaApiException("IO_ERROR", "API request failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * 发送POST请求，使用Map作为请求体
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param bodyMap 请求体Map
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    public static String post(String url, Map<String, String> headers, Map<String, String> bodyMap) {
        String jsonBody = JsonUtils.toJson(bodyMap);
        return post(url, headers, jsonBody);
    }
    
    /**
     * 发送GET请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    public static String get(String url, Map<String, String> headers) {
        logger.debug("Sending GET request to: {}", url);
        logger.debug("Request headers: {}", headers);
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .get();
        
        // 添加请求头
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        
        Request request = requestBuilder.build();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "";
            
            if (!response.isSuccessful()) {
                logger.error("Request failed with code: {}, response: {}", response.code(), responseBody);
                throw new NifaApiException(String.valueOf(response.code()), 
                        "API request failed with status code: " + response.code() + ", response: " + responseBody);
            }
            
            logger.debug("Response received: {}", responseBody);
            return responseBody;
        } catch (IOException e) {
            logger.error("IO error during API request: {}", e.getMessage(), e);
            throw new NifaApiException("IO_ERROR", "API request failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建自定义配置的HTTP客户端
     * 
     * @param connectTimeout 连接超时（秒）
     * @param readTimeout 读取超时（秒）
     * @param writeTimeout 写入超时（秒）
     * @return OkHttpClient实例
     */
    public static OkHttpClient createCustomClient(int connectTimeout, int readTimeout, int writeTimeout) {
        return new OkHttpClient.Builder()
                .connectTimeout(connectTimeout, TimeUnit.SECONDS)
                .readTimeout(readTimeout, TimeUnit.SECONDS)
                .writeTimeout(writeTimeout, TimeUnit.SECONDS)
                .build();
    }
    
    /**
     * 获取默认HTTP客户端
     * 
     * @return OkHttpClient实例
     */
    public static OkHttpClient getClient() {
        return client;
    }
}