#!/bin/bash
# 信用保证保险测试数据生成高级脚本
# 支持更多配置选项和环境切换

# 默认配置
ENV="dev"
ORG_CODE="123456789"
OUTPUT_DIR=""
UNDERWRITING_COUNT=10
COMPENSATION_COUNT=10
RECOVERY_COUNT=10
INCLUDE_NEGATIVE=false

# 显示帮助信息
show_help() {
    echo "信用保证保险测试数据生成工具"
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -e, --env ENV             设置环境 (dev/test/prod)"
    echo "  -o, --org-code CODE       设置机构代码"
    echo "  -d, --output-dir DIR      设置输出目录"
    echo "  -u, --underwriting COUNT  设置承保信息数量"
    echo "  -c, --compensation COUNT  设置代偿信息数量"
    echo "  -r, --recovery COUNT      设置追偿信息数量"
    echo "  -n, --negative            包含反向测试数据"
    echo "  -h, --help                显示帮助信息"
    echo "示例:"
    echo "  $0 -e test -u 20 -c 15 -r 10 -n"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -o|--org-code)
            ORG_CODE="$2"
            shift 2
            ;;
        -d|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -u|--underwriting)
            UNDERWRITING_COUNT="$2"
            shift 2
            ;;
        -c|--compensation)
            COMPENSATION_COUNT="$2"
            shift 2
            ;;
        -r|--recovery)
            RECOVERY_COUNT="$2"
            shift 2
            ;;
        -n|--negative)
            INCLUDE_NEGATIVE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 设置环境变量
export NIFA_ENV=$ENV
export NIFA_ORG_CODE=$ORG_CODE

# 构建命令行参数
ARGS=""
ARGS="$ARGS -u $UNDERWRITING_COUNT"
ARGS="$ARGS -c $COMPENSATION_COUNT"
ARGS="$ARGS -r $RECOVERY_COUNT"

if [ "$INCLUDE_NEGATIVE" = true ]; then
    ARGS="$ARGS -n"
fi

if [ -n "$OUTPUT_DIR" ]; then
    ARGS="$ARGS -o \"$OUTPUT_DIR\""
fi

# 显示配置信息
echo "=== 信用保证保险测试数据生成配置 ==="
echo "环境: $ENV"
echo "机构代码: $ORG_CODE"
echo "承保信息数量: $UNDERWRITING_COUNT"
echo "代偿信息数量: $COMPENSATION_COUNT"
echo "追偿信息数量: $RECOVERY_COUNT"
echo "包含反向测试数据: $INCLUDE_NEGATIVE"
if [ -n "$OUTPUT_DIR" ]; then
    echo "输出目录: $OUTPUT_DIR"
else
    echo "输出目录: 默认"
fi
echo "======================================="

# 执行数据生成命令
echo "开始生成信用保证保险测试数据..."
eval "java -cp target/bxcredit-1.0.0.jar cn.org.nifa.bxcredit.generator.CreditGuaranteeInsuranceDataGeneratorCLI $ARGS"

if [ $? -ne 0 ]; then
    echo "数据生成失败，错误代码: $?"
    exit $?
fi

echo "数据生成完成"