package cn.org.nifa.bxcredit.generator.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * 日期工具类
 * 
 * <AUTHOR> Team
 */
public class DateUtils {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    /**
     * 获取当前日期，格式YYYYMMDD
     * 
     * @return 当前日期字符串
     */
    public static String getCurrentDate() {
        return LocalDate.now().format(DATE_FORMATTER);
    }
    
    /**
     * 获取当月最后一天，格式YYYYMMDD
     * 
     * @return 当月最后一天日期字符串
     */
    public static String getLastDayOfMonth() {
        LocalDate now = LocalDate.now();
        LocalDate lastDay = now.withDayOfMonth(now.lengthOfMonth());
        return lastDay.format(DATE_FORMATTER);
    }
    
    /**
     * 计算两个日期之间的天数
     * 
     * @param date1 日期1，格式YYYYMMDD
     * @param date2 日期2，格式YYYYMMDD
     * @return 天数差
     */
    public static long daysBetween(String date1, String date2) {
        LocalDate d1 = LocalDate.parse(date1, DATE_FORMATTER);
        LocalDate d2 = LocalDate.parse(date2, DATE_FORMATTER);
        return ChronoUnit.DAYS.between(d1, d2);
    }
    
    /**
     * 校验日期格式和有效性
     * 
     * @param date 日期字符串，格式YYYYMMDD
     * @return 是否有效
     */
    public static boolean isValidDate(String date) {
        if (date == null || date.length() != 8) {
            return false;
        }
        try {
            LocalDate.parse(date, DATE_FORMATTER);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取指定天数前的日期
     * 
     * @param days 天数
     * @return 日期字符串，格式YYYYMMDD
     */
    public static String getDateBefore(int days) {
        return LocalDate.now().minusDays(days).format(DATE_FORMATTER);
    }
    
    /**
     * 获取指定天数后的日期
     * 
     * @param days 天数
     * @return 日期字符串，格式YYYYMMDD
     */
    public static String getDateAfter(int days) {
        return LocalDate.now().plusDays(days).format(DATE_FORMATTER);
    }
    
    /**
     * 比较两个日期
     * 
     * @param date1 日期1，格式YYYYMMDD
     * @param date2 日期2，格式YYYYMMDD
     * @return date1 < date2 返回负数，date1 = date2 返回0，date1 > date2 返回正数
     */
    public static int compareDates(String date1, String date2) {
        LocalDate d1 = LocalDate.parse(date1, DATE_FORMATTER);
        LocalDate d2 = LocalDate.parse(date2, DATE_FORMATTER);
        return d1.compareTo(d2);
    }
}