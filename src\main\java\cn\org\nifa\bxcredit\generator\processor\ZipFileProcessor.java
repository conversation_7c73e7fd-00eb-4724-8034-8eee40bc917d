package cn.org.nifa.bxcredit.generator.processor;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ZIP文件处理器
 * 
 * <AUTHOR> Team
 */
public class ZipFileProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(ZipFileProcessor.class);
    
    /**
     * 压缩文件
     * 
     * @param sourceFile 源文件路径
     * @param zipFile ZIP文件路径
     */
    public void zipFile(String sourceFile, String zipFile) {
        try (FileOutputStream fos = new FileOutputStream(zipFile);
             ZipOutputStream zos = new ZipOutputStream(fos);
             FileInputStream fis = new FileInputStream(sourceFile)) {
            
            ZipEntry zipEntry = new ZipEntry(new File(sourceFile).getName());
            zos.putNextEntry(zipEntry);
            
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                zos.write(buffer, 0, length);
            }
            
            zos.closeEntry();
            logger.info("文件压缩成功: {} -> {}", sourceFile, zipFile);
            
        } catch (IOException e) {
            logger.error("文件压缩失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件压缩失败", e);
        }
    }
    
    /**
     * 生成ZIP文件名
     * 
     * @param orgCode 机构代码
     * @param fileType 文件类型（12-个人，32-企业）
     * @param sequenceNumber 流水号
     * @return ZIP文件名
     */
    public String generateZipFileName(String orgCode, String fileType, String sequenceNumber) {
        // 文件名格式：机构代码(9位) + 生成日期时间(12位) + 文件类型(2位) + 流水号(4位)
        String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        
        String fileName = String.format("%s%s%s%s", 
                orgCode.length() == 18 ? orgCode.substring(8, 17) : orgCode, // 取机构代码或社会信用代码的相应位数
                timestamp,
                fileType,
                String.format("%04d", Integer.parseInt(sequenceNumber))
        );
        
        if (fileName.length() != 27) {
            logger.warn("生成的文件名长度不为27位: {}", fileName);
        }
        
        return fileName + ".zip";
    }
    
    /**
     * 校验ZIP文件名格式
     * 
     * @param fileName 文件名
     * @return 是否有效
     */
    public boolean validateZipFileName(String fileName) {
        if (fileName == null || !fileName.endsWith(".zip")) {
            return false;
        }
        
        String nameWithoutExt = fileName.substring(0, fileName.length() - 4);
        
        // 检查长度
        if (nameWithoutExt.length() != 27) {
            return false;
        }
        
        // 检查是否只包含字母和数字
        if (!nameWithoutExt.matches("[A-Za-z0-9]+")) {
            return false;
        }
        
        // 检查文件类型位
        String fileType = nameWithoutExt.substring(21, 23);
        if (!"12".equals(fileType) && !"32".equals(fileType)) {
            return false;
        }
        
        // 检查流水号位
        String sequenceNumber = nameWithoutExt.substring(23, 27);
        if (!sequenceNumber.matches("\\d{4}")) {
            return false;
        }
        
        return true;
    }
}