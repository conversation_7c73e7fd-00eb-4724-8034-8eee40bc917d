信用保证保险测试数据生成报告
==================================================

数据统计:
- 承保信息: 28 条
- 代偿信息: 10 条
- 追偿信息: 10 条
- 总计: 48 条

校验统计:
- 有效数据: 26 条
- 无效数据: 22 条

错误统计:
- 被保证人证件号码长度必须为18位
- 被保证人类型必须为1(企业或其他组织)或2(自然人)
- 被保证人名称不能为空
- 企业被保证人证件类型必须为a(组织机构代码)或b(社会信用代码)
- 企业被保证人的企业法人姓名为必填项
- 企业被保证人的企业法人证件类型为必填项
- 企业被保证人的企业法人证件号码为必填项
- 自然人被保证人证件类型必须为0~9、A、B、C或X
- 业务发生机构长度必须为9位(组织机构代码)或18位(统一社会信用代码)
- 保单编号格式必须为POL+数字
- 保单编号长度不能超过64个字符
- 保险业务种类必须为01(信用保证保险)、02(国内贸易信用保险)、03(个人消费信用保险)或99(其他)
- 保险方式必须为1(保证)或2(信用)
- 保险起始日期不能晚于保险到期日期
- 保险起始日期不能晚于当前日期
