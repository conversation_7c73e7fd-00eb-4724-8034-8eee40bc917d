package cn.org.nifa.bxcredit.generator.model;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据生成报告
 * 
 * <AUTHOR> Team
 */
public class DataGenerationReport {
    
    private String reportId;
    private String dataType;
    private LocalDateTime generationTime;
    private int totalRecords;
    private int validRecords;
    private int invalidRecords;
    private List<String> generatedFiles;
    private List<String> validationErrors;
    private String status;
    
    public DataGenerationReport() {}
    
    public String getReportId() { return reportId; }
    public void setReportId(String reportId) { this.reportId = reportId; }
    
    public String getDataType() { return dataType; }
    public void setDataType(String dataType) { this.dataType = dataType; }
    
    public LocalDateTime getGenerationTime() { return generationTime; }
    public void setGenerationTime(LocalDateTime generationTime) { this.generationTime = generationTime; }
    
    public int getTotalRecords() { return totalRecords; }
    public void setTotalRecords(int totalRecords) { this.totalRecords = totalRecords; }
    
    public int getValidRecords() { return validRecords; }
    public void setValidRecords(int validRecords) { this.validRecords = validRecords; }
    
    public int getInvalidRecords() { return invalidRecords; }
    public void setInvalidRecords(int invalidRecords) { this.invalidRecords = invalidRecords; }
    
    public List<String> getGeneratedFiles() { return generatedFiles; }
    public void setGeneratedFiles(List<String> generatedFiles) { this.generatedFiles = generatedFiles; }
    
    public List<String> getValidationErrors() { return validationErrors; }
    public void setValidationErrors(List<String> validationErrors) { this.validationErrors = validationErrors; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
}