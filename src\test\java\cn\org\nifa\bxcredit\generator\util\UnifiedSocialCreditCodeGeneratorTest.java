package cn.org.nifa.bxcredit.generator.util;

import org.testng.Assert;
import org.testng.annotations.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * UnifiedSocialCreditCodeGenerator 工具类单元测试
 * 
 * <AUTHOR> Team
 */
public class UnifiedSocialCreditCodeGeneratorTest {
    
    /**
     * 测试基础代码生成
     */
    @Test
    public void testGenerateRandomCode() {
        String code = UnifiedSocialCreditCodeGenerator.generateRandomCode();
        
        Assert.assertNotNull(code, "生成的代码不能为空");
        Assert.assertEquals(code.length(), 18, "统一社会信用代码长度应为18位");
        Assert.assertTrue(isValidCodeFormat(code), "代码格式应该有效");
        Assert.assertTrue(UnifiedSocialCreditCodeGenerator.validateCode(code), "生成的代码应该通过校验");
        
        System.out.println("生成的统一社会信用代码: " + code);
    }
    
    /**
     * 测试指定前缀的代码生成
     */
    @Test
    public void testGenerateCodeWithPrefix() {
        String deptCode = "9";
        String typeCode = "1";
        String adminCode = "110000";
        
        String code = UnifiedSocialCreditCodeGenerator.generateCodeWithPrefix(deptCode, typeCode, adminCode);
        
        Assert.assertNotNull(code, "生成的代码不能为空");
        Assert.assertEquals(code.length(), 18, "统一社会信用代码长度应为18位");
        Assert.assertEquals(code.substring(0, 1), deptCode, "登记管理部门代码应该匹配");
        Assert.assertEquals(code.substring(1, 2), typeCode, "机构类别代码应该匹配");
        Assert.assertEquals(code.substring(2, 8), adminCode, "行政区划代码应该匹配");
        Assert.assertTrue(UnifiedSocialCreditCodeGenerator.validateCode(code), "生成的代码应该通过校验");
        
        System.out.println("指定前缀生成的代码: " + code);
    }
    
    /**
     * 测试线程安全的代码生成
     */
    @Test
    public void testGenerateRandomCodeThreadSafe() throws InterruptedException {
        int threadCount = 10;
        int codesPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        Set<String> generatedCodes = new HashSet<>();
        
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < codesPerThread; j++) {
                    String code = UnifiedSocialCreditCodeGenerator.generateRandomCodeThreadSafe();
                    synchronized (generatedCodes) {
                        generatedCodes.add(code);
                    }
                    Assert.assertTrue(UnifiedSocialCreditCodeGenerator.validateCode(code), 
                        "线程安全生成的代码应该通过校验");
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).join();
        executor.shutdown();
        executor.awaitTermination(10, TimeUnit.SECONDS);
        
        Assert.assertTrue(generatedCodes.size() > 0, "应该生成了一些代码");
        System.out.println("线程安全测试生成了 " + generatedCodes.size() + " 个不同的代码");
    }
    
    /**
     * 测试校验码计算
     */
    @Test
    public void testCalculateCheckCode() {
        // 使用已知的测试用例
        String code17 = "91110000MA01A1B2C";
        char expectedCheckCode = UnifiedSocialCreditCodeGenerator.calculateCheckCode(code17);
        
        Assert.assertTrue(Character.isLetterOrDigit(expectedCheckCode), "校验码应该是字母或数字");
        
        // 验证完整代码
        String fullCode = code17 + expectedCheckCode;
        Assert.assertTrue(UnifiedSocialCreditCodeGenerator.validateCode(fullCode), 
            "带校验码的完整代码应该通过校验");
        
        System.out.println("测试代码: " + code17 + " -> 校验码: " + expectedCheckCode);
    }
    
    /**
     * 测试代码校验功能
     */
    @Test
    public void testValidateCode() {
        // 测试有效代码
        String validCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();
        Assert.assertTrue(UnifiedSocialCreditCodeGenerator.validateCode(validCode), 
            "生成的代码应该通过校验");
        
        // 测试无效代码
        Assert.assertFalse(UnifiedSocialCreditCodeGenerator.validateCode(null), 
            "null代码应该校验失败");
        Assert.assertFalse(UnifiedSocialCreditCodeGenerator.validateCode(""), 
            "空代码应该校验失败");
        Assert.assertFalse(UnifiedSocialCreditCodeGenerator.validateCode("123456789012345678"), 
            "错误格式的代码应该校验失败");
        
        // 测试错误校验码
        String code17 = validCode.substring(0, 17);
        String wrongCode = code17 + "X"; // 假设X不是正确的校验码
        if (!wrongCode.equals(validCode)) {
            Assert.assertFalse(UnifiedSocialCreditCodeGenerator.validateCode(wrongCode), 
                "错误校验码的代码应该校验失败");
        }
        
        System.out.println("代码校验测试通过");
    }
    
    /**
     * 测试组织机构代码提取
     */
    @Test
    public void testExtractOrganizationCode() {
        String code = UnifiedSocialCreditCodeGenerator.generateRandomCode();
        String orgCode = UnifiedSocialCreditCodeGenerator.extractOrganizationCode(code);
        
        Assert.assertNotNull(orgCode, "组织机构代码不能为空");
        Assert.assertEquals(orgCode.length(), 9, "组织机构代码长度应为9位");
        Assert.assertEquals(orgCode, code.substring(8, 17), "提取的组织机构代码应该匹配");
        
        System.out.println("统一社会信用代码: " + code);
        System.out.println("组织机构代码: " + orgCode);
    }
    
    /**
     * 测试代码唯一性
     */
    @Test
    public void testCodeUniqueness() {
        Set<String> codes = new HashSet<>();
        int testCount = 10000;
        
        for (int i = 0; i < testCount; i++) {
            String code = UnifiedSocialCreditCodeGenerator.generateRandomCode();
            codes.add(code);
        }
        
        // 唯一性测试：生成10000个代码，不同代码的比例应该很高
        double uniqueRatio = (double) codes.size() / testCount;
        Assert.assertTrue(uniqueRatio > 0.95, "代码唯一性不足，重复率过高: " + (1 - uniqueRatio));
        
        System.out.println("唯一性测试: 生成" + testCount + "个代码，其中" + codes.size() + 
                         "个不同，唯一性: " + String.format("%.2f%%", uniqueRatio * 100));
    }
    
    /**
     * 测试字符集有效性
     */
    @Test
    public void testValidCharacters() {
        String validChars = UnifiedSocialCreditCodeGenerator.getValidCharacters();

        Assert.assertNotNull(validChars, "有效字符集不能为空");
        Assert.assertEquals(validChars.length(), 33, "字符集长度应为33（0-9, A-H, J-N, P-Y）");
        Assert.assertFalse(validChars.contains("I"), "字符集不应包含I");
        Assert.assertFalse(validChars.contains("O"), "字符集不应包含O");
        Assert.assertFalse(validChars.contains("Z"), "字符集不应包含Z");

        // 验证字符集包含正确的字符
        String expectedChars = "0123456789ABCDEFGHJKLMNPQRSTUVWXY";
        Assert.assertEquals(validChars, expectedChars, "字符集应该匹配预期");

        System.out.println("有效字符集: " + validChars);
        System.out.println("字符集长度: " + validChars.length());
    }
    
    /**
     * 测试加权因子
     */
    @Test
    public void testWeightFactors() {
        int[] weights = UnifiedSocialCreditCodeGenerator.getWeightFactors();
        
        Assert.assertNotNull(weights, "加权因子数组不能为空");
        Assert.assertEquals(weights.length, 17, "加权因子数组长度应为17");
        
        // 验证加权因子的值
        int[] expectedWeights = {1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 9, 27, 19, 26, 16};
        Assert.assertEquals(weights, expectedWeights, "加权因子应该匹配标准值");
        
        System.out.println("加权因子验证通过");
    }
    
    /**
     * 测试配置数据
     */
    @Test
    public void testConfigurationData() {
        Assert.assertTrue(UnifiedSocialCreditCodeGenerator.getRegistrationDeptCodes().size() > 0, 
            "登记管理部门代码列表不能为空");
        Assert.assertTrue(UnifiedSocialCreditCodeGenerator.getOrganizationTypeCodes().size() > 0, 
            "机构类别代码列表不能为空");
        Assert.assertTrue(UnifiedSocialCreditCodeGenerator.getAdminDivisionCodes().size() > 0, 
            "行政区划代码列表不能为空");
        
        System.out.println("配置数据:");
        System.out.println("登记管理部门代码: " + UnifiedSocialCreditCodeGenerator.getRegistrationDeptCodes());
        System.out.println("机构类别代码: " + UnifiedSocialCreditCodeGenerator.getOrganizationTypeCodes());
        System.out.println("行政区划代码数量: " + UnifiedSocialCreditCodeGenerator.getAdminDivisionCodes().size());
    }
    
    /**
     * 测试批量生成到文件功能
     */
    @Test
    public void testGenerateCodesToFile() throws IOException {
        String testFilePath = "test_uscc_codes.csv";
        int testCount = 100;
        
        try {
            UnifiedSocialCreditCodeGenerator.BatchGenerationResult result = 
                UnifiedSocialCreditCodeGenerator.generateCodesToFile(testCount, testFilePath, true);
            
            // 验证结果
            Assert.assertEquals(result.getUniqueGenerated(), testCount, "应该生成指定数量的唯一代码");
            Assert.assertTrue(result.getTotalTimeMs() > 0, "耗时应该大于0");
            Assert.assertTrue(result.getAverageSpeed() > 0, "平均速度应该大于0");
            
            // 验证文件
            Path filePath = Paths.get(testFilePath);
            Assert.assertTrue(Files.exists(filePath), "输出文件应该存在");
            
            List<String> lines = Files.readAllLines(filePath);
            Assert.assertEquals(lines.size(), testCount + 1, "文件行数应该等于生成数量+1（包含头部）");
            
            // 验证文件头
            Assert.assertEquals(lines.get(0), "统一社会信用代码,组织机构代码", "文件头应该正确");
            
            // 验证数据行
            for (int i = 1; i < lines.size(); i++) {
                String line = lines.get(i);
                String[] parts = line.split(",");
                Assert.assertEquals(parts.length, 2, "每行应该包含两个字段");
                Assert.assertEquals(parts[0].length(), 18, "统一社会信用代码应该是18位");
                Assert.assertEquals(parts[1].length(), 9, "组织机构代码应该是9位");
                Assert.assertTrue(UnifiedSocialCreditCodeGenerator.validateCode(parts[0]), 
                    "代码应该通过校验");
            }
            
            System.out.println("批量生成测试成功");
            System.out.println(result);
            
        } finally {
            // 清理测试文件
            Files.deleteIfExists(Paths.get(testFilePath));
        }
    }
    
    /**
     * 测试多线程批量生成功能
     */
    @Test
    public void testGenerateCodesToFileMultiThreaded() throws IOException, InterruptedException {
        String testFilePath = "test_uscc_codes_mt.csv";
        int testCount = 1000;
        int threadCount = 4;
        
        try {
            UnifiedSocialCreditCodeGenerator.BatchGenerationResult result = 
                UnifiedSocialCreditCodeGenerator.generateCodesToFileMultiThreaded(
                    testCount, testFilePath, false, threadCount);
            
            // 验证结果（多线程环境下可能会有少量超出，允许一定误差）
            Assert.assertTrue(result.getUniqueGenerated() >= testCount, "应该生成至少指定数量的唯一代码");
            Assert.assertTrue(result.getUniqueGenerated() <= testCount + 10, "生成数量不应该超出太多");
            Assert.assertTrue(result.getTotalTimeMs() > 0, "耗时应该大于0");
            Assert.assertTrue(result.getAverageSpeed() > 0, "平均速度应该大于0");
            
            // 验证文件
            Path filePath = Paths.get(testFilePath);
            Assert.assertTrue(Files.exists(filePath), "输出文件应该存在");
            
            List<String> lines = Files.readAllLines(filePath);
            Assert.assertTrue(lines.size() >= testCount + 1, "文件行数应该至少等于生成数量+1（包含头部）");
            Assert.assertTrue(lines.size() <= testCount + 11, "文件行数不应该超出太多");
            
            // 验证文件头
            Assert.assertEquals(lines.get(0), "统一社会信用代码", "文件头应该正确");
            
            // 抽样验证数据行
            for (int i = 1; i < Math.min(101, lines.size()); i++) {
                String code = lines.get(i);
                Assert.assertEquals(code.length(), 18, "统一社会信用代码应该是18位");
                Assert.assertTrue(UnifiedSocialCreditCodeGenerator.validateCode(code), 
                    "代码应该通过校验");
            }
            
            System.out.println("多线程批量生成测试成功");
            System.out.println(result);
            
        } finally {
            // 清理测试文件
            Files.deleteIfExists(Paths.get(testFilePath));
        }
    }
    
    /**
     * 性能测试
     */
    @Test
    public void testPerformance() {
        int testCount = 10000;
        
        // 测试单线程性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < testCount; i++) {
            UnifiedSocialCreditCodeGenerator.generateRandomCode();
        }
        long singleThreadTime = System.currentTimeMillis() - startTime;
        
        // 测试多线程性能
        startTime = System.currentTimeMillis();
        ExecutorService executor = Executors.newFixedThreadPool(4);
        CompletableFuture<Void>[] futures = new CompletableFuture[4];
        
        for (int i = 0; i < 4; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < testCount / 4; j++) {
                    UnifiedSocialCreditCodeGenerator.generateRandomCodeThreadSafe();
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).join();
        executor.shutdown();
        long multiThreadTime = System.currentTimeMillis() - startTime;
        
        System.out.println("性能测试结果:");
        System.out.println("单线程生成 " + testCount + " 个代码耗时: " + singleThreadTime + "ms");
        System.out.println("多线程生成 " + testCount + " 个代码耗时: " + multiThreadTime + "ms");
        System.out.println("单线程速度: " + (testCount * 1000L / Math.max(singleThreadTime, 1)) + " 个/秒");
        System.out.println("多线程速度: " + (testCount * 1000L / Math.max(multiThreadTime, 1)) + " 个/秒");
    }
    
    /**
     * 检查代码格式是否有效
     */
    private boolean isValidCodeFormat(String code) {
        if (code == null || code.length() != 18) {
            return false;
        }
        
        String validChars = UnifiedSocialCreditCodeGenerator.getValidCharacters();
        for (char c : code.toCharArray()) {
            if (validChars.indexOf(c) == -1) {
                return false;
            }
        }
        
        return true;
    }
}
