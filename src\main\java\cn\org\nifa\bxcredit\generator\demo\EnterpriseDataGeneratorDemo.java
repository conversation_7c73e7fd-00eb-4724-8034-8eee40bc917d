package cn.org.nifa.bxcredit.generator.demo;

import cn.org.nifa.bxcredit.generator.util.CompanyNameGenerator;
import cn.org.nifa.bxcredit.generator.util.TestDataManager;
import cn.org.nifa.bxcredit.generator.util.UnifiedSocialCreditCodeGenerator;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Scanner;

/**
 * 企业数据生成器演示程序
 * 
 * 展示公司名称生成器和统一社会信用代码生成器的集成使用，
 * 生成完整的企业基础数据
 * 
 * <AUTHOR> Team
 */
public class EnterpriseDataGeneratorDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 企业数据生成器演示程序 ===");
        
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("请输入要生成的企业数量 [1-10000]: ");
        int count;
        try {
            count = Integer.parseInt(scanner.nextLine());
            if (count < 1 || count > 10000) {
                System.out.println("数量超出范围，使用默认值100。");
                count = 100;
            }
        } catch (NumberFormatException e) {
            System.out.println("输入无效，使用默认值100。");
            count = 100;
        }
        
        System.out.println("请选择公司名称模板：");
        System.out.println("1. 前缀+核心词+后缀（如：未来科技有限公司）");
        System.out.println("2. 核心词+核心词+后缀（如：创新发展集团）");
        System.out.println("3. 核心词+数字/字母+后缀（如：数字时代A888科技）");
        System.out.println("4. 地名+核心词+后缀（如：上海创新信息咨询）");
        System.out.println("5. 随机模板");
        System.out.print("请输入选择 [1-5]: ");
        
        int templateChoice;
        try {
            templateChoice = Integer.parseInt(scanner.nextLine());
            if (templateChoice < 1 || templateChoice > 5) {
                System.out.println("选择无效，使用随机模板。");
                templateChoice = 5;
            }
        } catch (NumberFormatException e) {
            System.out.println("输入无效，使用随机模板。");
            templateChoice = 5;
        }
        
        CompanyNameGenerator.NameTemplate template;
        switch (templateChoice) {
            case 1:
                template = CompanyNameGenerator.NameTemplate.PREFIX_CORE_SUFFIX;
                break;
            case 2:
                template = CompanyNameGenerator.NameTemplate.CORE_CORE_SUFFIX;
                break;
            case 3:
                template = CompanyNameGenerator.NameTemplate.CORE_DIGIT_SUFFIX;
                break;
            case 4:
                template = CompanyNameGenerator.NameTemplate.LOCATION_CORE_SUFFIX;
                break;
            case 5:
            default:
                template = CompanyNameGenerator.NameTemplate.RANDOM;
        }
        
        System.out.println("是否包含组织机构代码？[y/n]: ");
        boolean includeOrgCode = scanner.nextLine().trim().toLowerCase().startsWith("y");
        
        String outputFilePath = TestDataManager.generateTimestampedFileName("enterprise_data", "csv");
        outputFilePath = TestDataManager.getEnterpriseDataPath(outputFilePath);
        
        System.out.println("\n开始生成 " + count + " 条企业数据...");
        
        try {
            generateEnterpriseData(count, template, includeOrgCode, outputFilePath);
            System.out.println("\n生成完成！");
            System.out.println("输出文件: " + outputFilePath);
        } catch (IOException e) {
            System.err.println("生成企业数据失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        scanner.close();
    }
    
    /**
     * 生成企业数据
     * 
     * @param count 生成数量
     * @param template 公司名称模板
     * @param includeOrgCode 是否包含组织机构代码
     * @param outputFilePath 输出文件路径
     * @throws IOException 文件操作异常
     */
    private static void generateEnterpriseData(int count, CompanyNameGenerator.NameTemplate template,
                                             boolean includeOrgCode, String outputFilePath) throws IOException {
        Path outputPath = Paths.get(outputFilePath);
        
        try (BufferedWriter writer = Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8,
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {
            
            // 写入文件头
            if (includeOrgCode) {
                writer.write("企业名称,统一社会信用代码,组织机构代码");
            } else {
                writer.write("企业名称,统一社会信用代码");
            }
            writer.newLine();
            
            // 生成数据
            for (int i = 0; i < count; i++) {
                String companyName = CompanyNameGenerator.generateCompanyName(template);
                String creditCode = UnifiedSocialCreditCodeGenerator.generateRandomCode();
                
                if (includeOrgCode) {
                    String orgCode = UnifiedSocialCreditCodeGenerator.extractOrganizationCode(creditCode);
                    writer.write(companyName + "," + creditCode + "," + orgCode);
                } else {
                    writer.write(companyName + "," + creditCode);
                }
                writer.newLine();
                
                // 进度提示
                if ((i + 1) % 100 == 0 || i == count - 1) {
                    System.out.printf("已生成 %d/%d 条企业数据 (%.2f%%)\n",
                        i + 1, count, (double)(i + 1) / count * 100);
                }
            }
        }
    }
}