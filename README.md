# NIFA信用信息共享平台API测试框架

## 项目简介

中国互联网金融协会信用信息共享平台API测试框架，支持以下接口的自动化测试：

- 个人借贷共享查询接口（明文/密文）
- 企业共享查询接口
- 司法数据查询接口
- 数据上报接口
- 报送任务查询接口
- 查询量耗用情况查询接口

## 技术栈

- **Java 11+**
- **Maven 3.x**
- **TestNG 7.8** - 测试框架
- **REST-assured 5.3** - API测试库
- **Jersey 1.19** - HTTP客户端
- **BouncyCastle** - 国密加密算法

## 项目结构

```
src/
├── main/java/cn/org/nifa/bxcredit/
│   ├── config/
│   │   └── ConfigManager.java          # 配置管理器
│   ├── constants/
│   │   └── TestDataConstants.java      # 测试数据常量
│   ├── utils/
│   │   ├── CryptoUtils.java           # 加密工具类
│   │   ├── SignatureUtils.java        # 签名工具类
│   │   ├── HttpUtils.java             # HTTP工具类
│   │   └── RequestBuilder.java        # 请求构建器
│   └── [原有Demo类...]
├── main/resources/config/
│   ├── dev.properties                 # 开发环境配置
│   ├── test.properties                # 测试环境配置
│   └── prod.properties                # 生产环境配置
└── test/java/cn/org/nifa/bxcredit/api/
    ├── BaseApiTest.java               # 基础测试类
    └── InfoQueryApiTest.java          # 信息查询接口测试
```

## 快速开始

### 1. 环境要求

- JDK 11+
- Maven 3.6+

### 2. 配置环境

#### 方式一：环境变量配置（推荐）
```bash
export NIFA_ENV=dev
export NIFA_ORG_CODE=你的机构代码
export NIFA_API_KEY=你的API密钥
```

#### 方式二：系统属性配置
```bash
mvn test -Dnifa.env=test -DNIFA_ORG_CODE=你的机构代码 -DNIFA_API_KEY=你的API密钥
```

#### 方式三：修改配置文件
编辑 `src/main/resources/config/dev.properties`：
```properties
nifa.org.code=你的机构代码
nifa.api.key=你的API密钥
```

### 3. 运行测试

#### 运行所有测试
```bash
mvn clean test
```

#### 运行特定测试类
```bash
mvn test -Dtest=InfoQueryApiTest
```

#### 指定环境运行
```bash
mvn test -Dnifa.env=test
```

### 4. 查看测试报告

测试完成后，查看以下位置的报告：
- TestNG报告：`target/surefire-reports/index.html`
- 控制台输出：详细的请求/响应日志

## 配置说明

### 环境配置

支持三种环境：`dev`（开发）、`test`（测试）、`prod`（生产）

配置优先级：环境变量 > 系统属性 > 配置文件

### 主要配置项

| 配置项 | 说明 | 示例 |
|--------|------|------|
| nifa.org.code | 机构代码（9位） | C1B476EA0 |
| nifa.api.key | API密钥 | aTRPQZX5ZEWFpTix5j |
| nifa.endpoints.* | 各接口端点URL | https://... |
| nifa.crypto.* | 加密相关密钥 | da68acf0... |

## API测试示例

### 个人借贷查询接口测试

```java
@Test
public void testPersonalCreditQuery() {
    // 构建请求
    Map<String, String> body = RequestBuilder.buildInfoQueryBody(
        TestDataConstants.PersonalInfo.TEST_NAME,
        TestDataConstants.PersonalInfo.TEST_TYPE,
        TestDataConstants.PersonalInfo.TEST_REASON_B,
        TestDataConstants.PersonalInfo.TEST_ID_NUMBER
    );
    
    // 发送请求并验证
    given()
        .spec(requestSpec)
        .headers(headers)
        .body(body)
    .when()
        .post(ConfigManager.getInfoTestUri())
    .then()
        .statusCode(200)
        .body("msgCode", notNullValue());
}
```

## 代码规范

### 运行代码检查

```bash
# Checkstyle检查
mvn checkstyle:check

# SpotBugs检查
mvn spotbugs:check
```

### 编码规范

- 遵循Java命名约定
- 方法长度不超过150行
- 行长度不超过120字符
- 必要的JavaDoc注释
- 异常处理要具体化

## 开发指南

### 添加新的API测试

1. 在 `src/test/java/cn/org/nifa/bxcredit/api/` 下创建测试类
2. 继承 `BaseApiTest` 基础类
3. 使用 `@Test` 注解标记测试方法
4. 在 `testng.xml` 中添加测试类配置

### 添加新的工具方法

1. 根据职责选择合适的工具类（Crypto/Signature/Http/Request）
2. 添加静态方法
3. 编写对应的单元测试
4. 更新JavaDoc文档

## 故障排除

### 常见问题

1. **配置未生效**
   - 检查环境变量是否正确设置
   - 确认配置文件路径和格式
   - 查看控制台输出的配置加载信息

2. **SSL证书错误**
   - 确认网络连接正常
   - 检查防火墙和代理设置
   - 联系协会确认接口地址

3. **签名验证失败**
   - 检查机构代码和API密钥
   - 确认签名算法实现正确
   - 验证请求参数顺序

4. **测试数据问题**
   - 使用 `TestDataConstants` 中的测试数据
   - 避免使用真实个人信息
   - 确认测试环境数据权限

### 日志调试

启用详细日志：
```bash
mvn test -Dorg.slf4j.simpleLogger.defaultLogLevel=debug
```

## 更新日志

### v1.0.0 (重构版本)
- ✅ 实现多环境配置管理
- ✅ 引入TestNG和REST-assured测试框架
- ✅ 重构工具类，职责分离
- ✅ 添加代码规范检查
- ✅ 消除硬编码配置
- ✅ 完善异常处理机制

### v0.0.1 (原始版本)
- 基础Demo实现
- 支持6个主要API接口
- 国密加密算法集成

## 联系方式

如有问题，请联系开发团队或查阅协会官方文档。