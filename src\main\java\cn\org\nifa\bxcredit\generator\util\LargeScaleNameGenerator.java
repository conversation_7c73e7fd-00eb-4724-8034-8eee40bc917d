package cn.org.nifa.bxcredit.generator.util;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 大规模姓名生成工具类
 * 
 * 专门用于处理大数据量（如5000万条）的姓名生成任务，
 * 提供了多种优化策略和监控功能
 * 
 * <AUTHOR> Team
 */
public class LargeScaleNameGenerator {
    
    /**
     * 生成配置类
     */
    public static class GenerationConfig {
        private long totalCount;
        private NameGenerator.NameType nameType;
        private String outputFilePath;
        private int threadCount;
        private int batchSize;
        private int queueCapacity;
        private boolean enableProgressReport;
        private long progressReportInterval;
        
        public GenerationConfig(long totalCount, NameGenerator.NameType nameType, String outputFilePath) {
            this.totalCount = totalCount;
            this.nameType = nameType;
            this.outputFilePath = outputFilePath;
            this.threadCount = Runtime.getRuntime().availableProcessors();
            this.batchSize = 10000;
            this.queueCapacity = 50000;
            this.enableProgressReport = true;
            this.progressReportInterval = 100000;
        }
        
        // Getter 和 Setter 方法
        public long getTotalCount() { return totalCount; }
        public void setTotalCount(long totalCount) { this.totalCount = totalCount; }
        
        public NameGenerator.NameType getNameType() { return nameType; }
        public void setNameType(NameGenerator.NameType nameType) { this.nameType = nameType; }
        
        public String getOutputFilePath() { return outputFilePath; }
        public void setOutputFilePath(String outputFilePath) { this.outputFilePath = outputFilePath; }
        
        public int getThreadCount() { return threadCount; }
        public void setThreadCount(int threadCount) { this.threadCount = threadCount; }
        
        public int getBatchSize() { return batchSize; }
        public void setBatchSize(int batchSize) { this.batchSize = batchSize; }
        
        public int getQueueCapacity() { return queueCapacity; }
        public void setQueueCapacity(int queueCapacity) { this.queueCapacity = queueCapacity; }
        
        public boolean isEnableProgressReport() { return enableProgressReport; }
        public void setEnableProgressReport(boolean enableProgressReport) { this.enableProgressReport = enableProgressReport; }
        
        public long getProgressReportInterval() { return progressReportInterval; }
        public void setProgressReportInterval(long progressReportInterval) { this.progressReportInterval = progressReportInterval; }
    }
    
    /**
     * 生成统计信息类
     */
    public static class GenerationStatistics {
        private final long totalCount;
        private final long actualGenerated;
        private final long totalTimeMs;
        private final double averageSpeed;
        private final long estimatedFileSizeBytes;
        
        public GenerationStatistics(long totalCount, long actualGenerated, long totalTimeMs) {
            this.totalCount = totalCount;
            this.actualGenerated = actualGenerated;
            this.totalTimeMs = totalTimeMs;
            this.averageSpeed = totalTimeMs > 0 ? (double) actualGenerated * 1000 / totalTimeMs : 0;
            this.estimatedFileSizeBytes = actualGenerated * 4; // 估算每个姓名4字节
        }
        
        public long getTotalCount() { return totalCount; }
        public long getActualGenerated() { return actualGenerated; }
        public long getTotalTimeMs() { return totalTimeMs; }
        public double getAverageSpeed() { return averageSpeed; }
        public long getEstimatedFileSizeBytes() { return estimatedFileSizeBytes; }
        
        @Override
        public String toString() {
            return String.format(
                "生成统计:\n" +
                "  目标数量: %,d\n" +
                "  实际生成: %,d\n" +
                "  总耗时: %,d ms (%.2f 秒)\n" +
                "  平均速度: %,.0f 个/秒\n" +
                "  估算文件大小: %,.2f MB",
                totalCount, actualGenerated, totalTimeMs, totalTimeMs / 1000.0,
                averageSpeed, estimatedFileSizeBytes / 1024.0 / 1024.0
            );
        }
    }
    
    /**
     * 使用优化配置生成大规模姓名数据
     * 
     * @param config 生成配置
     * @return 生成统计信息
     * @throws IOException 文件操作异常
     * @throws InterruptedException 线程中断异常
     */
    public static GenerationStatistics generateLargeScale(GenerationConfig config) 
            throws IOException, InterruptedException {
        
        long startTime = System.currentTimeMillis();
        Path outputPath = Paths.get(config.getOutputFilePath());
        
        // 创建线程安全的队列
        BlockingQueue<String> nameQueue = new LinkedBlockingQueue<>(config.getQueueCapacity());
        AtomicLong generatedCount = new AtomicLong(0);
        AtomicLong writtenCount = new AtomicLong(0);
        
        // 创建线程池
        ExecutorService generatorPool = Executors.newFixedThreadPool(config.getThreadCount());
        ExecutorService writerPool = Executors.newSingleThreadExecutor();
        
        System.out.println("开始大规模姓名生成:");
        System.out.println("  目标数量: " + String.format("%,d", config.getTotalCount()));
        System.out.println("  姓名类型: " + config.getNameType());
        System.out.println("  输出文件: " + config.getOutputFilePath());
        System.out.println("  线程数量: " + config.getThreadCount());
        System.out.println("  批量大小: " + config.getBatchSize());
        System.out.println("  队列容量: " + config.getQueueCapacity());
        System.out.println();
        
        // 启动写入线程
        CompletableFuture<Void> writerFuture = CompletableFuture.runAsync(() -> {
            try (BufferedWriter writer = Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8,
                    StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {
                
                List<String> batch = new ArrayList<>(config.getBatchSize());
                
                while (writtenCount.get() < config.getTotalCount()) {
                    String name = nameQueue.poll(1, TimeUnit.SECONDS);
                    if (name != null) {
                        batch.add(name);
                        
                        if (batch.size() >= config.getBatchSize() || 
                            writtenCount.get() + batch.size() >= config.getTotalCount()) {
                            
                            // 批量写入
                            for (String n : batch) {
                                writer.write(n);
                                writer.newLine();
                            }
                            writer.flush();
                            
                            long written = writtenCount.addAndGet(batch.size());
                            batch.clear();
                            
                            // 进度报告
                            if (config.isEnableProgressReport() && 
                                written % config.getProgressReportInterval() == 0) {
                                reportProgress("写入", written, config.getTotalCount(), startTime);
                            }
                        }
                    }
                }
                
                // 写入剩余数据
                if (!batch.isEmpty()) {
                    for (String n : batch) {
                        writer.write(n);
                        writer.newLine();
                    }
                    writer.flush();
                    writtenCount.addAndGet(batch.size());
                }
                
            } catch (Exception e) {
                throw new RuntimeException("写入文件失败", e);
            }
        }, writerPool);
        
        // 启动生成线程
        long countPerThread = config.getTotalCount() / config.getThreadCount();
        long remainder = config.getTotalCount() % config.getThreadCount();
        
        List<CompletableFuture<Void>> generatorFutures = new ArrayList<>();
        
        for (int i = 0; i < config.getThreadCount(); i++) {
            long threadCount_local = countPerThread + (i < remainder ? 1 : 0);
            
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (long j = 0; j < threadCount_local; j++) {
                    try {
                        String name = generateNameByTypeThreadSafe(config.getNameType());
                        nameQueue.put(name);
                        
                        long generated = generatedCount.incrementAndGet();
                        if (config.isEnableProgressReport() && 
                            generated % config.getProgressReportInterval() == 0) {
                            reportProgress("生成", generated, config.getTotalCount(), startTime);
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("生成线程被中断", e);
                    }
                }
            }, generatorPool);
            
            generatorFutures.add(future);
        }
        
        // 等待所有生成线程完成
        CompletableFuture.allOf(generatorFutures.toArray(new CompletableFuture[0])).join();
        
        // 等待写入线程完成
        writerFuture.join();
        
        // 关闭线程池
        generatorPool.shutdown();
        writerPool.shutdown();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        GenerationStatistics stats = new GenerationStatistics(
            config.getTotalCount(), writtenCount.get(), totalTime);
        
        System.out.println("\n=== 大规模生成完成 ===");
        System.out.println(stats);
        
        return stats;
    }
    
    /**
     * 生成5000万条姓名数据的便捷方法
     * 
     * @param nameType 姓名类型
     * @param outputFilePath 输出文件路径
     * @return 生成统计信息
     * @throws IOException 文件操作异常
     * @throws InterruptedException 线程中断异常
     */
    public static GenerationStatistics generate50Million(NameGenerator.NameType nameType, String outputFilePath) 
            throws IOException, InterruptedException {
        
        GenerationConfig config = new GenerationConfig(50_000_000L, nameType, outputFilePath);
        config.setProgressReportInterval(1_000_000L); // 每100万条报告一次进度
        
        return generateLargeScale(config);
    }
    
    /**
     * 根据类型生成姓名（线程安全版本）
     */
    private static String generateNameByTypeThreadSafe(NameGenerator.NameType nameType) {
        switch (nameType) {
            case CHINESE_TWO_CHAR:
                return NameGenerator.generateChineseNameTwoCharThreadSafe();
            case CHINESE_THREE_CHAR:
                return NameGenerator.generateChineseNameThreeCharThreadSafe();
            case CHINESE_COMPOUND:
                return NameGenerator.generateChineseNameCompoundThreadSafe();
            case ENGLISH:
                return NameGenerator.generateEnglishNameThreadSafe();
            case CHINESE_RANDOM:
                return NameGenerator.generateRandomChineseNameThreadSafe();
            default:
                return NameGenerator.generateChineseNameTwoCharThreadSafe();
        }
    }
    
    /**
     * 报告进度
     */
    private static void reportProgress(String operation, long current, long total, long startTime) {
        long elapsed = System.currentTimeMillis() - startTime;
        double progress = (double) current / total * 100;
        double speed = elapsed > 0 ? (double) current * 1000 / elapsed : 0;
        
        System.out.printf("[%s] 进度: %,d/%,d (%.2f%%), 速度: %,.0f 个/秒, 耗时: %.1f秒\n",
            operation, current, total, progress, speed, elapsed / 1000.0);
    }
    
    /**
     * 估算生成时间
     * 
     * @param count 生成数量
     * @param nameType 姓名类型
     * @return 估算时间（秒）
     */
    public static double estimateGenerationTime(long count, NameGenerator.NameType nameType) {
        // 基于性能测试的经验值，每秒可生成约100万个姓名
        double baseSpeed = 1_000_000.0; // 个/秒
        
        // 根据姓名类型调整速度
        double typeMultiplier = 1.0;
        switch (nameType) {
            case CHINESE_TWO_CHAR:
                typeMultiplier = 1.0;
                break;
            case CHINESE_THREE_CHAR:
            case CHINESE_COMPOUND:
                typeMultiplier = 0.95;
                break;
            case ENGLISH:
                typeMultiplier = 0.9;
                break;
            case CHINESE_RANDOM:
                typeMultiplier = 0.85;
                break;
        }
        
        return count / (baseSpeed * typeMultiplier);
    }
    
    /**
     * 估算文件大小
     * 
     * @param count 生成数量
     * @param nameType 姓名类型
     * @return 估算文件大小（字节）
     */
    public static long estimateFileSize(long count, NameGenerator.NameType nameType) {
        int avgBytesPerName;
        
        switch (nameType) {
            case CHINESE_TWO_CHAR:
                avgBytesPerName = 7; // 2个中文字符 + 换行符 ≈ 7字节
                break;
            case CHINESE_THREE_CHAR:
                avgBytesPerName = 10; // 3个中文字符 + 换行符 ≈ 10字节
                break;
            case CHINESE_COMPOUND:
                avgBytesPerName = 13; // 4个中文字符 + 换行符 ≈ 13字节
                break;
            case ENGLISH:
                avgBytesPerName = 15; // 平均英文姓名长度 + 换行符 ≈ 15字节
                break;
            case CHINESE_RANDOM:
                avgBytesPerName = 9; // 平均中文姓名长度 + 换行符 ≈ 9字节
                break;
            default:
                avgBytesPerName = 8;
        }
        
        return count * avgBytesPerName;
    }
}
