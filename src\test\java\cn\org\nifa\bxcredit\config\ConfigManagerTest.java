package cn.org.nifa.bxcredit.config;

import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * 配置管理器测试
 * 
 * <AUTHOR> Team
 */
public class ConfigManagerTest {
    
    @Test
    public void testGetOrgCode() {
        String orgCode = ConfigManager.getOrgCode();
        Assert.assertNotNull(orgCode, "Organization code should not be null");
        Assert.assertFalse(orgCode.trim().isEmpty(), "Organization code should not be empty");
    }
    
    @Test
    public void testGetApiKey() {
        String apiKey = ConfigManager.getApiKey();
        Assert.assertNotNull(apiKey, "API key should not be null");
        Assert.assertFalse(apiKey.trim().isEmpty(), "API key should not be empty");
    }
    
    @Test
    public void testGetEndpoints() {
        Assert.assertNotNull(ConfigManager.getInfoTestUri(), "Info test URI should not be null");
        Assert.assertNotNull(ConfigManager.getDataUri(), "Data URI should not be null");
        Assert.assertNotNull(ConfigManager.getTaskUri(), "Task URI should not be null");
    }
    
    @Test
    public void testGetCryptoKeys() {
        Assert.assertNotNull(ConfigManager.getPubXKey(), "Public X key should not be null");
        Assert.assertNotNull(ConfigManager.getPubYKey(), "Public Y key should not be null");
        Assert.assertNotNull(ConfigManager.getPrivateKey(), "Private key should not be null");
    }
}