package cn.org.nifa.bxcredit.generator.util;

import java.util.Random;

/**
 * 业务号生成器
 * 
 * <AUTHOR> Team
 */
public class BusinessCodeGenerator {
    
    private static final Random RANDOM = new Random();
    
    /**
     * 生成业务号
     * 格式：BN + 30位随机数字
     * 
     * @return 业务号
     */
    public static String generateBusinessNumber() {
        StringBuilder businessNumber = new StringBuilder("BN");
        
        // 生成30位随机数字
        for (int i = 0; i < 30; i++) {
            businessNumber.append(RANDOM.nextInt(10));
        }
        
        return businessNumber.toString();
    }
    
    /**
     * 生成组织机构代码（9位）
     * 
     * @return 组织机构代码
     */
    public static String generateOrgCode() {
        StringBuilder orgCode = new StringBuilder();
        
        // 生成8位数字或字母
        for (int i = 0; i < 8; i++) {
            if (RANDOM.nextBoolean()) {
                orgCode.append(RANDOM.nextInt(10));
            } else {
                // 避免使用I、O、Z字符
                char c;
                do {
                    c = (char) ('A' + RANDOM.nextInt(26));
                } while (c == 'I' || c == 'O' || c == 'Z');
                orgCode.append(c);
            }
        }
        
        // 计算校验码
        orgCode.append(calculateOrgCodeCheckDigit(orgCode.toString()));
        
        return orgCode.toString();
    }
    
    /**
     * 计算组织机构代码校验位
     * 
     * @param code8 前8位代码
     * @return 校验位
     */
    private static char calculateOrgCodeCheckDigit(String code8) {
        if (code8 == null || code8.length() != 8) {
            throw new IllegalArgumentException("代码必须为8位字符");
        }
        
        // 加权因子
        int[] weights = {3, 7, 9, 10, 5, 8, 4, 2};
        
        // 校验码字符集
        String checkCodeChars = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ";
        
        int sum = 0;
        for (int i = 0; i < 8; i++) {
            char c = code8.charAt(i);
            int value;
            
            if (c >= '0' && c <= '9') {
                value = c - '0';
            } else if (c >= 'A' && c <= 'Z') {
                // 跳过I、O、Z
                if (c == 'I') value = 8;
                else if (c == 'O') value = 9;
                else if (c > 'O') value = c - 'A' - 2;
                else value = c - 'A' + 10;
            } else {
                throw new IllegalArgumentException("无效字符: " + c);
            }
            
            sum += value * weights[i];
        }
        
        int remainder = sum % 11;
        int checkValue = (11 - remainder) % 11;
        
        return checkValue == 10 ? 'X' : checkCodeChars.charAt(checkValue);
    }
    
    /**
     * 生成社会信用代码（18位）
     * 
     * @return 社会信用代码
     */
    public static String generateSocialCreditCode() {
        StringBuilder creditCode = new StringBuilder();
        
        // 第1位：登记管理部门代码
        String[] deptCodes = {"1", "5", "9", "Y"};
        creditCode.append(deptCodes[RANDOM.nextInt(deptCodes.length)]);
        
        // 第2位：机构类别代码
        String[] typeCodes = {"1", "2", "3", "9"};
        creditCode.append(typeCodes[RANDOM.nextInt(typeCodes.length)]);
        
        // 第3-8位：登记管理机关行政区划码
        String[] areaCodes = {"110000", "310000", "440000", "500000"};
        creditCode.append(areaCodes[RANDOM.nextInt(areaCodes.length)]);
        
        // 第9-17位：主体标识码
        for (int i = 0; i < 9; i++) {
            if (RANDOM.nextBoolean()) {
                creditCode.append(RANDOM.nextInt(10));
            } else {
                // 避免使用I、O、Z字符
                char c;
                do {
                    c = (char) ('A' + RANDOM.nextInt(26));
                } while (c == 'I' || c == 'O' || c == 'Z');
                creditCode.append(c);
            }
        }
        
        // 第18位：校验码
        creditCode.append(calculateSocialCreditCheckCode(creditCode.toString()));
        
        return creditCode.toString();
    }
    
    /**
     * 计算社会信用代码校验码
     * 基于GB 32100-2015标准实现
     * 
     * @param code17 前17位代码
     * @return 校验码
     */
    private static char calculateSocialCreditCheckCode(String code17) {
        if (code17 == null || code17.length() != 17) {
            throw new IllegalArgumentException("代码必须为17位字符");
        }
        
        // 加权因子表
        int[] weights = {1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 9, 27, 19, 26, 16};
        
        // 校验码字符集
        String checkCodeChars = "0123456789ABCDEFGHJKLMNPQRTUWXY";
        
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            char c = code17.charAt(i);
            int value;
            
            if (c >= '0' && c <= '9') {
                value = c - '0';
            } else if (c >= 'A' && c <= 'Z') {
                // 跳过I、O、Z
                if (c == 'I') value = 8;
                else if (c == 'O') value = 9;
                else if (c > 'O') value = c - 'A' - 2;
                else value = c - 'A' + 10;
            } else {
                throw new IllegalArgumentException("无效字符: " + c);
            }
            
            sum += value * weights[i];
        }
        
        int remainder = sum % 31;
        int checkValue = (31 - remainder) % 31;
        
        return checkCodeChars.charAt(checkValue);
    }
    
    /**
     * 生成无效的业务号（用于反向测试）
     * 
     * @param violationType 违反类型
     * @return 无效业务号
     */
    public static String generateInvalidBusinessNumber(String violationType) {
        switch (violationType) {
            case "NO_PREFIX":
                return generateRandomDigits(32); // 没有BN前缀
            case "WRONG_PREFIX":
                return "AB" + generateRandomDigits(30); // 错误前缀
            case "SHORT_LENGTH":
                return "BN" + generateRandomDigits(29); // 长度不足
            case "LONG_LENGTH":
                return "BN" + generateRandomDigits(31); // 长度超出
            case "NON_DIGIT":
                return "BN" + generateRandomDigits(29) + "A"; // 包含非数字
            default:
                return generateBusinessNumber();
        }
    }
    
    /**
     * 生成指定长度的随机数字字符串
     * 
     * @param length 长度
     * @return 数字字符串
     */
    private static String generateRandomDigits(int length) {
        StringBuilder digits = new StringBuilder();
        for (int i = 0; i < length; i++) {
            digits.append(RANDOM.nextInt(10));
        }
        return digits.toString();
    }
}