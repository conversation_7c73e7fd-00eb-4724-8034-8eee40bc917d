# 生产环境配置
nifa.org.code=${NIFA_ORG_CODE:}
nifa.api.key=${NIFA_API_KEY:}

# API端点配置 - 生产环境
nifa.endpoints.info.test=https://credit.nifa.org.cn/NifaCreditServer/context/info/json/upload
nifa.endpoints.info.md5=https://credit.nifa.org.cn/NifaCreditServer/context/info/json/uploadnew
nifa.endpoints.data=https://credit.nifa.org.cn/NifaCreditServer/context/data/json/asynlist
nifa.endpoints.task=https://credit.nifa.org.cn/NifaCreditServer/context/task/json/upload
nifa.endpoints.query.count=https://credit.nifa.org.cn/NifaCreditServer/context/info/json/querycontral
nifa.endpoints.highcourt=https://credit.nifa.org.cn/NifaCreditServer/nifa/sxzbr/json/people

# 加密密钥配置
nifa.crypto.pub.x=da68acf0ae676725fbd70894dfe7aaac5af008009bc30c13daf4e691f575d12a
nifa.crypto.pub.y=76d4a0f90065ad86221287a74bf99862e92124282dba02b94782ff50f8ea6701
nifa.crypto.private=143c18f085e49697b7918d1a03a90c49bbe8ca1b741511bbac9e81cceb7563d5