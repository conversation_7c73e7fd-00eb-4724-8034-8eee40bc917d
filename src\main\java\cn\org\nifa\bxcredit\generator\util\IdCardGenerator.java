package cn.org.nifa.bxcredit.generator.util;

import java.util.Random;

/**
 * 身份证号生成器
 * 
 * <AUTHOR> Team
 */
public class IdCardGenerator {
    
    private static final Random RANDOM = new Random();
    
    // 地区代码（前6位）
    private static final String[] AREA_CODES = {
        "110101", "110102", "110105", "110106", "110107", "110108", "110109",
        "310101", "310104", "310105", "310106", "310107", "310109", "310110",
        "440101", "440103", "440104", "440105", "440106", "440111", "440112"
    };
    
    // 校验码权重
    private static final int[] WEIGHTS = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
    
    // 校验码对应表
    private static final char[] CHECK_CODES = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
    
    /**
     * 生成随机身份证号
     * 
     * @return 18位身份证号
     */
    public static String generateRandomIdCard() {
        StringBuilder idCard = new StringBuilder();
        
        // 地区代码（6位）
        idCard.append(AREA_CODES[RANDOM.nextInt(AREA_CODES.length)]);
        
        // 出生日期（8位）
        int year = 1970 + RANDOM.nextInt(40); // 1970-2009年
        int month = 1 + RANDOM.nextInt(12);
        int day = 1 + RANDOM.nextInt(28); // 简化处理，避免月份天数问题
        
        idCard.append(String.format("%04d%02d%02d", year, month, day));
        
        // 顺序码（3位）
        idCard.append(String.format("%03d", RANDOM.nextInt(1000)));
        
        // 计算校验码
        char checkCode = calculateCheckCode(idCard.toString());
        idCard.append(checkCode);
        
        return idCard.toString();
    }
    
    /**
     * 计算身份证校验码
     * 
     * @param idCard17 前17位身份证号
     * @return 校验码
     */
    private static char calculateCheckCode(String idCard17) {
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += Character.getNumericValue(idCard17.charAt(i)) * WEIGHTS[i];
        }
        return CHECK_CODES[sum % 11];
    }
    
    /**
     * 校验身份证号
     * 
     * @param idCard 身份证号
     * @return 是否有效
     */
    public static boolean validateIdCard(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return false;
        }
        
        // 检查前17位是否为数字
        for (int i = 0; i < 17; i++) {
            if (!Character.isDigit(idCard.charAt(i))) {
                return false;
            }
        }
        
        // 检查最后一位校验码
        char lastChar = idCard.charAt(17);
        if (!Character.isDigit(lastChar) && lastChar != 'X') {
            return false;
        }
        
        // 验证校验码
        char expectedCheckCode = calculateCheckCode(idCard.substring(0, 17));
        return expectedCheckCode == lastChar;
    }
    
    /**
     * 生成无效的身份证号（用于反向测试）
     * 
     * @param violationType 违反类型
     * @return 无效身份证号
     */
    public static String generateInvalidIdCard(String violationType) {
        switch (violationType) {
            case "LENGTH":
                return "12345678901234567"; // 17位
            case "LAST_CHAR":
                return "11010119800101001x"; // 最后一位小写x
            case "NON_DIGIT":
                return "1101011980010100A1"; // 包含非数字字符
            case "INVALID_CHECK":
                return "110101198001010019"; // 校验码错误
            default:
                return generateRandomIdCard();
        }
    }
}