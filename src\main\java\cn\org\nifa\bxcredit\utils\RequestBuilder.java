package cn.org.nifa.bxcredit.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 请求构建器
 * 
 * <AUTHOR> Team
 */
public class RequestBuilder {
    
    /**
     * 构建通用请求头
     * 
     * @param orgCode 机构代码
     * @param randomCode 随机码
     * @param signature 签名
     * @return 请求头Map
     */
    public static Map<String, String> buildCommonHeaders(String orgCode, String randomCode, String signature) {
        Map<String, String> headers = new HashMap<>();
        headers.put("sbankcode", orgCode);
        headers.put("scode", randomCode);
        headers.put("sign", signature);
        return headers;
    }
    
    /**
     * 构建数据上报请求头
     * 
     * @param orgCode 机构代码
     * @param dataCode 数据代码
     * @param dataHash 数据哈希
     * @param signature 签名
     * @return 请求头Map
     */
    public static Map<String, String> buildDataSubmissionHeaders(String orgCode, String dataCode, 
            String dataHash, String signature) {
        Map<String, String> headers = new HashMap<>();
        headers.put("sbankcode", orgCode);
        headers.put("sdatacode", dataCode);
        headers.put("scode", dataHash);
        headers.put("sign", signature);
        return headers;
    }
    
    /**
     * 构建任务查询请求头
     * 
     * @param orgCode 机构代码
     * @param randomCode 随机码
     * @param dataCode 数据代码
     * @param signature 签名
     * @return 请求头Map
     */
    public static Map<String, String> buildTaskQueryHeaders(String orgCode, String randomCode, 
            String dataCode, String signature) {
        Map<String, String> headers = new HashMap<>();
        headers.put("sbankcode", orgCode);
        headers.put("scode", randomCode);
        headers.put("sign", signature);
        headers.put("sdatacode", dataCode);
        return headers;
    }
    
    /**
     * 构建信息查询请求体
     * 
     * @param name 姓名
     * @param type 证件类型
     * @param reason 查询原因
     * @param idNumber 证件号码
     * @return 请求体Map
     */
    public static Map<String, String> buildInfoQueryBody(String name, String type, String reason, String idNumber) {
        Map<String, String> body = new HashMap<>();
        body.put("sname", name);
        body.put("stype", type);
        body.put("sreason", reason);
        body.put("sno", idNumber);
        return body;
    }
    
    /**
     * 构建MD5查询请求体
     * 
     * @param md5Value MD5值
     * @param reason 查询原因
     * @return 请求体Map
     */
    public static Map<String, String> buildMd5QueryBody(String md5Value, String reason) {
        Map<String, String> body = new HashMap<>();
        body.put("md5", md5Value);
        body.put("sreason", reason);
        return body;
    }
    
    /**
     * 构建查询量统计请求体
     * 
     * @param date 查询日期
     * @param type 查询类型
     * @return 请求体Map
     */
    public static Map<String, String> buildQueryCountBody(String date, String type) {
        Map<String, String> body = new HashMap<>();
        body.put("sdate", date);
        body.put("stype", type);
        return body;
    }
    
    /**
     * 构建数据上报请求体
     * 
     * @param encryptedData 加密数据
     * @return 请求体Map
     */
    public static Map<String, String> buildDataSubmissionBody(String encryptedData) {
        Map<String, String> body = new HashMap<>();
        body.put("sdata", encryptedData);
        return body;
    }
    

}