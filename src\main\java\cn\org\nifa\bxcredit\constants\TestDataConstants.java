package cn.org.nifa.bxcredit.constants;

/**
 * 测试数据常量
 * 集中管理测试数据，避免硬编码
 */
public class TestDataConstants {
    
    /**
     * 个人信息测试数据
     */
    public static class PersonalInfo {
        public static final String TEST_NAME = "许文";
        public static final String TEST_TYPE = "0";
        public static final String TEST_REASON_A = "a";
        public static final String TEST_REASON_B = "b";
        public static final String TEST_ID_NUMBER = "640221198509101292";
    }
    
    /**
     * 企业信息测试数据
     */
    public static class EnterpriseInfo {
        public static final String TEST_NAME = "测试企业";
        public static final String TEST_CREDIT_CODE = "91110000100000000A";
        public static final String TEST_REASON = "a";
    }
    
    /**
     * 查询原因代码
     */
    public static class QueryReason {
        public static final String LOAN_APPLICATION = "a";
        public static final String CREDIT_MANAGEMENT = "b";
        public static final String GUARANTEE = "c";
        public static final String JUDICIAL_ASSISTANCE = "d";
    }
    
    /**
     * 查询参数
     */
    public static class QueryParams {
        public static final String TEST_DATE = "20240101";
        public static final String QUERY_TYPE_PERSONAL = "0";
        public static final String QUERY_TYPE_ENTERPRISE = "1";
    }
    
    /**
     * 测试文件名
     */
    public static class FileNames {
        public static final String TEST_TASK_ID = "2RR3PEGT2202309061715127112";
    }
}