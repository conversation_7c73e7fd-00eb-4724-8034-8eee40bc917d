package cn.org.nifa.bxcredit.config;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 配置管理器 - 支持多环境配置
 * 
 * <AUTHOR> Team
 */
public class ConfigManager {
    
    private static final String DEFAULT_ENV = "test";
    private static Properties config;
    private static String currentEnvironment;
    
    static {
        loadConfig();
    }
    
    private static void loadConfig() {
        String env = System.getProperty("nifa.env", System.getenv("NIFA_ENV"));
        if (env == null || env.trim().isEmpty()) {
            env = DEFAULT_ENV;
        }
        currentEnvironment = env;
        
        config = new Properties();
        String configFile = String.format("config/%s.properties", env);
        
        try (InputStream is = ConfigManager.class.getClassLoader().getResourceAsStream(configFile)) {
            if (is != null) {
                config.load(is);
                System.out.println("Loaded config from: " + configFile);
            } else {
                System.err.println("Config file not found: " + configFile);
                loadDefaultConfig();
            }
        } catch (IOException e) {
            System.err.println("Failed to load config: " + e.getMessage());
            loadDefaultConfig();
        }
        
        // 环境变量覆盖
        overrideWithEnvVars();
    }
    
    private static void loadDefaultConfig() {
        // 默认配置
        config.setProperty("nifa.org.code", "097967874");
        config.setProperty("nifa.api.key", "mNak7HBrKFFXMn72w5");
        config.setProperty("nifa.endpoints.info.test", "http://172.18.13.78:81//NewNifaServer/context/info/json/upload");
        config.setProperty("nifa.endpoints.info.md5", "http://172.18.13.78:81//NifaCreditServer/context/info/json/uploadnew");
        config.setProperty("nifa.endpoints.data", "http://172.18.13.78:81//NewNifaServer1/context/data/json/asynlist");
        config.setProperty("nifa.endpoints.task", "http://172.18.13.78:81//NewNifaServer1/context/task/json/upload");
        config.setProperty("nifa.endpoints.query.count", "http://172.18.13.78:81//NewNifaServer/context/info/json/querycontralNew");
        config.setProperty("nifa.endpoints.highcourt", "http://172.18.13.78:81//NifaCreditServer/nifa/sxzbr/json/people");
        
        // 加载密钥配置
        loadCryptoConfig();
    }
    
    /**
     * 加载密钥配置
     */
    private static void loadCryptoConfig() {
        String cryptoConfigFile = "keys/crypto.properties";
        
        try (InputStream is = ConfigManager.class.getClassLoader().getResourceAsStream(cryptoConfigFile)) {
            if (is != null) {
                Properties cryptoProps = new Properties();
                cryptoProps.load(is);
                
                // 加载密钥
                config.setProperty("nifa.crypto.pub.x", cryptoProps.getProperty("nifa.crypto.default.pub.x"));
                config.setProperty("nifa.crypto.pub.y", cryptoProps.getProperty("nifa.crypto.default.pub.y"));
                config.setProperty("nifa.crypto.private", cryptoProps.getProperty("nifa.crypto.default.private"));
                
                System.out.println("Loaded crypto config from: " + cryptoConfigFile);
            } else {
                System.err.println("Crypto config file not found: " + cryptoConfigFile);
                // 使用硬编码的默认值
                config.setProperty("nifa.crypto.pub.x", "da68acf0ae676725fbd70894dfe7aaac5af008009bc30c13daf4e691f575d12a");
                config.setProperty("nifa.crypto.pub.y", "76d4a0f90065ad86221287a74bf99862e92124282dba02b94782ff50f8ea6701");
                config.setProperty("nifa.crypto.private", "143c18f085e49697b7918d1a03a90c49bbe8ca1b741511bbac9e81cceb7563d5");
            }
        } catch (IOException e) {
            System.err.println("Failed to load crypto config: " + e.getMessage());
            // 使用硬编码的默认值
            config.setProperty("nifa.crypto.pub.x", "da68acf0ae676725fbd70894dfe7aaac5af008009bc30c13daf4e691f575d12a");
            config.setProperty("nifa.crypto.pub.y", "76d4a0f90065ad86221287a74bf99862e92124282dba02b94782ff50f8ea6701");
            config.setProperty("nifa.crypto.private", "143c18f085e49697b7918d1a03a90c49bbe8ca1b741511bbac9e81cceb7563d5");
        }
    }
    
    private static void overrideWithEnvVars() {
        // 系统属性优先级高于配置文件
        String orgCodeProp = System.getProperty("NIFA_ORG_CODE");
        if (orgCodeProp != null && !orgCodeProp.trim().isEmpty()) {
            config.setProperty("nifa.org.code", orgCodeProp);
        }
        
        String apiKeyProp = System.getProperty("NIFA_API_KEY");
        if (apiKeyProp != null && !apiKeyProp.trim().isEmpty()) {
            config.setProperty("nifa.api.key", apiKeyProp);
        }
        
        // 环境变量优先级最高
        String orgCode = System.getenv("NIFA_ORG_CODE");
        if (orgCode != null && !orgCode.trim().isEmpty()) {
            config.setProperty("nifa.org.code", orgCode);
        }
        
        String apiKey = System.getenv("NIFA_API_KEY");
        if (apiKey != null && !apiKey.trim().isEmpty()) {
            config.setProperty("nifa.api.key", apiKey);
        }
    }
    
    public static String getOrgCode() {
        return config.getProperty("nifa.org.code");
    }
    
    public static String getApiKey() {
        return config.getProperty("nifa.api.key");
    }
    
    public static String getInfoTestUri() {
        return config.getProperty("nifa.endpoints.info.test");
    }
    
    public static String getInfoMd5Uri() {
        return config.getProperty("nifa.endpoints.info.md5");
    }
    
    public static String getDataUri() {
        return config.getProperty("nifa.endpoints.data");
    }
    
    public static String getTaskUri() {
        return config.getProperty("nifa.endpoints.task");
    }
    
    public static String getQueryCountUri() {
        return config.getProperty("nifa.endpoints.query.count");
    }
    
    public static String getHighcourtUri() {
        return config.getProperty("nifa.endpoints.highcourt");
    }
    
    public static String getPubXKey() {
        return config.getProperty("nifa.crypto.pub.x");
    }
    
    public static String getPubYKey() {
        return config.getProperty("nifa.crypto.pub.y");
    }
    
    public static String getPrivateKey() {
        return config.getProperty("nifa.crypto.private");
    }
    
    /**
     * 获取当前环境
     */
    public static String getCurrentEnv() {
        return currentEnvironment;
    }
    
    /**
     * 重新加载配置
     */
    public static void reload() {
        loadConfig();
    }
}