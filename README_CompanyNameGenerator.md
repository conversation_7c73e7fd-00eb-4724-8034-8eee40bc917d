# 公司名称生成工具类使用说明

## 概述

本项目实现了一个高效、灵活的公司名称生成工具类，基于组件化的组合生成与实时唯一性校验，支持生成多种格式的公司名称，并针对大规模数据生成（如5000万条）进行了优化。

## 功能特性

### 核心功能
- **多模板生成**：支持8种不同的公司名称生成模板
- **组件化组合**：基于前缀词、核心词、后缀词、行业词、地名词的灵活组合
- **格式校验**：内置公司名称格式校验功能
- **长度控制**：支持指定长度范围的公司名称生成
- **线程安全**：提供线程安全的生成方法，适用于多线程环境

### 高级功能
- **批量生成到文件**：支持将生成的公司名称批量写入文件
- **多线程批量生成**：使用多线程技术提高大数据量生成效率
- **唯一性管理**：确保生成的公司名称不重复
- **性能监控**：提供生成统计信息和进度报告
- **大规模数据处理**：专门优化的5000万条数据生成功能

### 集成功能
- **ValidationUtils集成**：完全集成到现有的ValidationUtils工具类中
- **协同工作**：与姓名生成器、统一社会信用代码生成器协同工作
- **统一校验接口**：提供一致的校验方法接口

## 生成模板

### 支持的8种生成模板

1. **PREFIX_CORE_SUFFIX**：前缀+核心词+后缀
   - 示例：未来科技有限公司、新华信息集团

2. **CORE_CORE_SUFFIX**：核心词+核心词+后缀
   - 示例：创新发展集团、智能数据公司

3. **CORE_DIGIT_SUFFIX**：核心词+数字+后缀
   - 示例：科技888有限公司、创新123集团

4. **LOCATION_CORE_SUFFIX**：地名+核心词+后缀
   - 示例：上海创新信息咨询有限公司、北京科技发展集团

5. **RANDOM**：随机选择上述模板之一
   - 示例：各种格式的随机组合

## 数据统计

当前词库包含：
- **前缀词**：113个
- **核心词**：162个
- **后缀词**：58个
- **地名词**：55个
- **行业词**：40个

**理论组合数**：
- PREFIX_CORE_SUFFIX：约106万种组合
- CORE_CORE_SUFFIX：约152万种组合
- CORE_DIGIT_SUFFIX：约9400万种组合
- LOCATION_CORE_SUFFIX：约51万种组合
- 总计：超过1亿种组合

## 快速开始

### 1. 基础公司名称生成

```java
// 生成随机公司名称
String companyName = CompanyNameGenerator.generateRandomCompanyName();
System.out.println(companyName); // 输出：未来科技有限公司

// 线程安全生成（适用于多线程环境）
String threadSafeCompanyName = CompanyNameGenerator.generateRandomCompanyNameThreadSafe();

// 按指定模板生成
String prefixCoreCompany = CompanyNameGenerator.generateCompanyNameByTemplate(
    CompanyNameGenerator.NameTemplate.PREFIX_CORE_SUFFIX);
```

### 2. 指定长度范围生成

```java
// 生成长度在6-15个字符之间的公司名称
String companyName = CompanyNameGenerator.generateCompanyNameWithLength(6, 15);
System.out.println(companyName + " (长度: " + companyName.length() + ")");
```

### 3. 公司名称校验

```java
// 校验公司名称格式
boolean isValid = CompanyNameGenerator.validateCompanyName("未来科技有限公司");
System.out.println(isValid); // 输出：true

// 使用ValidationUtils校验
boolean isValidByUtils = ValidationUtils.validateCompanyName("未来科技有限公司");
String validationResult = ValidationUtils.validateCompanyNameComprehensive("未来科技有限公司");
```

### 4. 批量生成到文件

```java
// 单线程批量生成
CompanyNameGenerator.BatchGenerationResult result = 
    CompanyNameGenerator.generateCompanyNamesToFile(
        10000L,                                          // 生成数量
        CompanyNameGenerator.NameTemplate.RANDOM,        // 生成模板
        "company_names.txt"                              // 输出文件
    );

System.out.println(result);
```

### 5. 多线程批量生成

```java
// 多线程批量生成
CompanyNameGenerator.BatchGenerationResult result = 
    CompanyNameGenerator.generateCompanyNamesToFileMultiThreaded(
        1000000L,                                        // 生成数量
        CompanyNameGenerator.NameTemplate.RANDOM,        // 生成模板
        "company_names_mt.txt",                          // 输出文件
        8                                                // 线程数量
    );

System.out.println(result);
```

### 6. 大规模数据生成（5000万条）

```java
// 生成5000万条公司名称
CompanyNameGenerator.BatchGenerationResult result = 
    CompanyNameGenerator.generateCompanyNamesToFileMultiThreaded(
        50_000_000L,                                     // 5000万条
        CompanyNameGenerator.NameTemplate.RANDOM,        // 随机模板
        "large_scale_company_names.txt",                 // 输出文件
        Runtime.getRuntime().availableProcessors()      // 使用所有CPU核心
    );
```

## 与其他工具类协同使用

### 生成完整企业信息

```java
// 生成完整的企业信息
String companyName = CompanyNameGenerator.generateRandomCompanyName();
String legalRepresentative = NameGenerator.generateChineseNameThreeChar();
String creditCode = UnifiedSocialCreditCodeGenerator.generateCodeWithPrefix("9", "1", "110000");
String orgCode = UnifiedSocialCreditCodeGenerator.extractOrganizationCode(creditCode);

System.out.println("公司名称: " + companyName);
System.out.println("法人代表: " + legalRepresentative);
System.out.println("统一社会信用代码: " + creditCode);
System.out.println("组织机构代码: " + orgCode);

// 校验生成的数据
System.out.println("公司名称校验: " + CompanyNameGenerator.validateCompanyName(companyName));
System.out.println("信用代码校验: " + UnifiedSocialCreditCodeGenerator.validateCode(creditCode));
```

## ValidationUtils集成

### 公司名称校验功能

```java
// 基础校验
boolean isValid = ValidationUtils.validateCompanyName(companyName);

// 长度校验
boolean lengthValid = ValidationUtils.validateCompanyNameLength(companyName, 4, 50);

// 后缀校验
boolean suffixValid = ValidationUtils.validateCompanyNameSuffix(companyName);

// 非法字符检查
boolean hasIllegalChars = ValidationUtils.containsIllegalCharacters(companyName);

// 综合校验
String comprehensiveResult = ValidationUtils.validateCompanyNameComprehensive(companyName);
```

## 性能特性

### 生成速度
- **单线程**：约50万个/秒
- **多线程**：根据CPU核心数可达数百万个/秒
- **5000万条数据**：预计耗时约60-120秒（取决于硬件配置）

### 校验速度
- **格式校验**：约100万次/秒
- **综合校验**：约80万次/秒
- **内存使用**：稳定，无内存泄漏

### 文件输出
- **格式**：纯文本格式，UTF-8编码
- **批量写入**：使用缓冲写入提高I/O效率
- **唯一性保证**：使用ConcurrentHashMap确保线程安全的唯一性检查

## 运行演示程序

```bash
# 编译项目
mvn compile

# 运行演示程序
java -cp target/classes cn.org.nifa.bxcredit.generator.demo.CompanyNameGeneratorDemo
```

演示程序提供交互式菜单，包括：
1. 基础公司名称生成演示
2. 按模板生成公司名称演示
3. 公司名称校验演示
4. 指定长度范围生成演示
5. 批量生成到文件
6. 多线程批量生成到文件
7. 性能测试
8. 大规模数据生成（5000万条）
9. 与其他生成器协同演示
10. ValidationUtils集成演示

## 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=CompanyNameGeneratorTest
mvn test -Dtest=ValidationUtilsTest
```

## 扩展词库

如需扩展公司名称词库，可以编辑 `src/main/resources/data/` 目录下的文本文件：

1. **company_prefix_words.txt** - 添加更多前缀词
2. **company_core_words.txt** - 添加更多核心词
3. **company_suffix_words.txt** - 添加更多后缀词
4. **company_location_words.txt** - 添加更多地名词
5. **company_industry_words.txt** - 添加更多行业词

每个文件每行一个词条，程序会在启动时自动加载。

## 注意事项

1. **文件路径**：确保输出文件路径有写入权限
2. **磁盘空间**：大规模生成前请确保有足够的磁盘空间（5000万条约1GB）
3. **内存配置**：对于超大规模生成，建议设置JVM参数：`-Xmx4g -Xms2g`
4. **线程数量**：建议线程数不超过CPU核心数的2倍
5. **唯一性保证**：大规模生成时会自动处理重复名称，确保唯一性

## 错误处理

工具类提供完善的错误处理机制：
- **参数校验**：对输入参数进行严格校验
- **异常处理**：提供详细的异常信息
- **资源管理**：自动管理文件资源，确保正确关闭
- **进度监控**：实时报告生成进度和错误信息

## 技术实现

- **Java 11+** 兼容
- **线程安全**：使用ThreadLocalRandom和ConcurrentHashMap
- **高性能I/O**：使用BufferedWriter和批量写入
- **内存优化**：高效的数据结构和算法
- **并发处理**：使用CompletableFuture和ExecutorService
- **组件化设计**：基于词库组件的灵活组合生成

## 许可证

本项目遵循项目整体许可证。

---

如有问题或建议，请联系 NIFA Team。
