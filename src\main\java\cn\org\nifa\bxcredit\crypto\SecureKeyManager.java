package cn.org.nifa.bxcredit.crypto;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.Properties;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * 安全密钥管理器
 * 支持密钥的安全存储和加载
 * 
 * <AUTHOR> Team
 */
public class SecureKeyManager {
    
    private static final String DEFAULT_KEYSTORE_PATH = "keys/nifa-keystore.jks";
    private static final String DEFAULT_KEYSTORE_PASSWORD = "changeit";
    private static final String DEFAULT_KEY_ALIAS_PREFIX = "nifa-key-";
    
    private static final String PROPERTIES_PATH = "keys/keys.properties";
    
    private String keystorePath;
    private char[] keystorePassword;
    private KeyStore keyStore;
    private Properties keyProperties;
    
    /**
     * 创建密钥管理器
     */
    public SecureKeyManager() {
        this(DEFAULT_KEYSTORE_PATH, DEFAULT_KEYSTORE_PASSWORD.toCharArray());
    }
    
    /**
     * 创建密钥管理器
     * 
     * @param keystorePath 密钥库路径
     * @param keystorePassword 密钥库密码
     */
    public SecureKeyManager(String keystorePath, char[] keystorePassword) {
        this.keystorePath = keystorePath;
        this.keystorePassword = keystorePassword;
        initKeyStore();
        loadKeyProperties();
    }
    
    /**
     * 初始化密钥库
     */
    private void initKeyStore() {
        try {
            keyStore = KeyStore.getInstance("JCEKS");
            File keystoreFile = new File(keystorePath);
            
            if (keystoreFile.exists()) {
                try (FileInputStream fis = new FileInputStream(keystoreFile)) {
                    keyStore.load(fis, keystorePassword);
                }
            } else {
                // 创建新的密钥库
                keyStore.load(null, keystorePassword);
                // 确保目录存在
                keystoreFile.getParentFile().mkdirs();
                try (FileOutputStream fos = new FileOutputStream(keystoreFile)) {
                    keyStore.store(fos, keystorePassword);
                }
            }
        } catch (KeyStoreException | NoSuchAlgorithmException | CertificateException | IOException e) {
            throw new RuntimeException("Failed to initialize keystore", e);
        }
    }
    
    /**
     * 加载密钥属性
     */
    private void loadKeyProperties() {
        keyProperties = new Properties();
        File propertiesFile = new File(PROPERTIES_PATH);
        
        if (propertiesFile.exists()) {
            try (FileInputStream fis = new FileInputStream(propertiesFile)) {
                keyProperties.load(fis);
            } catch (IOException e) {
                throw new RuntimeException("Failed to load key properties", e);
            }
        } else {
            // 创建新的属性文件
            try {
                propertiesFile.getParentFile().mkdirs();
                try (FileOutputStream fos = new FileOutputStream(propertiesFile)) {
                    keyProperties.store(fos, "NIFA Crypto Keys");
                }
            } catch (IOException e) {
                throw new RuntimeException("Failed to create key properties file", e);
            }
        }
    }
    
    /**
     * 保存密钥属性
     */
    private void saveKeyProperties() {
        try (FileOutputStream fos = new FileOutputStream(PROPERTIES_PATH)) {
            keyProperties.store(fos, "NIFA Crypto Keys");
        } catch (IOException e) {
            throw new RuntimeException("Failed to save key properties", e);
        }
    }
    
    /**
     * 存储SM2密钥
     * 
     * @param keyId 密钥ID
     * @param publicKeyX 公钥X坐标
     * @param publicKeyY 公钥Y坐标
     * @param privateKey 私钥
     */
    public void storeSM2Keys(String keyId, String publicKeyX, String publicKeyY, String privateKey) {
        try {
            // 存储公钥X
            SecretKey publicKeyXSecret = new SecretKeySpec(
                publicKeyX.getBytes(StandardCharsets.UTF_8), "AES");
            keyStore.setKeyEntry(
                DEFAULT_KEY_ALIAS_PREFIX + keyId + "-pubx", 
                publicKeyXSecret, 
                keystorePassword, 
                null
            );
            
            // 存储公钥Y
            SecretKey publicKeyYSecret = new SecretKeySpec(
                publicKeyY.getBytes(StandardCharsets.UTF_8), "AES");
            keyStore.setKeyEntry(
                DEFAULT_KEY_ALIAS_PREFIX + keyId + "-puby", 
                publicKeyYSecret, 
                keystorePassword, 
                null
            );
            
            // 存储私钥
            SecretKey privateKeySecret = new SecretKeySpec(
                privateKey.getBytes(StandardCharsets.UTF_8), "AES");
            keyStore.setKeyEntry(
                DEFAULT_KEY_ALIAS_PREFIX + keyId + "-priv", 
                privateKeySecret, 
                keystorePassword, 
                null
            );
            
            // 保存密钥库
            try (FileOutputStream fos = new FileOutputStream(keystorePath)) {
                keyStore.store(fos, keystorePassword);
            }
            
            // 记录密钥ID
            keyProperties.setProperty("sm2.keys", 
                keyProperties.getProperty("sm2.keys", "") + "," + keyId);
            keyProperties.setProperty("sm2.key." + keyId + ".created", 
                String.valueOf(System.currentTimeMillis()));
            saveKeyProperties();
            
        } catch (KeyStoreException | NoSuchAlgorithmException | CertificateException | IOException e) {
            throw new RuntimeException("Failed to store SM2 keys", e);
        }
    }
    
    /**
     * 获取SM2公钥X坐标
     * 
     * @param keyId 密钥ID
     * @return 公钥X坐标
     */
    public String getSM2PublicKeyX(String keyId) {
        try {
            SecretKey key = (SecretKey) keyStore.getKey(
                DEFAULT_KEY_ALIAS_PREFIX + keyId + "-pubx", 
                keystorePassword
            );
            if (key == null) {
                return null;
            }
            return new String(key.getEncoded(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SM2 public key X", e);
        }
    }
    
    /**
     * 获取SM2公钥Y坐标
     * 
     * @param keyId 密钥ID
     * @return 公钥Y坐标
     */
    public String getSM2PublicKeyY(String keyId) {
        try {
            SecretKey key = (SecretKey) keyStore.getKey(
                DEFAULT_KEY_ALIAS_PREFIX + keyId + "-puby", 
                keystorePassword
            );
            if (key == null) {
                return null;
            }
            return new String(key.getEncoded(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SM2 public key Y", e);
        }
    }
    
    /**
     * 获取SM2私钥
     * 
     * @param keyId 密钥ID
     * @return 私钥
     */
    public String getSM2PrivateKey(String keyId) {
        try {
            SecretKey key = (SecretKey) keyStore.getKey(
                DEFAULT_KEY_ALIAS_PREFIX + keyId + "-priv", 
                keystorePassword
            );
            if (key == null) {
                return null;
            }
            return new String(key.getEncoded(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get SM2 private key", e);
        }
    }
    
    /**
     * 从环境变量加载密钥
     * 
     * @param keyId 密钥ID
     * @return 是否成功
     */
    public boolean loadKeysFromEnv(String keyId) {
        String publicKeyX = System.getenv("NIFA_SM2_" + keyId.toUpperCase() + "_PUBX");
        String publicKeyY = System.getenv("NIFA_SM2_" + keyId.toUpperCase() + "_PUBY");
        String privateKey = System.getenv("NIFA_SM2_" + keyId.toUpperCase() + "_PRIV");
        
        if (publicKeyX != null && publicKeyY != null && privateKey != null) {
            storeSM2Keys(keyId, publicKeyX, publicKeyY, privateKey);
            return true;
        }
        return false;
    }
    
    /**
     * 从配置文件加载密钥
     * 
     * @param keyId 密钥ID
     * @param configPath 配置文件路径
     * @return 是否成功
     */
    public boolean loadKeysFromConfig(String keyId, String configPath) {
        try {
            Properties props = new Properties();
            try (FileInputStream fis = new FileInputStream(configPath)) {
                props.load(fis);
            }
            
            String publicKeyX = props.getProperty("nifa.crypto." + keyId + ".pub.x");
            String publicKeyY = props.getProperty("nifa.crypto." + keyId + ".pub.y");
            String privateKey = props.getProperty("nifa.crypto." + keyId + ".private");
            
            if (publicKeyX != null && publicKeyY != null && privateKey != null) {
                storeSM2Keys(keyId, publicKeyX, publicKeyY, privateKey);
                return true;
            }
            return false;
        } catch (IOException e) {
            return false;
        }
    }
}