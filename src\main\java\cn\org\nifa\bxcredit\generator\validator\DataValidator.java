package cn.org.nifa.bxcredit.generator.validator;

import java.util.List;

/**
 * 数据校验器接口
 * 
 * @param <T> 数据类型
 * <AUTHOR> Team
 */
public interface DataValidator<T> {
    
    /**
     * 校验单条数据
     * 
     * @param data 待校验数据
     * @return 校验结果
     */
    ValidationResult validate(T data);
    
    /**
     * 校验数据集合
     * 
     * @param dataList 待校验数据列表
     * @return 校验结果列表
     */
    List<ValidationResult> validateBatch(List<T> dataList);
}