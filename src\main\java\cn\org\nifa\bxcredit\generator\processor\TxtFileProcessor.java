package cn.org.nifa.bxcredit.generator.processor;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * TXT文件处理器
 * 
 * @param <T> 数据类型
 * <AUTHOR> Team
 */
public class TxtFileProcessor<T> {
    
    private static final Logger logger = LoggerFactory.getLogger(TxtFileProcessor.class);
    private static final String DELIMITER = ",";  // 字段分隔符
    
    /**
     * 将数据写入TXT文件
     * 
     * @param dataList 数据列表
     * @param filePath 文件路径
     * @param converter 数据转换函数
     */
    public void writeToFile(List<T> dataList, String filePath, Function<T, String> converter) {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(filePath), StandardCharsets.UTF_8))) {
            
            for (T data : dataList) {
                writer.write(converter.apply(data));
                writer.newLine();
            }
            
            logger.info("成功写入{}条数据到文件: {}", dataList.size(), filePath);
            
        } catch (IOException e) {
            logger.error("写入文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("写入文件失败", e);
        }
    }
    
    /**
     * 将对象转换为分隔符分隔的字符串
     * 
     * @param values 字段值数组
     * @return 分隔符分隔的字符串
     */
    public String convertToDelimitedString(Object... values) {
        return Arrays.stream(values)
                .map(value -> value == null ? "" : String.valueOf(value))
                .collect(Collectors.joining(DELIMITER));
    }
    
    /**
     * 获取字段分隔符
     * 
     * @return 分隔符
     */
    public String getDelimiter() {
        return DELIMITER;
    }
}