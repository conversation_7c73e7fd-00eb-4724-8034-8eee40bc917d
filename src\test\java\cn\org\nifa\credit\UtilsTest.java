package cn.org.nifa.credit;

import static org.testng.Assert.assertEquals;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.testng.annotations.Test;

import cn.org.nifa.bxcredit.crypto.SM2CryptoService;
import cn.org.nifa.bxcredit.utils.JsonUtils;
import cn.org.nifa.bxcredit.utils.SignatureUtils;

/**
 * 工具类测试
 * 使用现代化工具类替代旧版Utils
 */
public class UtilsTest {
	@Test
	public void testGetRandomNumber() {
		System.out.println(SignatureUtils.generateRandomNumber(10));
		for(int i=0; i<100; i++) {
			assertEquals(i, SignatureUtils.generateRandomNumber(i).length());
		}
	}
	
	@Test
	public void testMap2Json() {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("sname", "张三");  
		headerMap.put("stype", "0");  
		headerMap.put("sreason", "a");  
		headerMap.put("sno", "362201198607112613");
		
		String json = JsonUtils.toJson(headerMap);
		System.out.println(json);
	}
	
	@Test
	public void testEncryptFile() {
		try {
			SM2CryptoService cryptoService = new SM2CryptoService();
			File file = cryptoService.encryptFile(
				"tmp/MA05M6KK9201810221201120001.zip", 
				"tmp/MA05M6KK9201810221201120001.enc"
			);
			System.out.println(file);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Test
	public void testMd5() {
		String md5 = SignatureUtils.md5("asfd");
		System.out.println("md5=" + md5);
		assertEquals(md5, "d78c03d72e72b44a131d255aec3c8a11");
	}
	
	@Test
	public void testDecryptFile() {
		try {
			String string = "BMhBZtqHsZKvofCWpWAAPbcyXm5UCbPhreXlnGeI20FjUsVydSsijMaqm+Y2ab0Bq4lPJIW4U+TdoKZblI1qzhmspr0TLlM2/mUfgsYlaj4i6xzTz3rQ55kotLLo/Zj4VLkmzkBVZW/vxPnal2vDuHEpo7fr8AkuTfclVM5UlXFtPy7OWlXP8BYcsvso1pZh5fkuw7f6aVeb1mncOIvd7TsOQa7DnT89qkSKAZh32RrJ0fkRvIY35F7m316SvZJLVfOqcpaM9KOgDK4Joqi/aTSc7xkdhw+soZMWJI3nBg==";
			File decEncFile = new File("tmp/test.enc");
			FileUtils.writeByteArrayToFile(decEncFile, Base64.decodeBase64(string));
			
			SM2CryptoService cryptoService = new SM2CryptoService();
			cryptoService.decryptFile(decEncFile.getPath(), "tmp/test.txt");
		} catch (IOException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Test
	public void testSha() {
		String preparedSign = "1234";
		System.out.println(SignatureUtils.sha256(preparedSign));
	}
}
