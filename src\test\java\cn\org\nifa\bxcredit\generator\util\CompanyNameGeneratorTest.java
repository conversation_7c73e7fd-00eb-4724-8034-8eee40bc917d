package cn.org.nifa.bxcredit.generator.util;

import org.testng.Assert;
import org.testng.annotations.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * CompanyNameGenerator 工具类单元测试
 * 
 * <AUTHOR> Team
 */
public class CompanyNameGeneratorTest {
    
    /**
     * 测试基础公司名称生成
     */
    @Test
    public void testGenerateRandomCompanyName() {
        String name = CompanyNameGenerator.generateRandomCompanyName();
        
        Assert.assertNotNull(name, "生成的公司名称不能为空");
        Assert.assertTrue(name.length() > 0, "公司名称长度应大于0");
        Assert.assertTrue(CompanyNameGenerator.validateCompanyName(name), "生成的公司名称应该通过校验");
        
        System.out.println("生成的随机公司名称: " + name);
    }
    
    /**
     * 测试不同模板的公司名称生成
     */
    @Test
    public void testGenerateCompanyNameWithTemplates() {
        String prefixCoreSuffixName = CompanyNameGenerator.generateCompanyName(
            CompanyNameGenerator.NameTemplate.PREFIX_CORE_SUFFIX);
        String coreCoreSuffixName = CompanyNameGenerator.generateCompanyName(
            CompanyNameGenerator.NameTemplate.CORE_CORE_SUFFIX);
        String coreDigitSuffixName = CompanyNameGenerator.generateCompanyName(
            CompanyNameGenerator.NameTemplate.CORE_DIGIT_SUFFIX);
        String locationCoreSuffixName = CompanyNameGenerator.generateCompanyName(
            CompanyNameGenerator.NameTemplate.LOCATION_CORE_SUFFIX);
        
        Assert.assertNotNull(prefixCoreSuffixName, "前缀+核心词+后缀名称不能为空");
        Assert.assertNotNull(coreCoreSuffixName, "核心词+核心词+后缀名称不能为空");
        Assert.assertNotNull(coreDigitSuffixName, "核心词+数字+后缀名称不能为空");
        Assert.assertNotNull(locationCoreSuffixName, "地名+核心词+后缀名称不能为空");
        
        System.out.println("前缀+核心词+后缀: " + prefixCoreSuffixName);
        System.out.println("核心词+核心词+后缀: " + coreCoreSuffixName);
        System.out.println("核心词+数字+后缀: " + coreDigitSuffixName);
        System.out.println("地名+核心词+后缀: " + locationCoreSuffixName);
    }
    
    /**
     * 测试线程安全的公司名称生成
     */
    @Test
    public void testGenerateCompanyNameThreadSafe() throws InterruptedException {
        int threadCount = 10;
        int namesPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        Set<String> generatedNames = new HashSet<>();
        
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < namesPerThread; j++) {
                    String name = CompanyNameGenerator.generateCompanyNameThreadSafe(
                        CompanyNameGenerator.NameTemplate.RANDOM);
                    synchronized (generatedNames) {
                        generatedNames.add(name);
                    }
                    Assert.assertTrue(CompanyNameGenerator.validateCompanyName(name), 
                        "线程安全生成的公司名称应该通过校验");
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).join();
        executor.shutdown();
        executor.awaitTermination(10, TimeUnit.SECONDS);
        
        Assert.assertTrue(generatedNames.size() > 0, "应该生成了一些公司名称");
        System.out.println("线程安全测试生成了 " + generatedNames.size() + " 个不同的公司名称");
    }
    
    /**
     * 测试公司名称校验功能
     */
    @Test
    public void testValidateCompanyName() {
        // 测试有效名称
        String validName = CompanyNameGenerator.generateRandomCompanyName();
        Assert.assertTrue(CompanyNameGenerator.validateCompanyName(validName), 
            "生成的公司名称应该通过校验");
        
        // 测试无效名称
        Assert.assertFalse(CompanyNameGenerator.validateCompanyName(null), 
            "null名称应该校验失败");
        Assert.assertFalse(CompanyNameGenerator.validateCompanyName(""), 
            "空名称应该校验失败");
        Assert.assertFalse(CompanyNameGenerator.validateCompanyName("ABC"), 
            "过短名称应该校验失败");
        Assert.assertFalse(CompanyNameGenerator.validateCompanyName("测试公司"), 
            "无后缀名称应该校验失败");
        
        System.out.println("公司名称校验测试通过");
    }
    
    /**
     * 测试公司名称唯一性
     */
    @Test
    public void testNameUniqueness() {
        Set<String> names = new HashSet<>();
        int testCount = 10000;
        
        for (int i = 0; i < testCount; i++) {
            String name = CompanyNameGenerator.generateRandomCompanyName();
            names.add(name);
        }
        
        // 唯一性测试：生成10000个名称，不同名称的比例应该很高
        double uniqueRatio = (double) names.size() / testCount;
        Assert.assertTrue(uniqueRatio > 0.95, "名称唯一性不足，重复率过高: " + (1 - uniqueRatio));
        
        System.out.println("唯一性测试: 生成" + testCount + "个名称，其中" + names.size() + 
                         "个不同，唯一性: " + String.format("%.2f%%", uniqueRatio * 100));
    }
    
    /**
     * 测试词库数据
     */
    @Test
    public void testVocabularyData() {
        Assert.assertTrue(CompanyNameGenerator.getPrefixWordCount() > 0, 
            "前缀词库不能为空");
        Assert.assertTrue(CompanyNameGenerator.getCoreWordCount() > 0, 
            "核心词库不能为空");
        Assert.assertTrue(CompanyNameGenerator.getSuffixWordCount() > 0, 
            "后缀词库不能为空");
        Assert.assertTrue(CompanyNameGenerator.getLocationWordCount() > 0, 
            "地名词库不能为空");
        Assert.assertTrue(CompanyNameGenerator.getIndustryWordCount() > 0, 
            "行业词库不能为空");
        
        System.out.println("词库数据:");
        System.out.println("前缀词数量: " + CompanyNameGenerator.getPrefixWordCount());
        System.out.println("核心词数量: " + CompanyNameGenerator.getCoreWordCount());
        System.out.println("后缀词数量: " + CompanyNameGenerator.getSuffixWordCount());
        System.out.println("地名词数量: " + CompanyNameGenerator.getLocationWordCount());
        System.out.println("行业词数量: " + CompanyNameGenerator.getIndustryWordCount());
    }
    
    /**
     * 测试理论组合数量
     */
    @Test
    public void testTheoricalCombinations() {
        long prefixCoreSuffixCombinations = CompanyNameGenerator.calculateTheoricalCombinations(
            CompanyNameGenerator.NameTemplate.PREFIX_CORE_SUFFIX);
        long coreCoreSuffixCombinations = CompanyNameGenerator.calculateTheoricalCombinations(
            CompanyNameGenerator.NameTemplate.CORE_CORE_SUFFIX);
        long coreDigitSuffixCombinations = CompanyNameGenerator.calculateTheoricalCombinations(
            CompanyNameGenerator.NameTemplate.CORE_DIGIT_SUFFIX);
        long locationCoreSuffixCombinations = CompanyNameGenerator.calculateTheoricalCombinations(
            CompanyNameGenerator.NameTemplate.LOCATION_CORE_SUFFIX);
        long randomCombinations = CompanyNameGenerator.calculateTheoricalCombinations(
            CompanyNameGenerator.NameTemplate.RANDOM);
        
        Assert.assertTrue(prefixCoreSuffixCombinations > 0, "前缀+核心词+后缀组合数应大于0");
        Assert.assertTrue(coreCoreSuffixCombinations > 0, "核心词+核心词+后缀组合数应大于0");
        Assert.assertTrue(coreDigitSuffixCombinations > 0, "核心词+数字+后缀组合数应大于0");
        Assert.assertTrue(locationCoreSuffixCombinations > 0, "地名+核心词+后缀组合数应大于0");
        Assert.assertTrue(randomCombinations > 0, "随机模板组合数应大于0");
        
        System.out.println("理论组合数量:");
        System.out.println("前缀+核心词+后缀: " + String.format("%,d", prefixCoreSuffixCombinations));
        System.out.println("核心词+核心词+后缀: " + String.format("%,d", coreCoreSuffixCombinations));
        System.out.println("核心词+数字+后缀: " + String.format("%,d", coreDigitSuffixCombinations));
        System.out.println("地名+核心词+后缀: " + String.format("%,d", locationCoreSuffixCombinations));
        System.out.println("随机模板总组合: " + String.format("%,d", randomCombinations));
    }
    
    /**
     * 测试小批量生成到文件功能
     */
    @Test
    public void testGenerateNamesToFile() throws IOException {
        String testFilePath = "test_company_names.csv";
        int testCount = 100;
        
        try {
            CompanyNameGenerator.BatchGenerationResult result = 
                CompanyNameGenerator.generateNamesToFile(testCount, testFilePath, 
                    CompanyNameGenerator.NameTemplate.RANDOM);
            
            // 验证结果
            Assert.assertEquals(result.getUniqueGenerated(), testCount, "应该生成指定数量的唯一名称");
            Assert.assertTrue(result.getTotalTimeMs() > 0, "耗时应该大于0");
            
            // 验证文件
            Path path = Paths.get(testFilePath);
            Assert.assertTrue(Files.exists(path), "生成的文件应该存在");
            
            // 验证文件内容
            long lineCount = Files.lines(path).count();
            Assert.assertEquals(lineCount, testCount + 1, "文件行数应该是生成数量+1(标题行)");
            
            System.out.println("小批量生成测试通过，生成文件: " + testFilePath);
            System.out.println(result);
            
            // 清理测试文件
            Files.deleteIfExists(path);
        } catch (IOException e) {
            System.err.println("测试文件操作失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 测试多线程小批量生成
     */
    @Test
    public void testGenerateNamesToFileMultiThreaded() throws IOException, InterruptedException {
        String testFilePath = "test_company_names_mt.csv";
        int testCount = 1000;
        int threadCount = 4;
        int batchSize = 100;
        
        try {
            CompanyNameGenerator.BatchGenerationResult result = 
                CompanyNameGenerator.generateNamesToFileMultiThreaded(
                    testCount, testFilePath, CompanyNameGenerator.NameTemplate.RANDOM,
                    threadCount, batchSize);
            
            // 验证结果
            Assert.assertEquals(result.getUniqueGenerated(), testCount, "应该生成指定数量的唯一名称");
            Assert.assertTrue(result.getTotalTimeMs() > 0, "耗时应该大于0");
            
            // 验证文件
            Path path = Paths.get(testFilePath);
            Assert.assertTrue(Files.exists(path), "生成的文件应该存在");
            
            // 验证文件内容
            long lineCount = Files.lines(path).count();
            Assert.assertEquals(lineCount, testCount + 1, "文件行数应该是生成数量+1(标题行)");
            
            System.out.println("多线程小批量生成测试通过，生成文件: " + testFilePath);
            System.out.println(result);
            
            // 清理测试文件
            Files.deleteIfExists(path);
        } catch (IOException | InterruptedException e) {
            System.err.println("测试文件操作失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 测试估算功能
     */
    @Test
    public void testEstimations() {
        long count = 50_000_000L;
        CompanyNameGenerator.NameTemplate template = CompanyNameGenerator.NameTemplate.RANDOM;
        
        double estimatedTime = CompanyNameGenerator.estimateGenerationTime(count, template);
        long estimatedSize = CompanyNameGenerator.estimateFileSize(count, template);
        
        Assert.assertTrue(estimatedTime > 0, "估算时间应大于0");
        Assert.assertTrue(estimatedSize > 0, "估算文件大小应大于0");
        
        System.out.println("估算结果:");
        System.out.println("  生成5000万条数据估算时间: " + String.format("%.2f 秒 (%.2f 小时)", 
            estimatedTime, estimatedTime / 3600));
        System.out.println("  生成5000万条数据估算文件大小: " + String.format("%.2f MB (%.2f GB)", 
            estimatedSize / 1024.0 / 1024.0, estimatedSize / 1024.0 / 1024.0 / 1024.0));
    }
    
    /**
     * 辅助方法：检查代码格式是否有效
     */
    private boolean isValidNameFormat(String name) {
        return name != null && name.length() >= 4 && name.length() <= 50;
    }
}