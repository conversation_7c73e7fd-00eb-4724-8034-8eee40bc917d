package cn.org.nifa.bxcredit.generator.demo;

import cn.org.nifa.bxcredit.generator.util.NameGenerator;

import java.io.IOException;
import java.util.Scanner;

/**
 * 姓名生成器演示类
 * 
 * 演示如何使用 NameGenerator 进行各种类型的姓名生成，
 * 包括小批量生成和大数据量生成（如5000万条）
 * 
 * <AUTHOR> Team
 */
public class NameGeneratorDemo {
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("=== 姓名生成器演示程序 ===");
        System.out.println();
        
        while (true) {
            showMenu();
            System.out.print("请选择功能 (输入数字): ");
            
            try {
                int choice = scanner.nextInt();
                scanner.nextLine(); // 消费换行符
                
                switch (choice) {
                    case 1:
                        demonstrateBasicGeneration();
                        break;
                    case 2:
                        demonstrateThreadSafeGeneration();
                        break;
                    case 3:
                        demonstrateDataStatistics();
                        break;
                    case 4:
                        demonstrateBatchGeneration(scanner);
                        break;
                    case 5:
                        demonstrateMultiThreadedGeneration(scanner);
                        break;
                    case 6:
                        demonstratePerformanceTest();
                        break;
                    case 7:
                        demonstrateLargeScaleGeneration(scanner);
                        break;
                    case 0:
                        System.out.println("感谢使用姓名生成器演示程序！");
                        return;
                    default:
                        System.out.println("无效选择，请重新输入。");
                }
                
                System.out.println();
                System.out.println("按回车键继续...");
                scanner.nextLine();
                System.out.println();
                
            } catch (Exception e) {
                System.err.println("发生错误: " + e.getMessage());
                scanner.nextLine(); // 清除无效输入
            }
        }
    }
    
    /**
     * 显示菜单
     */
    private static void showMenu() {
        System.out.println("功能菜单:");
        System.out.println("1. 基础姓名生成演示");
        System.out.println("2. 线程安全姓名生成演示");
        System.out.println("3. 数据统计信息");
        System.out.println("4. 批量生成到文件");
        System.out.println("5. 多线程批量生成到文件");
        System.out.println("6. 性能测试");
        System.out.println("7. 大规模数据生成（5000万条）");
        System.out.println("0. 退出程序");
        System.out.println();
    }
    
    /**
     * 演示基础姓名生成功能
     */
    private static void demonstrateBasicGeneration() {
        System.out.println("=== 基础姓名生成演示 ===");
        
        System.out.println("中文两字姓名示例:");
        for (int i = 0; i < 5; i++) {
            System.out.println("  " + NameGenerator.generateChineseNameTwoChar());
        }
        
        System.out.println("\n中文三字姓名示例:");
        for (int i = 0; i < 5; i++) {
            System.out.println("  " + NameGenerator.generateChineseNameThreeChar());
        }
        
        System.out.println("\n中文复姓四字姓名示例:");
        for (int i = 0; i < 5; i++) {
            System.out.println("  " + NameGenerator.generateChineseNameCompound());
        }
        
        System.out.println("\n英文姓名示例:");
        for (int i = 0; i < 5; i++) {
            System.out.println("  " + NameGenerator.generateEnglishName());
        }
        
        System.out.println("\n随机中文姓名示例:");
        for (int i = 0; i < 5; i++) {
            System.out.println("  " + NameGenerator.generateRandomChineseName());
        }
    }
    
    /**
     * 演示线程安全姓名生成功能
     */
    private static void demonstrateThreadSafeGeneration() {
        System.out.println("=== 线程安全姓名生成演示 ===");
        
        System.out.println("使用线程安全方法生成姓名:");
        System.out.println("中文两字姓名: " + NameGenerator.generateChineseNameTwoCharThreadSafe());
        System.out.println("中文三字姓名: " + NameGenerator.generateChineseNameThreeCharThreadSafe());
        System.out.println("中文复姓四字姓名: " + NameGenerator.generateChineseNameCompoundThreadSafe());
        System.out.println("英文姓名: " + NameGenerator.generateEnglishNameThreadSafe());
        System.out.println("随机中文姓名: " + NameGenerator.generateRandomChineseNameThreadSafe());
    }
    
    /**
     * 演示数据统计信息
     */
    private static void demonstrateDataStatistics() {
        System.out.println("=== 数据统计信息 ===");
        
        System.out.println("中文单姓数量: " + NameGenerator.getChineseSingleSurnameCount());
        System.out.println("中文复姓数量: " + NameGenerator.getChineseCompoundSurnameCount());
        System.out.println("中文单字名数量: " + NameGenerator.getChineseSingleNameCount());
        System.out.println("中文双字名数量: " + NameGenerator.getChineseDoubleNameCount());
        System.out.println("英文名数量: " + NameGenerator.getEnglishFirstNameCount());
        System.out.println("英文姓数量: " + NameGenerator.getEnglishLastNameCount());
        
        // 计算理论组合数
        long twoCharCombinations = (long) NameGenerator.getChineseSingleSurnameCount() * 
                                  NameGenerator.getChineseSingleNameCount();
        long threeCharCombinations = (long) NameGenerator.getChineseSingleSurnameCount() * 
                                    NameGenerator.getChineseDoubleNameCount();
        long compoundCombinations = (long) NameGenerator.getChineseCompoundSurnameCount() * 
                                   NameGenerator.getChineseDoubleNameCount();
        long englishCombinations = (long) NameGenerator.getEnglishFirstNameCount() * 
                                  NameGenerator.getEnglishLastNameCount();
        
        System.out.println("\n理论组合数:");
        System.out.println("中文两字姓名: " + twoCharCombinations + " 种");
        System.out.println("中文三字姓名: " + threeCharCombinations + " 种");
        System.out.println("中文复姓四字姓名: " + compoundCombinations + " 种");
        System.out.println("英文姓名: " + englishCombinations + " 种");
        System.out.println("中文姓名总计: " + (twoCharCombinations + threeCharCombinations + compoundCombinations) + " 种");
    }
    
    /**
     * 演示批量生成到文件
     */
    private static void demonstrateBatchGeneration(Scanner scanner) throws IOException {
        System.out.println("=== 批量生成到文件演示 ===");
        
        System.out.print("请输入生成数量: ");
        long count = scanner.nextLong();
        scanner.nextLine();
        
        System.out.println("请选择姓名类型:");
        System.out.println("1. 中文两字姓名");
        System.out.println("2. 中文三字姓名");
        System.out.println("3. 中文复姓四字姓名");
        System.out.println("4. 英文姓名");
        System.out.println("5. 随机中文姓名");
        System.out.print("请选择 (1-5): ");
        
        int typeChoice = scanner.nextInt();
        scanner.nextLine();
        
        NameGenerator.NameType nameType = getNameTypeByChoice(typeChoice);
        if (nameType == null) {
            System.out.println("无效的姓名类型选择");
            return;
        }
        
        System.out.print("请输入输出文件路径 (例: names.txt): ");
        String filePath = scanner.nextLine();
        
        System.out.println("开始生成...");
        long startTime = System.currentTimeMillis();
        
        NameGenerator.generateNamesToFile(count, nameType, filePath);
        
        long endTime = System.currentTimeMillis();
        System.out.println("生成完成，耗时: " + (endTime - startTime) + "ms");
        System.out.println("平均速度: " + (count * 1000L / (endTime - startTime)) + " 个/秒");
    }
    
    /**
     * 演示多线程批量生成到文件
     */
    private static void demonstrateMultiThreadedGeneration(Scanner scanner) throws IOException, InterruptedException {
        System.out.println("=== 多线程批量生成到文件演示 ===");
        
        System.out.print("请输入生成数量: ");
        long count = scanner.nextLong();
        scanner.nextLine();
        
        System.out.println("请选择姓名类型:");
        System.out.println("1. 中文两字姓名");
        System.out.println("2. 中文三字姓名");
        System.out.println("3. 中文复姓四字姓名");
        System.out.println("4. 英文姓名");
        System.out.println("5. 随机中文姓名");
        System.out.print("请选择 (1-5): ");
        
        int typeChoice = scanner.nextInt();
        scanner.nextLine();
        
        NameGenerator.NameType nameType = getNameTypeByChoice(typeChoice);
        if (nameType == null) {
            System.out.println("无效的姓名类型选择");
            return;
        }
        
        System.out.print("请输入线程数量 (建议: " + Runtime.getRuntime().availableProcessors() + "): ");
        int threadCount = scanner.nextInt();
        scanner.nextLine();
        
        System.out.print("请输入输出文件路径 (例: names_mt.txt): ");
        String filePath = scanner.nextLine();
        
        System.out.println("开始多线程生成...");
        long startTime = System.currentTimeMillis();
        
        NameGenerator.generateNamesToFileMultiThreaded(count, nameType, filePath, threadCount);
        
        long endTime = System.currentTimeMillis();
        System.out.println("多线程生成完成，耗时: " + (endTime - startTime) + "ms");
        System.out.println("平均速度: " + (count * 1000L / (endTime - startTime)) + " 个/秒");
    }
    
    /**
     * 演示性能测试
     */
    private static void demonstratePerformanceTest() {
        System.out.println("=== 性能测试演示 ===");
        
        int[] testCounts = {1000, 10000, 100000};
        
        for (int testCount : testCounts) {
            System.out.println("\n测试数量: " + testCount);
            
            // 单线程性能测试
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < testCount; i++) {
                NameGenerator.generateChineseNameTwoChar();
            }
            long singleThreadTime = System.currentTimeMillis() - startTime;
            
            // 线程安全方法性能测试
            startTime = System.currentTimeMillis();
            for (int i = 0; i < testCount; i++) {
                NameGenerator.generateChineseNameTwoCharThreadSafe();
            }
            long threadSafeTime = System.currentTimeMillis() - startTime;
            
            System.out.println("单线程方法耗时: " + singleThreadTime + "ms, 速度: " + 
                             (testCount * 1000L / Math.max(singleThreadTime, 1)) + " 个/秒");
            System.out.println("线程安全方法耗时: " + threadSafeTime + "ms, 速度: " + 
                             (testCount * 1000L / Math.max(threadSafeTime, 1)) + " 个/秒");
        }
    }
    
    /**
     * 演示大规模数据生成（5000万条）
     */
    private static void demonstrateLargeScaleGeneration(Scanner scanner) throws IOException, InterruptedException {
        System.out.println("=== 大规模数据生成演示（5000万条） ===");
        System.out.println("警告: 这将生成5000万条姓名数据，可能需要较长时间和大量磁盘空间！");
        System.out.print("确认继续？(y/N): ");
        
        String confirm = scanner.nextLine();
        if (!confirm.equalsIgnoreCase("y") && !confirm.equalsIgnoreCase("yes")) {
            System.out.println("已取消大规模数据生成");
            return;
        }
        
        long count = 50_000_000L; // 5000万
        int threadCount = Runtime.getRuntime().availableProcessors();
        String filePath = "large_scale_names.txt";
        
        System.out.println("开始生成5000万条中文两字姓名...");
        System.out.println("使用线程数: " + threadCount);
        System.out.println("输出文件: " + filePath);
        
        long startTime = System.currentTimeMillis();
        
        NameGenerator.generateNamesToFileMultiThreaded(count, 
            NameGenerator.NameType.CHINESE_TWO_CHAR, filePath, threadCount);
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        System.out.println("\n=== 大规模生成完成 ===");
        System.out.println("总耗时: " + totalTime + "ms (" + (totalTime / 1000.0) + "秒)");
        System.out.println("平均速度: " + (count * 1000L / totalTime) + " 个/秒");
        System.out.println("文件大小约: " + (count * 3 / 1024 / 1024) + "MB"); // 每个中文姓名约3字节
    }
    
    /**
     * 根据选择获取姓名类型
     */
    private static NameGenerator.NameType getNameTypeByChoice(int choice) {
        switch (choice) {
            case 1:
                return NameGenerator.NameType.CHINESE_TWO_CHAR;
            case 2:
                return NameGenerator.NameType.CHINESE_THREE_CHAR;
            case 3:
                return NameGenerator.NameType.CHINESE_COMPOUND;
            case 4:
                return NameGenerator.NameType.ENGLISH;
            case 5:
                return NameGenerator.NameType.CHINESE_RANDOM;
            default:
                return null;
        }
    }
}
