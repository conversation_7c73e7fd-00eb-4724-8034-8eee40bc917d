package cn.org.nifa.bxcredit.generator.util;

import org.testng.Assert;
import org.testng.annotations.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * NameGenerator 工具类单元测试
 * 
 * <AUTHOR> Team
 */
public class NameGeneratorTest {
    
    /**
     * 测试中文两字姓名生成
     */
    @Test
    public void testGenerateChineseNameTwoChar() {
        String name = NameGenerator.generateChineseNameTwoChar();
        
        Assert.assertNotNull(name, "生成的姓名不能为空");
        Assert.assertEquals(name.length(), 2, "两字姓名长度应为2");
        Assert.assertTrue(isChineseCharacters(name), "应该只包含中文字符");
        
        System.out.println("生成的两字姓名: " + name);
    }
    
    /**
     * 测试中文三字姓名生成
     */
    @Test
    public void testGenerateChineseNameThreeChar() {
        String name = NameGenerator.generateChineseNameThreeChar();
        
        Assert.assertNotNull(name, "生成的姓名不能为空");
        Assert.assertEquals(name.length(), 3, "三字姓名长度应为3");
        Assert.assertTrue(isChineseCharacters(name), "应该只包含中文字符");
        
        System.out.println("生成的三字姓名: " + name);
    }
    
    /**
     * 测试中文复姓四字姓名生成
     */
    @Test
    public void testGenerateChineseNameCompound() {
        String name = NameGenerator.generateChineseNameCompound();
        
        Assert.assertNotNull(name, "生成的姓名不能为空");
        Assert.assertEquals(name.length(), 4, "复姓四字姓名长度应为4");
        Assert.assertTrue(isChineseCharacters(name), "应该只包含中文字符");
        
        System.out.println("生成的复姓四字姓名: " + name);
    }
    
    /**
     * 测试英文姓名生成
     */
    @Test
    public void testGenerateEnglishName() {
        String name = NameGenerator.generateEnglishName();
        
        Assert.assertNotNull(name, "生成的姓名不能为空");
        Assert.assertTrue(name.contains(" "), "英文姓名应包含空格");
        Assert.assertTrue(isEnglishName(name), "应该只包含英文字符和空格");
        
        String[] parts = name.split(" ");
        Assert.assertEquals(parts.length, 2, "英文姓名应包含名和姓两部分");
        
        System.out.println("生成的英文姓名: " + name);
    }
    
    /**
     * 测试随机中文姓名生成
     */
    @Test
    public void testGenerateRandomChineseName() {
        String name = NameGenerator.generateRandomChineseName();
        
        Assert.assertNotNull(name, "生成的姓名不能为空");
        Assert.assertTrue(name.length() >= 2 && name.length() <= 4, "中文姓名长度应在2-4之间");
        Assert.assertTrue(isChineseCharacters(name), "应该只包含中文字符");
        
        System.out.println("生成的随机中文姓名: " + name);
    }
    
    /**
     * 测试线程安全的姓名生成方法
     */
    @Test
    public void testThreadSafeNameGeneration() throws InterruptedException {
        int threadCount = 10;
        int namesPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        Set<String> generatedNames = new HashSet<>();
        
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < namesPerThread; j++) {
                    String name = NameGenerator.generateChineseNameTwoCharThreadSafe();
                    synchronized (generatedNames) {
                        generatedNames.add(name);
                    }
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).join();
        executor.shutdown();
        executor.awaitTermination(10, TimeUnit.SECONDS);
        
        Assert.assertTrue(generatedNames.size() > 0, "应该生成了一些姓名");
        System.out.println("线程安全测试生成了 " + generatedNames.size() + " 个不同的姓名");
    }
    
    /**
     * 测试数据统计方法
     */
    @Test
    public void testDataCounts() {
        Assert.assertTrue(NameGenerator.getChineseSingleSurnameCount() > 0, "中文单姓数量应大于0");
        Assert.assertTrue(NameGenerator.getChineseCompoundSurnameCount() > 0, "中文复姓数量应大于0");
        Assert.assertTrue(NameGenerator.getChineseSingleNameCount() > 0, "中文单字名数量应大于0");
        Assert.assertTrue(NameGenerator.getChineseDoubleNameCount() > 0, "中文双字名数量应大于0");
        Assert.assertTrue(NameGenerator.getEnglishFirstNameCount() > 0, "英文名数量应大于0");
        Assert.assertTrue(NameGenerator.getEnglishLastNameCount() > 0, "英文姓数量应大于0");
        
        System.out.println("数据统计:");
        System.out.println("中文单姓: " + NameGenerator.getChineseSingleSurnameCount());
        System.out.println("中文复姓: " + NameGenerator.getChineseCompoundSurnameCount());
        System.out.println("中文单字名: " + NameGenerator.getChineseSingleNameCount());
        System.out.println("中文双字名: " + NameGenerator.getChineseDoubleNameCount());
        System.out.println("英文名: " + NameGenerator.getEnglishFirstNameCount());
        System.out.println("英文姓: " + NameGenerator.getEnglishLastNameCount());
    }
    
    /**
     * 测试姓名随机性
     */
    @Test
    public void testNameRandomness() {
        Set<String> names = new HashSet<>();
        int testCount = 1000;
        
        for (int i = 0; i < testCount; i++) {
            names.add(NameGenerator.generateChineseNameTwoChar());
        }
        
        // 随机性测试：生成1000个姓名，不同姓名的比例应该较高
        double uniqueRatio = (double) names.size() / testCount;
        Assert.assertTrue(uniqueRatio > 0.8, "姓名随机性不足，重复率过高: " + (1 - uniqueRatio));
        
        System.out.println("随机性测试: 生成" + testCount + "个姓名，其中" + names.size() + "个不同，随机性: " + 
                String.format("%.2f%%", uniqueRatio * 100));
    }
    
    /**
     * 测试批量生成到文件功能
     */
    @Test
    public void testGenerateNamesToFile() throws IOException {
        String testFilePath = "test_names.txt";
        int testCount = 100;
        
        try {
            NameGenerator.generateNamesToFile(testCount, NameGenerator.NameType.CHINESE_TWO_CHAR, testFilePath);
            
            Path filePath = Paths.get(testFilePath);
            Assert.assertTrue(Files.exists(filePath), "输出文件应该存在");
            
            List<String> lines = Files.readAllLines(filePath);
            Assert.assertEquals(lines.size(), testCount, "文件行数应该等于生成数量");
            
            // 验证每行都是有效的两字中文姓名
            for (String line : lines) {
                Assert.assertEquals(line.length(), 2, "每行应该是两字姓名");
                Assert.assertTrue(isChineseCharacters(line), "每行应该只包含中文字符");
            }
            
            System.out.println("批量生成测试成功，生成了 " + lines.size() + " 个姓名到文件");
            
        } finally {
            // 清理测试文件
            Files.deleteIfExists(Paths.get(testFilePath));
        }
    }
    
    /**
     * 测试多线程批量生成到文件功能
     */
    @Test
    public void testGenerateNamesToFileMultiThreaded() throws IOException, InterruptedException {
        String testFilePath = "test_names_mt.txt";
        int testCount = 1000;
        int threadCount = 4;
        
        try {
            NameGenerator.generateNamesToFileMultiThreaded(testCount, 
                NameGenerator.NameType.CHINESE_THREE_CHAR, testFilePath, threadCount);
            
            Path filePath = Paths.get(testFilePath);
            Assert.assertTrue(Files.exists(filePath), "输出文件应该存在");
            
            List<String> lines = Files.readAllLines(filePath);
            Assert.assertEquals(lines.size(), testCount, "文件行数应该等于生成数量");
            
            // 验证每行都是有效的三字中文姓名
            for (String line : lines) {
                Assert.assertEquals(line.length(), 3, "每行应该是三字姓名");
                Assert.assertTrue(isChineseCharacters(line), "每行应该只包含中文字符");
            }
            
            System.out.println("多线程批量生成测试成功，生成了 " + lines.size() + " 个姓名到文件");
            
        } finally {
            // 清理测试文件
            Files.deleteIfExists(Paths.get(testFilePath));
        }
    }
    
    /**
     * 性能测试
     */
    @Test
    public void testPerformance() {
        int testCount = 10000;
        
        // 测试单线程性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < testCount; i++) {
            NameGenerator.generateChineseNameTwoChar();
        }
        long singleThreadTime = System.currentTimeMillis() - startTime;
        
        // 测试多线程性能
        startTime = System.currentTimeMillis();
        ExecutorService executor = Executors.newFixedThreadPool(4);
        CompletableFuture<Void>[] futures = new CompletableFuture[4];
        
        for (int i = 0; i < 4; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < testCount / 4; j++) {
                    NameGenerator.generateChineseNameTwoCharThreadSafe();
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).join();
        executor.shutdown();
        long multiThreadTime = System.currentTimeMillis() - startTime;
        
        System.out.println("性能测试结果:");
        System.out.println("单线程生成 " + testCount + " 个姓名耗时: " + singleThreadTime + "ms");
        System.out.println("多线程生成 " + testCount + " 个姓名耗时: " + multiThreadTime + "ms");
        System.out.println("单线程速度: " + (testCount * 1000L / singleThreadTime) + " 个/秒");
        System.out.println("多线程速度: " + (testCount * 1000L / multiThreadTime) + " 个/秒");
    }
    
    /**
     * 检查字符串是否只包含中文字符
     */
    private boolean isChineseCharacters(String str) {
        return Pattern.matches("[\u4e00-\u9fa5]+", str);
    }
    
    /**
     * 检查字符串是否是有效的英文姓名格式
     */
    private boolean isEnglishName(String str) {
        return Pattern.matches("[A-Za-z]+ [A-Za-z]+", str);
    }
}
