package cn.org.nifa.bxcredit.api;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.notNullValue;

import java.util.Map;

import org.testng.annotations.Test;

import cn.org.nifa.bxcredit.config.ConfigManager;
import cn.org.nifa.bxcredit.constants.TestDataConstants;
import cn.org.nifa.bxcredit.utils.JsonUtils;
import cn.org.nifa.bxcredit.utils.RequestBuilder;
import cn.org.nifa.bxcredit.utils.SignatureUtils;

/**
 * 个人借贷共享查询接口测试
 * 
 * <AUTHOR> Team
 */
public class InfoQueryApiTest extends BaseApiTest {
    
    @Test(description = "个人借贷共享查询接口-明文测试")
    public void testPersonalCreditQuery() {
        printTestInfo("Personal Credit Query Test", "测试个人借贷共享查询接口");
        
        // 生成随机码
        String randomCode = SignatureUtils.generateRandomNumber(10);
        
        // 构建请求体
        Map<String, String> body = RequestBuilder.buildInfoQueryBody(
                TestDataConstants.PersonalInfo.TEST_NAME,
                TestDataConstants.PersonalInfo.TEST_TYPE,
                TestDataConstants.PersonalInfo.TEST_REASON_B,
                TestDataConstants.PersonalInfo.TEST_ID_NUMBER
        );
        
        // 生成签名
        String signature = SignatureUtils.generateInfoQuerySignature(
                ConfigManager.getOrgCode(),
                randomCode,
                TestDataConstants.PersonalInfo.TEST_NAME,
                TestDataConstants.PersonalInfo.TEST_TYPE,
                TestDataConstants.PersonalInfo.TEST_REASON_B,
                TestDataConstants.PersonalInfo.TEST_ID_NUMBER,
                ConfigManager.getApiKey()
        );
        
        // 构建请求头
        Map<String, String> headers = RequestBuilder.buildCommonHeaders(
                ConfigManager.getOrgCode(),
                randomCode,
                signature
        );
        
        // 发送请求并验证响应
        given()
                .spec(requestSpec)
                .headers(headers)
                .body(JsonUtils.toJson(body))
        .when()
                .post(ConfigManager.getInfoTestUri())
        .then()
                .statusCode(200)
                .body("msgCode", notNullValue())
                .body("msgContent", notNullValue())
                .log().all();
    }
    
    @Test(description = "个人借贷共享查询接口-MD5密文测试")
    public void testPersonalCreditQueryMd5() {
        printTestInfo("Personal Credit Query MD5 Test", "测试个人借贷共享查询接口MD5密文");
        
        // 生成随机码
        String randomCode = SignatureUtils.generateRandomNumber(10);
        
        // 构建MD5值
        String nameMd5 = SignatureUtils.md5(SignatureUtils.md5(TestDataConstants.PersonalInfo.TEST_NAME) + "password");
        String idMd5 = SignatureUtils.md5(SignatureUtils.md5(TestDataConstants.PersonalInfo.TEST_ID_NUMBER) + "password");
        String md5Value = nameMd5 + TestDataConstants.PersonalInfo.TEST_TYPE + idMd5;
        
        // 构建请求体
        Map<String, String> body = RequestBuilder.buildMd5QueryBody(
                md5Value,
                TestDataConstants.PersonalInfo.TEST_REASON_B
        );
        
        // 生成签名
        String signature = SignatureUtils.generateMd5QuerySignature(
                ConfigManager.getOrgCode(),
                randomCode,
                md5Value,
                TestDataConstants.PersonalInfo.TEST_REASON_B,
                ConfigManager.getApiKey()
        );
        
        // 构建请求头
        Map<String, String> headers = RequestBuilder.buildCommonHeaders(
                ConfigManager.getOrgCode(),
                randomCode,
                signature
        );
        
        // 发送请求并验证响应
        given()
                .spec(requestSpec)
                .headers(headers)
                .body(JsonUtils.toJson(body))
        .when()
                .post(ConfigManager.getInfoMd5Uri())
        .then()
                .statusCode(200)
                .body("msgCode", notNullValue())
                .body("msgContent", notNullValue())
                .log().all();
    }
}