package cn.org.nifa.bxcredit.generator.generator;

import java.util.List;

/**
 * 数据生成器接口
 * 
 * @param <T> 数据类型
 * <AUTHOR> Team
 */
public interface DataGenerator<T> {
    
    /**
     * 生成单条正向测试数据
     * 
     * @return 正向测试数据
     */
    T generatePositiveData();
    
    /**
     * 生成多条正向测试数据
     * 
     * @param count 数据条数
     * @return 正向测试数据列表
     */
    List<T> generatePositiveDataBatch(int count);
    
    /**
     * 生成违反特定规则的反向测试数据
     * 
     * @param ruleViolation 违反的规则类型
     * @return 反向测试数据
     */
    T generateNegativeData(String ruleViolation);
    
    /**
     * 生成多条反向测试数据，覆盖所有规则
     * 
     * @return 反向测试数据列表
     */
    List<T> generateAllNegativeData();
}