package cn.org.nifa.bxcredit.generator;

import java.io.File;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.org.nifa.bxcredit.generator.generator.CreditGuaranteeInsuranceDataGenerator;
import cn.org.nifa.bxcredit.generator.model.CreditGuaranteeInsuranceData;
import cn.org.nifa.bxcredit.generator.processor.CreditGuaranteeInsuranceFileProcessor;
import cn.org.nifa.bxcredit.generator.util.TestDataManager;
import cn.org.nifa.bxcredit.generator.validator.CreditGuaranteeInsuranceRuleValidator;
import cn.org.nifa.bxcredit.generator.validator.ValidationResult;

/**
 * 信用保证保险测试数据生成命令行工具
 * 支持通过命令行参数配置生成数据的数量和类型
 * 
 * <AUTHOR> Team
 */
public class CreditGuaranteeInsuranceDataGeneratorCLI {

    private static final Logger logger = LoggerFactory.getLogger(CreditGuaranteeInsuranceDataGeneratorCLI.class);

    public static void main(String[] args) {
        logger.info("=== 信用保证保险测试数据生成命令行工具启动 ===");

        try {
            // 解析命令行参数
            CommandLineOptions options = parseCommandLineArgs(args);
            
            // 生成测试数据
            generateTestData(options);
            
            logger.info("=== 信用保证保险测试数据生成完成 ===");
        } catch (Exception e) {
            logger.error("信用保证保险测试数据生成失败: {}", e.getMessage(), e);
            printUsage();
        }
    }

    /**
     * 解析命令行参数
     */
    private static CommandLineOptions parseCommandLineArgs(String[] args) {
        CommandLineOptions options = new CommandLineOptions();
        
        // 设置默认值
        options.underwritingCount = 10;
        options.compensationCount = 10;
        options.recoveryCount = 10;
        options.includeNegativeData = false;
        options.outputDir = null; // 使用默认输出目录
        
        // 解析参数
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];
            
            switch (arg) {
                case "-u":
                case "--underwriting":
                    if (i + 1 < args.length) {
                        options.underwritingCount = Integer.parseInt(args[++i]);
                    }
                    break;
                    
                case "-c":
                case "--compensation":
                    if (i + 1 < args.length) {
                        options.compensationCount = Integer.parseInt(args[++i]);
                    }
                    break;
                    
                case "-r":
                case "--recovery":
                    if (i + 1 < args.length) {
                        options.recoveryCount = Integer.parseInt(args[++i]);
                    }
                    break;
                    
                case "-n":
                case "--negative":
                    options.includeNegativeData = true;
                    break;
                    
                case "-o":
                case "--output":
                    if (i + 1 < args.length) {
                        options.outputDir = args[++i];
                    }
                    break;
                    
                case "-h":
                case "--help":
                    printUsage();
                    System.exit(0);
                    break;
                    
                default:
                    logger.warn("未知参数: {}", arg);
                    break;
            }
        }
        
        return options;
    }

    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.out.println("使用方法: java -cp bxcredit.jar cn.org.nifa.bxcredit.generator.CreditGuaranteeInsuranceDataGeneratorCLI [options]");
        System.out.println("选项:");
        System.out.println("  -u, --underwriting <count>   生成的承保信息数量 (默认: 10)");
        System.out.println("  -c, --compensation <count>   生成的代偿信息数量 (默认: 10)");
        System.out.println("  -r, --recovery <count>       生成的追偿信息数量 (默认: 10)");
        System.out.println("  -n, --negative               包含反向测试数据");
        System.out.println("  -o, --output <dir>           指定输出目录");
        System.out.println("  -h, --help                   显示帮助信息");
        System.out.println("示例:");
        System.out.println("  java -cp bxcredit.jar cn.org.nifa.bxcredit.generator.CreditGuaranteeInsuranceDataGeneratorCLI -u 20 -c 15 -r 10 -n");
    }

    /**
     * 生成测试数据
     */
    private static void generateTestData(CommandLineOptions options) {
        logger.info("开始生成信用保证保险测试数据...");
        logger.info("配置: 承保信息 {} 条, 代偿信息 {} 条, 追偿信息 {} 条, 包含反向测试数据: {}",
                options.underwritingCount, options.compensationCount, options.recoveryCount, options.includeNegativeData);

        // 1. 数据生成
        CreditGuaranteeInsuranceDataGenerator generator = new CreditGuaranteeInsuranceDataGenerator();
        List<CreditGuaranteeInsuranceData> dataList = generateDataBatch(generator, options);
        logger.info("生成信用保证保险数据总量: {} 条", dataList.size());

        // 2. 数据校验
        CreditGuaranteeInsuranceRuleValidator validator = new CreditGuaranteeInsuranceRuleValidator();
        List<ValidationResult> results = validator.validateBatch(dataList);

        long validCount = results.stream().filter(ValidationResult::isValid).count();
        logger.info("信用保证保险数据校验结果 - 总数: {}, 有效: {}, 无效: {}",
                results.size(), validCount, results.size() - validCount);

        // 3. 创建输出目录
        String outputDir = getOutputDirectory(options);
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs();
        }

        // 4. 文件处理
        CreditGuaranteeInsuranceFileProcessor fileProcessor = new CreditGuaranteeInsuranceFileProcessor();
        processFiles(fileProcessor, dataList, outputDir);

        // 5. 生成数据报告
        generateDataReport(dataList, results, outputDir);

        // 6. 归档旧数据
        if (options.outputDir == null) {
            TestDataManager.archiveOldData("test-data/generated/insurance", 10);
        }

        logger.info("信用保证保险测试数据生成完成，输出目录: {}", outputDir);
    }

    /**
     * 生成数据批次
     */
    private static List<CreditGuaranteeInsuranceData> generateDataBatch(
            CreditGuaranteeInsuranceDataGenerator generator, CommandLineOptions options) {
        
        // 生成承保信息
        List<CreditGuaranteeInsuranceData> underwritingData = 
                generator.generateDataBatch("UNDERWRITING", options.underwritingCount);
        
        // 生成代偿信息
        List<CreditGuaranteeInsuranceData> compensationData = 
                generator.generateDataBatch("COMPENSATION", options.compensationCount);
        
        // 生成追偿信息
        List<CreditGuaranteeInsuranceData> recoveryData = 
                generator.generateDataBatch("RECOVERY", options.recoveryCount);
        
        // 合并数据
        List<CreditGuaranteeInsuranceData> allData = new java.util.ArrayList<>();
        allData.addAll(underwritingData);
        allData.addAll(compensationData);
        allData.addAll(recoveryData);
        
        // 添加反向测试数据
        if (options.includeNegativeData) {
            List<CreditGuaranteeInsuranceData> negativeData = generator.generateAllNegativeData();
            allData.addAll(negativeData);
        }
        
        return allData;
    }

    /**
     * 获取输出目录
     */
    private static String getOutputDirectory(CommandLineOptions options) {
        if (options.outputDir != null) {
            return options.outputDir;
        } else {
            String timestampDir = TestDataManager.generateTimestampedFileName("insurance", "");
            return TestDataManager.getInsuranceDataPath(timestampDir);
        }
    }

    /**
     * 处理文件
     */
    private static void processFiles(CreditGuaranteeInsuranceFileProcessor fileProcessor,
            List<CreditGuaranteeInsuranceData> dataList, String outputDir) {
        
        // 处理承保信息
        CreditGuaranteeInsuranceFileProcessor.ProcessResult underwritingResult = 
                fileProcessor.processUnderwritingData(dataList, outputDir);
        logProcessResult("承保信息", underwritingResult);

        // 处理代偿信息
        CreditGuaranteeInsuranceFileProcessor.ProcessResult compensationResult = 
                fileProcessor.processCompensationData(dataList, outputDir);
        logProcessResult("代偿信息", compensationResult);

        // 处理追偿信息
        CreditGuaranteeInsuranceFileProcessor.ProcessResult recoveryResult = 
                fileProcessor.processRecoveryData(dataList, outputDir);
        logProcessResult("追偿信息", recoveryResult);
    }

    /**
     * 记录处理结果
     */
    private static void logProcessResult(String dataType, CreditGuaranteeInsuranceFileProcessor.ProcessResult result) {
        if (result.isSuccess()) {
            logger.info("{}处理成功:", dataType);
            if (result.getTxtFilePath() != null) {
                logger.info("- TXT文件: {}", result.getTxtFilePath());
            }
            if (result.getZipFilePath() != null) {
                logger.info("- ZIP文件: {}", result.getZipFilePath());
            }
            if (result.getEncFilePath() != null) {
                logger.info("- ENC文件: {}", result.getEncFilePath());
            } else {
                logger.warn("- ENC文件: 加密失败，仅生成ZIP文件");
            }
        } else {
            logger.error("{}处理失败: {}", dataType, result.getMessage());
        }
    }

    /**
     * 生成数据报告
     */
    private static void generateDataReport(List<CreditGuaranteeInsuranceData> dataList,
            List<ValidationResult> results, String outputDir) {
        try {
            StringBuilder report = new StringBuilder();
            report.append("信用保证保险测试数据生成报告\n");
            // 添加50个等号作为分隔线
            StringBuilder separator = new StringBuilder();
            for (int i = 0; i < 50; i++) {
                separator.append("=");
            }
            report.append(separator).append("\n\n");

            // 数据统计
            long underwritingCount = dataList.stream().filter(d -> "UNDERWRITING".equals(d.getDataType())).count();
            long compensationCount = dataList.stream().filter(d -> "COMPENSATION".equals(d.getDataType())).count();
            long recoveryCount = dataList.stream().filter(d -> "RECOVERY".equals(d.getDataType())).count();

            report.append("数据统计:\n");
            report.append("- 承保信息: ").append(underwritingCount).append(" 条\n");
            report.append("- 代偿信息: ").append(compensationCount).append(" 条\n");
            report.append("- 追偿信息: ").append(recoveryCount).append(" 条\n");
            report.append("- 总计: ").append(dataList.size()).append(" 条\n\n");

            // 校验统计
            long validCount = results.stream().filter(ValidationResult::isValid).count();
            report.append("校验统计:\n");
            report.append("- 有效数据: ").append(validCount).append(" 条\n");
            report.append("- 无效数据: ").append(results.size() - validCount).append(" 条\n\n");

            // 错误统计
            report.append("错误统计:\n");
            results.stream()
                    .filter(r -> !r.isValid())
                    .flatMap(r -> r.getErrors().stream())
                    .distinct()
                    .forEach(error -> report.append("- ").append(error).append("\n"));

            // 写入报告文件
            String reportPath = outputDir + File.separator + "data_generation_report.txt";
            java.nio.file.Files.write(java.nio.file.Paths.get(reportPath), report.toString().getBytes());

            logger.info("数据生成报告已保存: {}", reportPath);

        } catch (Exception e) {
            logger.error("生成数据报告失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 命令行选项类
     */
    private static class CommandLineOptions {
        int underwritingCount;
        int compensationCount;
        int recoveryCount;
        boolean includeNegativeData;
        String outputDir;
    }
}