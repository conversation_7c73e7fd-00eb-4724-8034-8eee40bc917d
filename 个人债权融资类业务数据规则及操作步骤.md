数据字段及规则
以下是您提供的所有数据字段及其详细规则：

1. 证件类型
长度: 1

枚举值:

0-身份证

1-户口簿

2-护照

3-军官证

4-士兵证

5-港澳居民来往内地通行证

6-台湾同胞来往内地通行证

7-临时身份证

8-外国人居留证

9-警官证

A-香港身份证

B-澳门身份证

C-台湾身份证

X-其他证件

2. 证件号码
长度: 18

规则:

18位身份证号码的最后一位只能为0-9的数字或大写字母“X”。

若数据报送机构在提取证件号码时，发现18位身份证号码的最后一位为小写字母“x”，应该用大写字母“X”替代。

3. 业务发生机构
长度: 9 或者 18

规则:

组织机构代码长度为9。

社会信用代码长度为18。

4. 业务号
长度: BN + 长度等于30位的随机数字

规则: 业务号由固定前缀“BN”和30位随机数字组成。

5. 业务类型
长度: 1

枚举值:

2-分期还款

3-一次性还款

4-循环

5-不定期还款

6. 业务种类
长度: 2

枚举值:

11-网络借贷

21-网络小贷

31-消费金融

41-赊销

51-小额贷款

71-助贷

99-其他

7. 开户日期
长度: 8

格式: YYYYMMDD

注意:

当业务类型为2、3、5时，该数据项为该笔业务的首次放款日期。

当业务类型为4时，此数据项是指首次授信日期。

8. 到期日期
长度: 8

格式: YYYYMMDD

注意:

当业务类型为2、3、5时，此数据项为到期日期。

贷款发生缩期、展期时，本数据项要做相应调整。

当业务类型为4时，用20991231填充。

9. 授信额度
长度: 10

规则:

当业务类型为4时，该数据项为信用额度。

当业务类型为2、3、5时，该数据项为“贷款合同金额”。

10. 业务发生日期
长度: 8

格式: YYYYMMDD

规则:

对于新开立的债权融资业务，此数据项填“开户日期”。

对于结清/销户的债权融资业务，此数据项填写“结清日期/销卡日期”。

对于非开立及非结清/销户的债权融资业务：

1）到期前，本月需要还款的，填本期“应还款日”；本月无需还款的，填写本月的最后一天。

2）到期后，填写本月的最后一天。

11. 余额
长度: 10

规则:

长度大于0小于等于10的数字。

当业务类型为2、3、5时，该数据项为借款本金余额。

当业务类型为4时，该数据项为已使用额度。

12. 当前逾期总额
长度: 10

规则:

长度大于0小于等于10的数字。

当业务类型为2、3、5时，该数据项为当前应还未还的借款本息之和。

当业务类型为4时，该数据项为当前应还未还的金额以及由此产生的利息和费用。

13. 本月还款状态
长度: 1

枚举值:

*-当月不需还款（当月不需要还款，且没有以前月份的拖欠需要归还）

N-正常（借款人已经按时归还该月应还款金额的全部，且该账户没有逾期）。提前归还该月应还款金额的全部（但尚未结清），也归入“N-正常”。

1-表示逾期1-30天

2-表示逾期31-60天

3-表示逾期61-90天

4-表示逾期91-120天

5-表示逾期121-150天

6-表示逾期151-180天

7-表示逾期180天以上

C-结清或销户（包括借款人的该笔贷款全部还清，贷款余额为0。包括正常结清、提前结清）。

D-担保人代还（表示借款人的该笔贷款已由担保人代还，包括担保人按期代还与担保人代还部分贷款）。

I-保险代还（表示借款人的该笔贷款已由保险代还，包括保险按期代还与保险代还部分贷款）。

Z-以资抵债（表示借款人的该笔贷款已通过以资抵债的方式进行还款。仅指以资抵债部分）。

G-结束（除结清外的，其他任何形态的终止账户）。

注: 逾期指借款人在借贷合同约定到期未足额归还本金或利息。

1）分期付款的，一个账户只要有应还未足额还的情况，账户状态就算逾期，而非整个账户到期后未还清才算逾期；

2）一次还本分期付息的，利息未足额还也算逾期；

3）平台代偿也算逾期；

4）有宽限期的，宽限期内足额还款的不算逾期；

5）发生展期的，展期到期日前足额还款不算逾期。

其他校验规则
以下是适用于多个数据项的校验规则：

“开户日期” ≤ “文件生成时间”中的日期，“开户日期” ≤ “到期日期”，“开户日期” ≤ “业务发生日期”。

“本月还款状态”为“C-结清或销户”时，“余额”必须为零。

“本月还款状态”为“1-7”时，“当前逾期总额”应大于零。

当业务类型非4-循环时，“授信额度”须大于等于“余额”。

业务发生日期应小于等于文件报送日期/文件生成日期。

“本月还款状态”为*，N，C时，“当前逾期总额”必须为0。

业务类型不在数据字典列表中。

业务种类不在数据字典列表中。

证件类型不在数据字典列表中。

“本月还款状态”不在数据字典列表中。

对于一个借款项目，本次报送时，之前报送过的记录“本月还款状态”不能为“C-结清或销户”；“之前报送过的记录”是指业务发生日期小于本次报送的业务发生日期的记录。

当记录为漏报记录时，则“本月还款状态”不能为“C-结清或销户”。

业务发生机构（组织机构代码）不在数据字典列表中。

当记录为漏报记录时，记录最新业务发生日期减漏报记录业务发生日期需小于12个月。

上述数据全部为必填。

操作步骤及技术栈
技术栈建议
编程语言: Java

日期处理: java.time 包 (Java 8及以上) 或 java.util.Date/java.util.Calendar (旧版本)。推荐使用 java.time 进行日期计算和格式化，以确保准确性和易用性。

文件操作: java.io 或 java.nio.file 包进行文件创建、写入。

压缩: java.util.zip 包进行ZIP文件压缩。

加密: SM2加密算法需要引入第三方库，例如 Bouncy Castle (org.bouncycastle)。Bouncy Castle 提供了广泛的密码学API，包括国密算法SM2的支持。

操作步骤
您需要按照以下步骤来生成测试数据：

步骤 1: 数据生成与校验

定义数据结构: 在Java中，为每个数据字段定义相应的变量类型（例如，String 用于文本，int 或 long 用于数字，LocalDate 用于日期）。可以考虑创建一个POJO（Plain Old Java Object）类来表示单条业务记录。

生成正向测试数据:

根据每个字段的长度、枚举值和特定规则，生成符合要求的数据。

确保生成的数据满足所有“其他校验规则”中的正向条件。例如，“开户日期” ≤ “到期日期”，“余额”为零时“本月还款状态”为“C”等。

对于涉及日期计算的规则（如“业务发生日期”），使用Java的日期API进行精确计算。

对于涉及唯一性的规则（如“业务号”和ZIP文件名），实现逻辑以确保生成的值不重复。

生成反向测试数据:

针对每条规则，至少生成一条违反该规则的反向测试数据。

例如：

证件号码: 生成最后一位为小写“x”的证件号码，或长度不为18的证件号码。

业务类型/种类/证件类型/本月还款状态: 生成不在枚举值列表中的值。

日期校验: 生成“开户日期” > “到期日期”的数据。

余额与还款状态: 生成“本月还款状态”为“C”但“余额”不为零的数据。

授信额度与余额: 当业务类型非4时，生成“授信额度” < “余额”的数据。

逾期总额与还款状态: 生成“本月还款状态”为“1-7”但“当前逾期总额”为零的数据。

必填项: 生成某个字段为空的数据（如果允许空字符串，则生成空字符串；如果必须有值，则生成缺失字段的记录）。

历史记录校验: 模拟一个借款项目，生成先前的记录“本月还款状态”为“C”，但本次报送的业务发生日期晚于之前记录的业务发生日期，且该项目再次出现非C状态的记录（此场景需模拟多条记录）。

漏报记录校验: 模拟漏报场景，确保业务发生日期与最新业务发生日期之差大于12个月。

数据写入TXT文件:

将生成的数据按照每条记录一行，字段之间以特定分隔符（例如，制表符或逗号，如果文档未指定，则需要自行定义或根据实际情况推断）连接，写入名为 121EXPORTTRADEINFO.txt 的文本文件。

确保文件编码符合要求（例如UTF-8）。

步骤 2: 文件压缩

创建ZIP文件: 使用Java的 java.util.zip 包，将 121EXPORTTRADEINFO.txt 文件压缩成一个ZIP文件。

ZIP文件命名:

文件名长度必须为27位。

文件名第1～9位必须为上报机构的组织机构代码（9位）或者社会信用代码（18位）的第9-17位（必须与报送用户所属机构的组织机构代码一致）。

文件名第10～17位为有效年月，且年份大于1990，年月小于或等于当前年月，格式为“YYYYMMDD”。

文件名第22～23位只能填写固定的值12。

文件名第24～27位只能填写“0～9”的数字。

文件名必须和本机构以前所有上报文件的文件名不重复，即文件名唯一。

重要: 实现逻辑来确保文件名的唯一性，例如，可以维护一个已生成文件名的列表或使用时间戳和随机数组合。

步骤 3: 文件加密

SM2加密: 使用引入的第三方加密库（如Bouncy Castle），对生成的ZIP文件进行SM2加密。

您需要获取SM2加密所需的公钥和私钥（通常由接收方提供或根据规范生成）。

加密过程将ZIP文件的二进制内容作为输入，输出加密后的二进制内容。

生成ENC文件: 将加密后的二进制内容写入一个新文件，文件名与ZIP文件名一致，但扩展名为 .enc。